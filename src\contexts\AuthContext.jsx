import { useState, createContext, useEffect } from "react";
import axios from "../configs/axios";
import {
  addAccessToken,
  getAccessToken,
  removeAccessToken,
} from "../utils/local-storage";

export const AuthContext = createContext();

export default function AuthContextProvider({ children }) {
  const [initialLoading, setInitialLoading] = useState(true);
  const [authUser, setAuthUser] = useState(null);

  const getUser = async () => {
    try {
      const response = await axios.get("/auth/me");
      if (response.status === 200) {
        setAuthUser(response.data.user);
        localStorage.setItem("user", JSON.stringify(response.data.user));
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      setAuthUser(null);
    }
  };

  useEffect(() => {
    const savedUser = localStorage.getItem("user");
    
    if (savedUser) {
      setAuthUser(JSON.parse(savedUser));
    } else if (getAccessToken()) {
      getUser();
    }

    setInitialLoading(false);
  }, []);

  const login = async (credential) => {
    try {
      const res = await axios.post("/auth/login", credential);
      
      // บันทึก accessToken และ user ลง local storage
      addAccessToken(res.data.accessToken);
      localStorage.setItem("user", JSON.stringify(res.data.user));

      setAuthUser(res.data.user);
    } catch (error) {
      console.error("Error logging in:", error);
      throw error;
    }
  };

  const register = async (registerInputObj) => {
    const res = await axios.post("/auth/register", registerInputObj);
    
    // บันทึก accessToken และ user ลง local storage
    addAccessToken(res.data.accessToken);
    localStorage.setItem("user", JSON.stringify(res.data.user));

    setAuthUser(res.data.user);
  };

  const logout = () => {
    removeAccessToken(); // ลบ accessToken ออกจาก localStorage หรือ cookies
    localStorage.removeItem("user"); // ลบข้อมูล user ออกจาก localStorage
    localStorage.removeItem("someOtherItem"); // ลบข้อมูลอื่นๆ ถ้ามี
    setAuthUser(null); // รีเซ็ตสถานะของผู้ใช้
  };

  return (
    <AuthContext.Provider
      value={{
        login,
        authUser,
        register,
        logout,
        initialLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
