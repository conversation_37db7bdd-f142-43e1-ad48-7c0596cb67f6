import React, { useState, useEffect, useRef } from "react";
import html2canvas from "html2canvas";

export default function RD_Pl_Pg_None() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const pageHeight = 800;
  const reportRefs = useRef([]);
  const [pages, setPages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [planlistData, setPlanlistData] = useState([]);
  const [selectedSearchType, setSelectedSearchType] = useState(null);
  const [delivery1, setDelivery1] = useState(null);
  const [delivery2, setDelivery2] = useState(null);
  const [delivery3, setDelivery3] = useState(null);
  const [viewSchedule, setViewSchedule] = useState(null);
  const [planTarget, setPlanTarget] = useState(null);
  const [format, setFormat] = useState(null);
  const [changePage, setChangePage] = useState(null);
  const [target, setTarget] = useState(null);
  const [markDays, setMarkDays] = useState(null);
  const [checkboxGroupState, setCheckboxGroupState] = useState(null);
  const initialState = {
    S_St_Pd_Grp_CD: "",
    S_St_Pd_Grp_Abb: "",
    S_Ed_Pd_Grp_CD: "",
    S_Ed_Pd_Grp_Abb: "",
    S_No_Pd_Grp_CD1: "",
    S_No_Pd_Grp_CD2: "",
    S_No_Pd_Grp_Abb1: "",
    S_No_Pd_Grp_Abb2: "",
    S_Coating_CD1: "",
    S_Coating_CD2: "",
    S_Coating_CD3: "",
    Coating_Name1: "",
    Coating_Name2: "",
    Coating_Name3: "",
    S_No_Coating_CD: "",
    S_No_Coating_Name: "",
    S_Customer_CD1: "",
    S_Customer_CD2: "",
    S_Customer_CD3: "",
    S_No_Customer_CD: "",
    S_Customer_Name1: "",
    S_Customer_Name2: "",
    S_Customer_Name3: "",
    S_Customer_Abb1: "",
    S_Customer_Abb2: "",
    S_Customer_Abb3: "",
    S_No_Customer_Abb: "",
    S_Item1_CD: "",
    S_Item1_Name: "",
    S_Product_Name: "",
    S_Sl_Person_CD: "",
    S_Sl_Person_Name: "",
    S_Od_Ctl_Person_CD: "",
    S_Od_Ctl_Person_Name: "",
    S_Pl_Reg_Person_CD: "",
    S_Pl_Reg_Person_Name: "",
  };

  const [reportState, setReportState] = useState(initialState);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const {
        status,
        data,
        selectedSearchType,
        delivery1,
        delivery2,
        delivery3,
        viewSchedule,
        Plan_Target,
        format,
        changePage,
        target,
        markDays,
        checkboxGroupState,
      } = event.data;

      // console.log("Received message data:", event.data);

      if (status === 200 && data?.status === "success") {
        setPlanlistData(data);
        setSelectedSearchType(selectedSearchType || null);
        setDelivery1(delivery1 || null);
        setDelivery2(delivery2 || null);
        setDelivery3(delivery3 || null);
        setViewSchedule(viewSchedule || null);
        setPlanTarget(Plan_Target || null);
        setFormat(format || null);
        setChangePage(changePage || null);
        setTarget(target || null);
        setMarkDays(markDays || null);
        setCheckboxGroupState(checkboxGroupState || null);

        const updated = {};
        for (const key in initialState) {
          updated[key] = event.data[key] || "";
        }
        setReportState(updated);
      } else {
        console.error("Failed to receive data.");
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  useEffect(() => {
    setIsLoading(true);

    if (planlistData?.data?.length > 0) {
      const checkRefsReady = setInterval(() => {
        if (reportRefs.current.length > 0) {
          clearInterval(checkRefsReady);
          processPDF();
        }
      }, 100);
    }
  }, [planlistData]);

  useEffect(() => {
    if (planlistData?.data?.length > 0) {
      // First, sort ALL data with multi-level sorting criteria
      const sortedData = [...planlistData.data].sort((a, b) => {
        // Primary sort: Product_Delivery date (oldest to newest)
        const dateA = a.Product_Delivery
          ? new Date(a.Product_Delivery)
          : new Date(0);
        const dateB = b.Product_Delivery
          ? new Date(b.Product_Delivery)
          : new Date(0);
        const productDeliveryComparison = dateA - dateB;

        if (productDeliveryComparison !== 0) {
          return productDeliveryComparison;
        }

        // Secondary sort: Request_Delivery date (oldest to newest)
        const reqDateA = a.Request_Delivery
          ? new Date(a.Request_Delivery)
          : new Date(0);
        const reqDateB = b.Request_Delivery
          ? new Date(b.Request_Delivery)
          : new Date(0);
        const requestDeliveryComparison = reqDateA - reqDateB;

        if (requestDeliveryComparison !== 0) {
          return requestDeliveryComparison;
        }

        // Tertiary sort: Confirm_Delivery date (oldest to newest)
        const confDateA = a.Confirm_Delivery
          ? new Date(a.Confirm_Delivery)
          : new Date(0);
        const confDateB = b.Confirm_Delivery
          ? new Date(b.Confirm_Delivery)
          : new Date(0);
        return confDateA - confDateB;
      });

      // Then split the sorted data into pages based on height constraints
      let pagesArray = [];
      let currentPageData = [];
      let currentHeight = 0;

      sortedData.forEach((item) => {
        currentPageData.push(item);

        const latestPPDPosition = item.Latest_PPD_Position || 24;
        const extraHeight =
          latestPPDPosition > 24 ? (latestPPDPosition - 24) * 10 : 0;

        const rowHeight = 50 + extraHeight;

        currentHeight += rowHeight;

        if (currentHeight >= pageHeight) {
          pagesArray.push(currentPageData);
          currentPageData = [item];
          currentHeight = rowHeight;
        }
      });

      if (currentPageData.length > 0) {
        pagesArray.push(currentPageData);
      }

      setPages(pagesArray);
    }
  }, [planlistData]);

  const processPDF = async () => {
    const scale = 2;
    const maxItemsPerPDF = 250;
    const chunkSize = 30;
    const totalChunks = Math.ceil(reportRefs.current.length / chunkSize);

    let images = [];
    let pdfUrls = [];
    let currentPdfIndex = 0;

    const processChunk = async (chunkIndex) => {
      if (chunkIndex >= totalChunks) {
        if (images.length > 0) {
          await generatePDF(images, currentPdfIndex);
        }
        return;
      }

      const chunk = reportRefs.current.slice(
        chunkIndex * chunkSize,
        (chunkIndex + 1) * chunkSize
      );

      const chunkImages = await Promise.all(
        chunk.map(async (ref) => {
          if (!ref) return null;
          const canvas = await html2canvas(ref, {
            scale,
            backgroundColor: null,
          });
          const imgData = canvas.toDataURL("image/png");
          const imgWidth = 297;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          return { imgData, imgWidth, imgHeight };
        })
      );

      images = [...images, ...chunkImages.filter(Boolean)];

      if (images.length >= maxItemsPerPDF || chunkIndex === totalChunks - 1) {
        await generatePDF(images, currentPdfIndex);
        images = [];
        currentPdfIndex++;
      }

      setTimeout(() => processChunk(chunkIndex + 1), 0);
    };

    const generatePDF = (images, currentPdfIndex) => {
      return new Promise((resolve) => {
        const worker = new Worker(
          new URL("./workers/saveAsPdfWorker.js", import.meta.url)
        );

        worker.onmessage = (e) => {
          const { pdfBlob } = e.data;
          const pdfURL = URL.createObjectURL(pdfBlob);

          pdfUrls.push({ pdfURL, index: currentPdfIndex });
          window.open(pdfURL, "_blank");

          worker.terminate();
          resolve();

          setIsLoading(false);
          window.close();
        };

        worker.postMessage({ images });
      });
    };

    processChunk(0);
  };

  // Function Sum Target_Amount
  const totalTargetAmount = Array.isArray(planlistData?.data)
    ? planlistData.data
        .reduce((sum, item) => sum + (item.Target_Amount || 0), 0)
        .toFixed(2)
    : "0.00";

  return (
    <>
      {isLoading && (
        <div className="fixed inset-0 flex justify-center items-center bg-gray-700 bg-opacity-50 z-50 transition-opacity duration-300 ease-out">
          <div className="bg-white p-6 rounded-xl shadow-xl w-72 flex flex-col justify-center items-center space-y-4">
            <div className="text-lg font-semibold text-gray-900">
              Generating PDF...
            </div>
            <div className="flex justify-center items-center space-x-2">
              <div className="w-12 h-12 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
            </div>
            <div className="text-sm text-gray-500">
              Please wait while we prepare your report.
            </div>
          </div>
        </div>
      )}

      {pages.map((pageData, pageIndex) => {
        // Data is already sorted globally, no need to sort per page
        const sortedPageData = pageData;
        return (
          <div
            key={pageIndex}
            ref={(el) => (reportRefs.current[pageIndex] = el)}
            className="w-[1535px] mx-auto"
          >
            <div className="flex flex-col overflow-x-auto flex-grow pt-3 px-0.5">
              <div className="overflow-x-auto max-h-[100vh] max-w-full">
                <table className="min-w-full bg-white">
                  <thead>
                    {pageIndex === 0 && (
                      <>
                        <tr>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Product_Grp:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center text-sm border border-black min-w-[60px] max-w-[60px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_St_Pd_Grp_CD || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center text-sm border border-black min-w-[90px] max-w-[90px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_St_Pd_Grp_Abb || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Coating_CD1:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[30px] max-w-[30px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Coating_CD1 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.Coating_Name1 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_CD1:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 justify-start">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_CD1 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[110px] max-w-[110px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Abb1 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_Name1:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[113px] max-w-[113px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Name1 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="py-1 font-bold text-center text-md min-w-[200px]"
                            rowSpan="4"
                          >
                            <div
                              className="flex flex-col"
                              style={{ marginLeft: "-80px" }}
                            >
                              <span
                                className="text-[#000080]"
                                style={{ transform: "translateY(-8px)" }}
                              >
                                Target Amount Sum:
                              </span>
                              <span
                                className="text-md font-bold"
                                style={{ transform: "translateY(-8px)" }}
                              >
                                {parseFloat(totalTargetAmount).toLocaleString(
                                  undefined,
                                  {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                  }
                                )}
                              </span>
                            </div>
                          </td>
                        </tr>

                        <tr>
                          <td
                            className="px-2 text-right text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              ~:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center text-sm border border-black min-w-[60px] max-w-[60px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Ed_Pd_Grp_CD || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center text-sm border border-black min-w-[90px] max-w-[90px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Ed_Pd_Grp_Abb || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Coating_CD2:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[30px] max-w-[30px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Coating_CD2 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.Coating_Name2 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_CD2:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 justify-start">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_CD2 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[110px] max-w-[110px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Abb2 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_Name2:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[113px] max-w-[113px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Name2 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>

                        <tr>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Not_pd_Grp_CD1:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[60px] max-w-[60px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Pd_Grp_CD1 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[90px] max-w-[90px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Pd_Grp_Abb1 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Coating_CD3:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[30px] max-w-[30px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Coating_CD3 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.Coating_Name3 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_CD3:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 justify-start">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_CD3 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[110px] max-w-[110px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Abb3 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Customer_Name3:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[113px] max-w-[113px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Customer_Name3 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>

                        <tr>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Not_pd_Grp_CD2:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 justify-start">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[60px] max-w-[60px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Pd_Grp_CD2 || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[90px] max-w-[90px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Pd_Grp_Abb2 || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Not_Coating_CD:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[30px] max-w-[30px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Coating_CD || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Coating_Name || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-right text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span style={{ transform: "translateY(-8px)" }}>
                              Not_Customer_CD:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 justify-start">
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Customer_CD || ""}
                                </span>
                              </div>
                              <div className="flex-1 py-0.5 text-center border border-black min-w-[110px] max-w-[110px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_No_Customer_Abb || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td
                            className="px-2 text-left text-[12px] text-[#000080]"
                            colSpan="1"
                          >
                            <span
                              style={{ transform: "translateY(-8px)" }}
                              className="pl-6"
                            >
                              Item1:
                            </span>
                          </td>
                          <td colSpan="1">
                            <div className="flex gap-0 ">
                              <div className="py-0.5 text-center border border-black min-w-[80px] max-w-[80px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Item1_CD || ""}
                                </span>
                              </div>
                              <div className="py-0.5 text-center border border-black min-w-[80px] max-w-[80px] h-7">
                                <span style={{ transform: "translateY(-8px)" }}>
                                  {reportState.S_Item1_Name || ""}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </>
                    )}

                    <tr>
                      <td colSpan="9">
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "center",
                            position: "relative",
                          }}
                        >
                          <span
                            className="text-center text-[20px] text-[#000080] font-bold"
                            style={{ transform: "translateY(-8px)" }}
                          >
                            Progress List
                          </span>
                          <span
                            className="text-right text-[16px] text-[#000080] font-bold"
                            style={{
                              position: "absolute",
                              left: "63%",
                              top: "15%",
                              transform: "translateY(-8px)",
                            }}
                          >
                            Create_date:
                          </span>
                          <span
                            className="text-right text-[16px] text-black font-bold"
                            style={{
                              position: "absolute",
                              left: "72%",
                              top: "15%",
                              transform: "translateY(-8px)",
                            }}
                          >
                            {new Date().toLocaleDateString("th-TH")}{" "}
                            {new Date().toLocaleTimeString("th-TH")}
                          </span>

                          <span
                            className="text-right text-[16px] text-[#000080] font-bold"
                            style={{
                              position: "absolute",
                              left: "88%",
                              top: "15%",
                              transform: "translateY(-8px)",
                            }}
                          >
                            Page:
                          </span>
                          <span
                            className="text-right text-[16px] text-black font-bold"
                            style={{
                              position: "absolute",
                              left: "93%",
                              top: "15%",
                              transform: "translateY(-8px)",
                            }}
                          >
                            {pageIndex + 1}/{pages.length}
                          </span>
                        </div>
                      </td>
                    </tr>

                    <tr>
                      <td
                        className="px-2 text-right text-[12px] text-[#000080]"
                        colSpan="1"
                      >
                        <span style={{ transform: "translateY(-8px)" }}>
                          Product_Name:
                        </span>
                      </td>
                      <td colSpan="1">
                        <div className="py-0.5 text-center border border-black min-w-[150px] max-w-[150px] h-7">
                          <span style={{ transform: "translateY(-8px)" }}>
                            {reportState.S_Product_Name || ""}
                          </span>
                        </div>
                      </td>
                      <td
                        className="px-2 text-right text-[12px] text-[#000080]"
                        colSpan="1"
                      >
                        <span style={{ transform: "translateY(-8px)" }}>
                          Sales_Person:
                        </span>
                      </td>
                      <td colSpan="1">
                        <div className="flex gap-0">
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[30px] max-w-[30px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Sl_Person_CD || ""}
                            </span>
                          </div>
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Sl_Person_Name || ""}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td
                        className="px-2 text-right text-[12px] text-[#000080]"
                        colSpan="1"
                      >
                        <span style={{ transform: "translateY(-8px)" }}>
                          Order_Ctl_Person:
                        </span>
                      </td>
                      <td colSpan="1">
                        <div className="flex gap-0 justify-start">
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[70px] max-w-[70px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Od_Ctl_Person_CD || ""}
                            </span>
                          </div>
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[110px] max-w-[110px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Od_Ctl_Person_Name || ""}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td
                        className="pr-3 text-right text-[12px] text-[#000080]"
                        colSpan="1"
                      >
                        <span style={{ transform: "translateY(-8px)" }}>
                          Plan_Reg_Person:
                        </span>
                      </td>
                      <td colSpan="1">
                        <div className="flex gap-0">
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[80px] max-w-[80px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Pl_Reg_Person_CD || ""}
                            </span>
                          </div>
                          <div className="flex-1 py-0.5 text-center border border-black min-w-[80px] max-w-[80px] h-7">
                            <span style={{ transform: "translateY(-8px)" }}>
                              {reportState.S_Pl_Reg_Person_Name || ""}
                            </span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </thead>
                </table>
              </div>

              <div className="container min-w-full mt-3 relative">
                <div className="">
                  <table
                    className="bg-white border border-solid border-[#000080]"
                    style={{
                      transform: "scale(0.845)",
                      transformOrigin: "top left",
                      transition: "transform 0.2s ease-in-out",
                    }}
                  >
                    <thead className="bg-white">
                      <tr className="font-bold text-xs">
                        <th
                          className="py-1 border border-dashed border-[#000080] text-[12px] text-center min-w-[44px]"
                          rowSpan="2"
                        >
                          <span
                            className="text-black block"
                            style={{ transform: "translateY(-9px)" }}
                          >
                            Pro
                          </span>
                          <span
                            className="text-[#000080] block"
                            style={{ transform: "translateY(-5px)" }}
                          >
                            Deli
                          </span>
                        </th>
                        <th
                          className="py-1 border border-dashed border-[#000080] text-[12px] text-center min-w-[44px]"
                          rowSpan="2"
                        >
                          <span
                            className="text-black block"
                            style={{ transform: "translateY(-9px)" }}
                          >
                            Con
                          </span>
                          <span
                            className="text-[#000080] block"
                            style={{ transform: "translateY(-5px)" }}
                          >
                            Deli
                          </span>
                        </th>
                        <th
                          className="py-1 border border-dashed border-[#000080] text-[12px] text-center"
                          rowSpan="2"
                        >
                          <span
                            className="text-black block"
                            style={{ transform: "translateY(-9px)" }}
                          >
                            Req
                          </span>
                          <span
                            className="text-[#000080] block"
                            style={{ transform: "translateY(-5px)" }}
                          >
                            Deli
                          </span>
                        </th>
                        <th
                          className=" border border-dashed border-[#000080] text-[#000080] text-[12px] text-center min-w-[100px]"
                          rowSpan="2"
                        >
                          <span style={{ transform: "translateY(5px)" }}>
                            Order_Parts_No
                          </span>
                        </th>
                        <th
                          className="py-1 px-1 border border-dashed border-[#000080] text-[#000080] text-[12px] text-center relative min-w-[225px]"
                          rowSpan="3"
                        >
                          <div className="absolute top-0 right-12 text-right text-[12px] border border-dashed border-[#000080] px-1 text-xs">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Sales
                            </span>
                          </div>
                          <div className="absolute top-0 right-6 text-[12px] border border-dashed border-[#000080] px-1 text-xs">
                            <span style={{ transform: "translateY(-5px)" }}>
                              CT
                            </span>
                          </div>
                          <div className="absolute top-0 right-0 text-right text-[12px] border border-dashed border-[#000080] px-1 text-xs">
                            <span style={{ transform: "translateY(-5px)" }}>
                              PO
                            </span>
                          </div>
                          <span style={{ transform: "translateY(5px)" }}>
                            Customer/Production_Name
                          </span>
                        </th>
                        <th
                          className="py-1 border border-dashed border-[#000080] text-[#000080] text-[12px] text-center"
                          rowSpan="2"
                        >
                          <span
                            className="block"
                            style={{ transform: "translateY(-9px)" }}
                          >
                            PT_Name
                          </span>
                          <span
                            className="block"
                            style={{ transform: "translateY(-5px)" }}
                          >
                            Material
                          </span>
                        </th>
                        <th
                          className="py-1 px-1 border border-dashed border-[#000080] text-[#000080] text-[12px] text-center"
                          rowSpan="2"
                        >
                          <span
                            className="block"
                            style={{ transform: "translateY(-9px)" }}
                          >
                            Plan
                          </span>
                          <span
                            className="block"
                            style={{ transform: "translateY(-5px)" }}
                          >
                            Qty
                          </span>
                        </th>
                        <th
                          className="py-1 px-1 border border-dashed border-[#000080] text-[#000080] text-[12px] text-center"
                          colSpan="24"
                        >
                          <span style={{ transform: "translateY(-5px)" }}>
                            Process
                          </span>
                        </th>
                        <th
                          className="py-1 px-1 border border-dashed border-[#000080] text-[#000080] text-[12px] text-center"
                          rowSpan="2"
                        >
                          <span style={{ transform: "translateY(-10px)" }}>
                            Pt_Note
                          </span>
                          <span
                            className="block"
                            style={{ transform: "translateY(-3px)" }}
                          >
                            /Info
                          </span>
                        </th>
                      </tr>
                      <tr className=" font-bold border-b border border-[#000080] text-[#000080] text-xs">
                        {[...Array(24)].map((_, infoidx) => (
                          <th
                            key={infoidx}
                            className="py-1 px-1 border border-dashed border-[#000080] text-center"
                          >
                            <span style={{ transform: "translateY(-5px)" }}>
                              {infoidx + 1}
                            </span>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {sortedPageData.map((item, rowIndex) => {
                        if (!item) return null;

                        // Find the previous item's Product_Delivery date for comparison
                        const prevItem = sortedPageData[rowIndex - 1];
                        const currentProductDelivery = item.Product_Delivery
                          ? new Date(item.Product_Delivery)
                          : null;

                        const prevProductDelivery =
                          prevItem && prevItem.Product_Delivery
                            ? new Date(prevItem.Product_Delivery)
                            : null;

                        // Format date for display
                        const formatDate = (date) => {
                          if (!date) return "";
                          return date.toLocaleDateString("en-GB", {
                            day: "2-digit",
                            month: "2-digit",
                          });
                        };

                        // Only show date if it's different from the previous row
                        const showProductDelivery =
                          !prevProductDelivery ||
                          !currentProductDelivery ||
                          currentProductDelivery.getTime() !==
                            prevProductDelivery.getTime();

                        const isResultDataView =
                          checkboxGroupState?.Result_Data_View;

                        const rowBackgroundColor =
                          checkboxGroupState?.Color_View === true
                            ? rowIndex % 2 === 0
                              ? "bg-[#cffff9]"
                              : "bg-white"
                            : "";

                        const latestPPDPosition =
                          item.Latest_PPD_Position || 24;
                        const extraValues =
                          latestPPDPosition > 24 ? latestPPDPosition - 24 : 0;
                        const result = {
                          planQty:
                            item.Pt_Split === true
                              ? `${item.Pt_Qty}/${item.Quantity}`
                              : item.Pt_Qty,
                          spare:
                            item.Pt_Spare_Qty - item.Pt_NG_Qty === 0
                              ? ""
                              : item.Pt_Spare_Qty - item.Pt_NG_Qty > 0
                              ? `+${item.Pt_Spare_Qty - item.Pt_NG_Qty}`
                              : `${item.Pt_Spare_Qty - item.Pt_NG_Qty}`,
                        };
                        return (
                          <React.Fragment key={`fragment-${rowIndex}`}>
                            <tr
                              key={`row-${rowIndex}`}
                              className={`text-xs ${rowBackgroundColor}`}
                            >
                              <td className="py-0.5 border-t border-l border-r border-dashed border-[#78a7da] text-[12px] font-bold text-center min-w-[44px]">
                                {showProductDelivery ? (
                                  <div className="bg-yellow-500 inline-block p-1 rounded">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    >
                                      {formatDate(currentProductDelivery)}
                                    </span>
                                  </div>
                                ) : (
                                  <span></span>
                                )}
                              </td>
                              <td className="py-0.5 px-1 border-t border-l border-r border-dashed border-[#78a7da] text-[12px] font-bold text-center">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {item.Request_Delivery
                                    ? new Date(
                                        item.Request_Delivery
                                      ).toLocaleDateString("en-GB", {
                                        day: "2-digit",
                                        month: "2-digit",
                                      })
                                    : ""}
                                </span>
                              </td>
                              <td className="py-0.5 px-1 border-t border-l border-r border-dashed border-[#78a7da] text-[12px] font-bold text-center">
                                <div className="bg-yellow-500 inline-block p-1 rounded">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {item.Confirm_Delivery
                                      ? new Date(
                                          item.Confirm_Delivery
                                        ).toLocaleDateString("en-GB", {
                                          day: "2-digit",
                                          month: "2-digit",
                                        })
                                      : ""}
                                  </span>
                                </div>
                                <span
                                  className="block mt-0.5"
                                  style={{ transform: "translateY(-5px)" }}
                                >
                                  {item.WorkG_Symbol || ""}
                                </span>
                              </td>
                              <td className="py-0.5 border-t border-l border-r border-dashed border-[#78a7da] text-center relative">
                                <div className="absolute top-2 left-0 w-full">
                                  <span
                                    className="text-center font-bold text-[12px]"
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {item.Order_No || ""} -{" "}
                                    {item.Parts_No || ""}
                                  </span>
                                </div>
                                <div className="absolute bottom-0 right-0 flex justify-between w-full">
                                  <span
                                    className="text-left px-1 text-[12px]"
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {item.Target_Symbol || ""}
                                  </span>
                                  <span className="text-right px-1 text-[12px] font-bold">
                                    {(Number(item.Target_Amount) || 0).toFixed(
                                      2
                                    )}
                                  </span>
                                </div>
                              </td>
                              <td className="py-0.5 px-0.5 border-t border-l border-r border-dashed border-[#78a7da] text-center font-bold relative min-w-[225px] max-w-[225px]">
                                <div className="absolute top-0 left-0 w-full flex justify-between">
                                  <span
                                    className="text-left pl-0.5 mt-0.5 text-[12px] overflow-hidden whitespace-nowrap"
                                    style={{
                                      transform: "translateY(-4px)",
                                      maxWidth: "130px",
                                    }}
                                  >
                                    {item.Customer_Abb || ""}
                                  </span>
                                  <div className="flex">
                                    <div className="text-center border border-dashed border-[#78a7da] min-w-[40px] max-w-[40px]">
                                      <span
                                        className="text-[10px] font-bold overflow-hidden whitespace-nowrap block mt-1.5"
                                        style={{
                                          transform: "translateY(-5px)",
                                        }}
                                      >
                                        {item.Sales || ""}
                                      </span>
                                    </div>
                                    <div className="text-center border border-dashed border-[#78a7da] min-w-[20px] max-w-[42px]">
                                      <span
                                        className="text-[10px] font-bold overflow-hidden whitespace-nowrap block mt-1.5"
                                        style={{
                                          transform: "translateY(-5px)",
                                        }}
                                      >
                                        {item.Coating || ""}
                                      </span>
                                    </div>
                                    <div className="text-center border border-dashed border-[#78a7da] min-w-[30px] max-w-[30px]">
                                      <span
                                        className="text-[10px] font-bold overflow-hidden whitespace-nowrap block mt-1.5"
                                        style={{
                                          transform: "translateY(-5px)",
                                        }}
                                      >
                                        {item.PO || ""}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-start mt-8">
                                  <span
                                    className="text-left text-[12px] overflow-hidden whitespace-nowrap"
                                    style={{
                                      transform: "translateY(-5px)",
                                      maxWidth: "220px",
                                    }}
                                  >
                                    {item.Product_Name || ""}
                                  </span>
                                </div>
                              </td>
                              <td className="py-0.5 px-1 border-t border-l border-r border-dashed border-[#78a7da] text-[12px] font-bold text-center">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {item.pt_name || ""}
                                </span>
                                <span
                                  className="block mt-1"
                                  style={{ transform: "translateY(-5px)" }}
                                >
                                  {item.Material1 || ""}
                                </span>
                              </td>
                              <td className="py-0.5 px-1 border-t border-l border-r border-dashed border-[#78a7da] text-[12px] font-bold text-center">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {result.planQty || ""} <br />
                                  {result.spare || ""}
                                </span>
                              </td>
                              {Array.from({ length: 24 }).map((_, index) => {
                                const ppcValue = item[`PPC${index + 1}`] || "";

                                const fontSize =
                                  ppcValue.trim().length > 7
                                    ? "text-[10px]"
                                    : "text-[14px]";

                                // ฟังก์ชันแปลง Date -> yyyyMMdd (เพื่อใช้เปรียบเทียบ)
                                const formatForComparison = (date) => {
                                  if (!date) return "";
                                  const d = new Date(date);
                                  const yyyy = d.getFullYear();
                                  const mm = String(d.getMonth() + 1).padStart(
                                    2,
                                    "0"
                                  );
                                  const dd = String(d.getDate()).padStart(
                                    2,
                                    "0"
                                  );
                                  return `${yyyy}${mm}${dd}`;
                                };

                                // ฟังก์ชันแปลง Date -> mm/d (เพื่อแสดงผล)
                                const formatForDisplay = (date) => {
                                  if (!date) return "";
                                  const d = new Date(date);
                                  const mm = String(d.getMonth() + 1);
                                  const dd = String(d.getDate());
                                  return `${dd}/${mm}`;
                                };

                                // PPD value: แปลงเป็นวันที่
                                const ppdValue = item[`PPD${index + 1}`]
                                  ? new Date(item[`PPD${index + 1}`])
                                  : null;
                                const ppdFormatted =
                                  formatForComparison(ppdValue);
                                const ppdDisplay = formatForDisplay(ppdValue);

                                // RPD value: ถ้าไม่มีค่า ให้ใช้ PPD แทน
                                const rpdValue = item[`RPD${index + 1}`]
                                  ? formatForDisplay(item[`RPD${index + 1}`])
                                  : ppdDisplay;

                                let displayValue = "";
                                let backgroundColor = "";

                                // Mark_Days
                                const markDays = planlistData.data[0]?.Mark_Days
                                  ? new Date(planlistData.data[0]?.Mark_Days)
                                  : null;
                                const markDaysFormatted =
                                  formatForComparison(markDays);

                                // ถ้า isResultDataView เป็น false และ RPD มีค่า → ให้พื้นหลังเป็นสีดำเสมอ
                                if (
                                  !isResultDataView &&
                                  item[`RPD${index + 1}`]
                                ) {
                                  displayValue = "■";
                                } else {
                                  // ถ้า PPD > Mark_Days → ให้พื้นหลังเป็นสีฟ้า
                                  if (
                                    ppdFormatted &&
                                    markDaysFormatted &&
                                    ppdFormatted > markDaysFormatted
                                  ) {
                                    backgroundColor = "";
                                  }
                                  // ถ้า PPD < Mark_Days → ให้พื้นหลังเป็นสีเหลือง
                                  else if (
                                    ppdFormatted &&
                                    markDaysFormatted &&
                                    ppdFormatted < markDaysFormatted
                                  ) {
                                    backgroundColor = "bg-yellow-500";
                                  }

                                  // กรณี isResultDataView เป็น true
                                  if (isResultDataView) {
                                    displayValue = rpdValue;
                                    if (item[`RPD${index + 1}`]) {
                                      backgroundColor = "bg-black text-white"; // กรณีแสดงผลตาม RPD ต้องพื้นหลังดำ
                                    }
                                  } else {
                                    displayValue = ppdDisplay; // ถ้าไม่มี RPD ให้แสดง PPD ตามปกติ
                                  }
                                }

                                return (
                                  <td
                                    key={`ppc-${index}`}
                                    className={`border-t border-l border-r border-dashed border-[#78a7da] text-[11px] font-normal text-center min-w-[50px] break-words whitespace-normal overflow-hidden`}
                                    style={{
                                      wordWrap: "break-word",
                                      overflowWrap: "break-word",
                                      wordBreak: "break-word",
                                      position: "relative",
                                      zIndex: 0,
                                    }}
                                  >
                                    <div className={`${fontSize} font-bold`}>
                                      <span
                                        style={{
                                          transform: "translateY(-5px)",
                                        }}
                                      >
                                        {ppcValue}
                                      </span>
                                      <div
                                        className={`block mt-1 ${backgroundColor}`}
                                      >
                                        <span
                                          className="text-[14px]"
                                          style={{
                                            transform: "translateY(-7px)",
                                            color:
                                              displayValue === "■"
                                                ? "black"
                                                : "",
                                          }}
                                        >
                                          {displayValue}
                                        </span>
                                      </div>
                                    </div>
                                  </td>
                                );
                              })}

                              <td className="py-0.5 px-1 border-t border-l border-r border-dashed border-[#78a7da] text-[14px] text-center font-normal relative">
                                <div
                                  className="flex flex-col absolute font-medium gap-y-1"
                                  style={{
                                    transform: "translateY(-25px)",
                                    right: "5px",
                                    whiteSpace: "nowrap",
                                    zIndex: 10,
                                  }}
                                >
                                  <span>{item.Pt_Instructions || ""}</span>

                                  <span>{item.Pt_Information || ""}</span>
                                </div>
                              </td>
                            </tr>

                            {/* สร้างแถวใหม่ถ้าจำนวน PPC มากกว่า 24 */}
                            {extraValues > 0 && (
                              <tr
                                key={`extra-${rowIndex}`}
                                className={`border-b border-[#000080] text-xs h-12 ${rowBackgroundColor}`}
                              >
                                {Array.from({ length: 7 }).map((_, i) => (
                                  <td
                                    key={`empty-${i}`}
                                    className="border-l border-r border-dashed border-[#78a7da]"
                                  ></td>
                                ))}
                                {Array.from({ length: extraValues }).map(
                                  (_, index) => {
                                    const dataIndex = 24 + index;
                                    const ppcValue =
                                      item[`PPC${dataIndex + 1}`] || "";

                                    const fontSize =
                                      ppcValue.trim().length > 7
                                        ? "text-[10px]"
                                        : "text-[14px]";

                                    // ฟังก์ชันแปลง Date -> yyyyMMdd (เพื่อใช้เปรียบเทียบ)
                                    const formatForComparison = (date) => {
                                      if (!date) return "";
                                      const d = new Date(date);
                                      const yyyy = d.getFullYear();
                                      const mm = String(
                                        d.getMonth() + 1
                                      ).padStart(2, "0");
                                      const dd = String(d.getDate()).padStart(
                                        2,
                                        "0"
                                      );
                                      return `${yyyy}${mm}${dd}`;
                                    };

                                    // ฟังก์ชันแปลง Date -> mm/d (เพื่อแสดงผล)
                                    const formatForDisplay = (date) => {
                                      if (!date) return "";
                                      const d = new Date(date);
                                      const mm = String(d.getMonth() + 1);
                                      const dd = String(d.getDate());
                                      return `${dd}/${mm}`;
                                    };

                                    // PPD value: แปลงเป็นวันที่
                                    const ppdValue = item[`PPD${dataIndex + 1}`]
                                      ? new Date(item[`PPD${dataIndex + 1}`])
                                      : null;
                                    const ppdFormatted =
                                      formatForComparison(ppdValue);
                                    const ppdDisplay =
                                      formatForDisplay(ppdValue);

                                    // RPD value: ถ้าไม่มีค่า ให้ใช้ PPD แทน
                                    const rpdValue = item[`RPD${dataIndex + 1}`]
                                      ? formatForDisplay(
                                          item[`RPD${dataIndex + 1}`]
                                        )
                                      : ppdDisplay;

                                    let displayValue = "";
                                    let backgroundColor = "";

                                    // Mark_Days
                                    const markDays = planlistData.data[0]
                                      ?.Mark_Days
                                      ? new Date(
                                          planlistData.data[0]?.Mark_Days
                                        )
                                      : null;
                                    const markDaysFormatted =
                                      formatForComparison(markDays);

                                    // ถ้า isResultDataView เป็น false และ RPD มีค่า → ให้พื้นหลังเป็นสีดำเสมอ
                                    if (
                                      !isResultDataView &&
                                      item[`RPD${dataIndex + 1}`]
                                    ) {
                                      displayValue = "■";
                                    } else {
                                      // ถ้า PPD > Mark_Days → ให้พื้นหลังเป็นสีฟ้า
                                      if (
                                        ppdFormatted &&
                                        markDaysFormatted &&
                                        ppdFormatted > markDaysFormatted
                                      ) {
                                        backgroundColor = "";
                                      }
                                      // ถ้า PPD < Mark_Days → ให้พื้นหลังเป็นสีเหลือง
                                      else if (
                                        ppdFormatted &&
                                        markDaysFormatted &&
                                        ppdFormatted < markDaysFormatted
                                      ) {
                                        backgroundColor = "bg-yellow-500";
                                      }

                                      // กรณี isResultDataView เป็น true
                                      if (isResultDataView) {
                                        displayValue = rpdValue;
                                        if (item[`RPD${dataIndex + 1}`]) {
                                          backgroundColor =
                                            "bg-black text-white"; // กรณีแสดงผลตาม RPD ต้องพื้นหลังดำ
                                        }
                                      } else {
                                        displayValue = ppdDisplay; // ถ้าไม่มี RPD ให้แสดง PPD ตามปกติ
                                      }
                                    }

                                    return (
                                      <td
                                        key={`extra-${dataIndex}`}
                                        className={`border-l border-r border-dashed border-[#78a7da] text-[11px] font-normal text-center min-w-[50px] break-words whitespace-normal overflow-hidden`}
                                        style={{
                                          wordWrap: "break-word",
                                          overflowWrap: "break-word",
                                          wordBreak: "break-word",
                                          position: "relative",
                                          zIndex: 0,
                                        }}
                                      >
                                        <div
                                          className={`${fontSize} font-bold`}
                                        >
                                          <span
                                            style={{
                                              transform: "translateY(-5px)",
                                            }}
                                          >
                                            {ppcValue}
                                          </span>
                                          <div
                                            className={`block mt-1 ${backgroundColor}`}
                                          >
                                            <span
                                              className="text-[14px]"
                                              style={{
                                                transform: "translateY(-7px)",
                                                color:
                                                  displayValue === "■"
                                                    ? "black"
                                                    : "",
                                              }}
                                            >
                                              {displayValue}
                                            </span>
                                          </div>
                                        </div>
                                      </td>
                                    );
                                  }
                                )}
                                {/* ✅ คำนวณจำนวน td ว่างที่ต้องเติมให้ครบ 48 ช่อง */}
                                {Array.from({
                                  length: 48 - (latestPPDPosition || 0),
                                }).map((_, i) => (
                                  <td
                                    key={`empty-end-${i}`}
                                    className="border-l border-r border-dashed border-[#78a7da]"
                                  ></td>
                                ))}

                                <td className="py-0.5 px-1 border-l border-r border-dashed border-[#78a7da] text-[14px] text-center font-normal relative"></td>
                              </tr>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
}
