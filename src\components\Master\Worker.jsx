import React, { useState, useEffect } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Worker() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [editedData, setEditedData] = useState({});
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [selectedWorkers, setSelectedWorkers] = useState([]); // เก็บ Worker_CD ที่เลือก
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchWorker = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/order/worker`);

      setData(response.data.data.worker || []);
    } catch (error) {}
  };

  useEffect(() => {
    fetchWorker();
  }, []);

  const [formData, setFormData] = useState({
    Worker_CD: "",
    Worker_Pass: "",
    WorkerG_CD: "",
    WorkG_CD: "",
    Worker_Name: "",
    Worker_Abb: "",
    Worker_JPN: "",
    Access_Lv: "",
    Worker_Level: "",
    Worker_Menu: "",
    Worker_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleChange = (e, workerCd, field) => {
    setEditedData({
      ...editedData,
      [workerCd]: {
        ...editedData[workerCd],
        [field]: e.target.value,
      },
    });
  };

  const openModal = () => {
    if (selectedRowForCopy) {
      const { Worker_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Worker_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Worker_CD: "",
        Worker_Pass: "",
        WorkerG_CD: "",
        WorkG_CD: "",
        Worker_Name: "",
        Worker_Abb: "",
        Worker_JPN: "",
        Access_Lv: "",
        Worker_Level: "",
        Worker_Menu: "",
        Worker_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleCheckboxChange = (event, workerCD) => {
    const isChecked = event.target.checked;

    setSelectedWorkers((prev) => {
      if (isChecked) {
        return [...prev, workerCD]; // เพิ่ม Worker_CD ที่เลือก
      } else {
        return prev.filter((cd) => cd !== workerCD); // เอา Worker_CD ออกจากรายการ
      }
    });
  };

  const handleCreateWorker = async (e) => {
    e.preventDefault();

    // เงื่อนไขตรวจสอบ Access_Lv
    if (formData.Access_Lv.length > 1 || !/^[0-9]*$/.test(formData.Access_Lv)) {
      Swal.fire({
        title: "Validation Error",
        text: "Access_Lv must be a single number (0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    // เงื่อนไขตรวจสอบ Worker_Level
    if (
      formData.Worker_Level.length > 2 ||
      !/^[A-Za-z0-9]*$/.test(formData.Worker_Level)
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "Worker_Level must be up to 2 characters only (A-Z, a-z, 0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    // เงื่อนไขตรวจสอบ Worker_Menu
    if (
      formData.Worker_Menu.length > 2 ||
      !/^[A-Za-z0-9]*$/.test(formData.Worker_Menu)
    ) {
      Swal.fire({
        title: "Validation Error",
        text: "Worker_Menu must be up to 2 characters only (A-Z, a-z, 0-9).",
        icon: "error",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      const token = localStorage.getItem("authToken");

      const response = await axios.post(
        `${apiUrl_4000}/worker/create-worker`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Worker created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        // อัปเดตข้อมูลในตารางหลังจากสร้างสำเร็จ
        fetchWorker();

        // ล้างค่าฟอร์ม
        setFormData({
          Worker_CD: "",
          Worker_Pass: "",
          WorkerG_CD: "",
          WorkG_CD: "",
          Worker_Name: "",
          Worker_Abb: "",
          Worker_JPN: "",
          Access_Lv: "",
          Worker_Level: "",
          Worker_Menu: "",
          Worker_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        // ปิด Modal
        closeModal();
      }
    } catch (error) {
      console.error("Error creating worker:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create worker.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleUpdateWorker = async (workerCd) => {
    const payload = { Worker_CD: workerCd };
    let isEdited = false;

    const editedFields = Object.keys(editedData[workerCd] || {});

    for (const field of editedFields) {
      const newValue = editedData[workerCd]?.[field];
      const oldValue = data.find((row) => row.Worker_CD === workerCd)?.[field];

      if (newValue !== oldValue) {
        // ตรวจสอบฟิลด์ Access_Lv
        if (
          field === "Access_Lv" &&
          (newValue.length > 1 ||
            (!/^[0-9]*$/.test(newValue) && newValue !== ""))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be a single number (0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        // ตรวจสอบฟิลด์ Worker_Level
        if (
          field === "Worker_Level" &&
          (newValue.length > 2 || !/^[A-Za-z0-9]*$/.test(newValue))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be up to 2 characters only (A-Z, a-z, 0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        // ตรวจสอบฟิลด์ Worker_Menu
        if (
          field === "Worker_Menu" &&
          (newValue.length > 2 || !/^[A-Za-z0-9]*$/.test(newValue))
        ) {
          Swal.fire({
            title: "Validation Error",
            text: `${field} must be up to 2 characters only (A-Z, a-z, 0-9).`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        payload[field] = newValue === "" ? null : newValue;
        isEdited = true;
      }
    }

    if (!isEdited) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const response = await axios.put(
        `${apiUrl_4000}/worker/update-worker`,
        payload
      );

      const updatedData = [...data];
      const rowIndex = updatedData.findIndex(
        (row) => row.Worker_CD === workerCd
      );
      if (rowIndex !== -1) {
        Object.keys(payload).forEach((field) => {
          if (field !== "Worker_CD") {
            updatedData[rowIndex][field] = payload[field];
          }
        });
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "Worker data has been updated.",
      });
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const handleDeleteWorker = async () => {
    if (selectedWorkers.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const workerList = selectedWorkers.join(", "); // รวม Worker_CD ที่เลือกเป็น String

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>Worker CDs: <b>${workerList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/worker/delete-worker`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedWorkers.map((cd) => ({ Worker_CD: cd })), // ส่งเป็นอาเรย์ของ { Worker_CD }
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: "The selected workers have been deleted.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // รีเฟรชข้อมูล
          fetchWorker();

          // รีเซ็ต selectedWorker เป็นค่าว่าง
          setSelectedWorkers([]);
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteWorker:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 1?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 1 หรือไม่?<br>データは編集されました。master 1 に戻りますか？"
          : "Do you want to go back to master 1?<br>คุณต้องการกลับไปที่หน้า master 1 หรือไม่?<br>master 1 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/master1");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Worker_CD: row.Worker_CD,
      Worker_Pass: row.Worker_Pass,
      WorkerG_CD: row.WorkerG_CD,
      WorkG_CD: row.WorkG_CD,
      Worker_Name: row.Worker_Name,
      Worker_Abb: row.Worker_Abb,
      Worker_JPN: row.Worker_JPN,
      Access_Lv: row.Access_Lv,
      Worker_Level: row.Worker_Level,
      Worker_Menu: row.Worker_Menu,
      Worker_Remark: row.Worker_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Worker_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy && selectedRowForCopy.Worker_CD === row.Worker_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            checked={selectedWorkers.includes(row.Worker_CD)} // กำหนดสถานะของ checkbox
            onChange={(e) => handleCheckboxChange(e, row.Worker_CD)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Worker_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_CD !== undefined
              ? editedData[row.Worker_CD]?.Worker_CD
              : row.Worker_CD || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Worker_CD, "Worker_CD")}
          disabled
        />
      ),
      width: "170px",
    },
    {
      name: "Worker_Pass",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_Pass !== undefined
              ? editedData[row.Worker_CD]?.Worker_Pass
              : row.Worker_Pass || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Pass")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "WorkerG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.WorkerG_CD !== undefined
              ? editedData[row.Worker_CD]?.WorkerG_CD
              : row.WorkerG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "WorkerG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "WorkG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.WorkG_CD !== undefined
              ? editedData[row.Worker_CD]?.WorkG_CD
              : row.WorkG_CD || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "WorkG_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Worker_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_Name !== undefined
              ? editedData[row.Worker_CD]?.Worker_Name
              : row.Worker_Name || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "200px",
    },
    {
      name: "Worker_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_Abb !== undefined
              ? editedData[row.Worker_CD]?.Worker_Abb
              : row.Worker_Abb || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Worker_JPN",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_JPN !== undefined
              ? editedData[row.Worker_CD]?.Worker_JPN
              : row.Worker_JPN || ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_JPN")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Access_Lv",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.Worker_CD]?.Access_Lv !== undefined
              ? editedData[row.Worker_CD]?.Access_Lv
              : row.Access_Lv ?? ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Access_Lv")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Worker_Level",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_Level !== undefined
              ? editedData[row.Worker_CD]?.Worker_Level
              : row.Worker_Level ?? ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Level")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Worker_Menu",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Worker_CD]?.Worker_Menu !== undefined
              ? editedData[row.Worker_CD]?.Worker_Menu
              : row.Worker_Menu ?? ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Menu")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Worker_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "250px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Worker_CD]?.Worker_Remark !== undefined
              ? editedData[row.Worker_CD]?.Worker_Remark
              : row.Worker_Remark ?? ""
          }
          onChange={(e) => handleChange(e, row.Worker_CD, "Worker_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "300px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Worker <br /> 社員マスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Worker</h2>
                  <form onSubmit={handleCreateWorker}>
                    {/* Worker_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_CD
                      </label>
                      <input
                        type="text"
                        name="Worker_CD"
                        value={formData.Worker_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Pass */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Pass
                      </label>
                      <input
                        type="text"
                        name="Worker_Pass"
                        value={formData.Worker_Pass}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* WorkerG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkerG_CD
                      </label>
                      <input
                        type="text"
                        name="WorkerG_CD"
                        value={formData.WorkerG_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* WorkG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_CD
                      </label>
                      <input
                        type="text"
                        name="WorkG_CD"
                        value={formData.WorkG_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Name
                      </label>
                      <input
                        type="text"
                        name="Worker_Name"
                        value={formData.Worker_Name}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Abb
                      </label>
                      <input
                        type="text"
                        name="Worker_Abb"
                        value={formData.Worker_Abb}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_JPN */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_JPN
                      </label>
                      <input
                        type="text"
                        name="Worker_JPN"
                        value={formData.Worker_JPN}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Access_Lv */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Access_Lv
                      </label>
                      <input
                        type="text"
                        name="Access_Lv"
                        value={formData.Access_Lv}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Level */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Level
                      </label>
                      <input
                        type="text"
                        name="Worker_Level"
                        value={formData.Worker_Level}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Menu */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Menu
                      </label>
                      <input
                        type="text"
                        name="Worker_Menu"
                        value={formData.Worker_Menu}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Worker_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Worker_Remark
                      </label>
                      <textarea
                        name="Worker_Remark"
                        value={formData.Worker_Remark}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Worker_CD]?.[field] !== undefined) {
                      handleUpdateWorker(row.Worker_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteWorker}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
