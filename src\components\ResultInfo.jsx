import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";
import Swal from "sweetalert2";
import { useResult } from "../hooks/use-result";
import { useOrder } from "../hooks/use-order";
import { usePlan } from "../hooks/use-plan";
import { useCost } from "../hooks/use-cost";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Select from "react-select";

// Icons
import { AiTwotoneCalendar } from "react-icons/ai";

export default function ResultInfo() {
  const inputs = Array.from({ length: 12 }, (_, i) => i + 1);
  const inputs2 = Array.from({ length: 12 }, (_, i) => i + 13);
  const inputs3 = Array.from({ length: 12 }, (_, i) => i + 25);
  const inputs4 = Array.from({ length: 6 }, (_, i) => i + 1);
  const [isEditable, setIsEditable] = useState(false);
  const [selectedCustomerAbb, setSelectedCustomerAbb] = useState("");
  const [selectedWorkGName, setSelectedWorkGName] = useState("");
  const [personName, setPersonName] = useState("");
  const [selectedSalesPersonAbb, setSelectedSalesPerson] = useState("");
  const [SpecificName, setSpecificName] = useState("");
  const [OdProgressName, setOdProgressName] = useState("");
  const [DeliveryName, setDeliveryName] = useState("");
  const [targetName, setTargetName] = useState("");
  const [unitName, setUnitName] = useState("");
  const [PriceName, setPriceName] = useState("");
  const [supplyName, setSupplyName] = useState("");
  const [coatingName, setCoatingName] = useState("");
  const [targetNo, setTargetNo] = useState("");
  const [hasUserEditedOrderNo, setHasUserEditedOrderNo] = useState(false);
  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    F3: false,
    F4: false,
    F5: false,
    F6: true,
    F7: true,
    F8: false,
    F9: false,
    F10: false,
    F11: false,
    F12: true,
  });

  const {
    planData,
    setPlanData,
    searchPartsData,
    selectedPlanNo,
    setSelectedPlanNo,
    selectPartsData,
    PartsData,
    plprogressData,
    setPlProgressData,
    ScheduleData,
    setScheduleData,
    ProcessGData,
    QR_ProG_Plan,
    StatusData,
    setStatusData,
  } = usePlan();
  const {
    CoatingData,
    SupplyData,
    PriceData,
    UnitData,
    TargetData,
    DeliveryData,
    OdProgressData,
    orderData,
    WorkergData,
    WorkerData,
    SpecificData,
    setOrderData,
    searchOrderData,
    setCustomerData,
    CustomerData,
    setWorkergData,
    setWorkerData,
    setSpecificData,
    setOdProgressData,
    setDeliveryData,
    setTargetData,
    setUnitData,
    setPriceData,
    setSupplyData,
    setCoatingData,
  } = useOrder();
  const {
    ResultData,
    setResultData,
    SearchResultData,
    autoFinish,
    Update_Result,
  } = useResult();
  const { ProcessCData } = useCost();

  const navigate = useNavigate();
  const location = useLocation();
  const { searchOrderNo: initialSearchOrderNo = "" } = location.state || {};
  const { searchPlanNo: initialSearchPlanNo = "" } = location.state || {};
  const [searchOrderNo, setSearchOrderNo] = useState(initialSearchOrderNo);
  const [searchPlanNo, setSearchPlanNo] = useState(initialSearchPlanNo);
  const [orderNotFound, setOrderNotFound] = useState(false);
  const [Search_Odpt_No, setSearch_Odpt_No] = useState("");
  const [Person_Name, setPerson_Name] = useState("");
  const [PartName, setPartName] = useState("");
  const [ProgressName, setProgressName] = useState("");
  const [Schedule_Name, setSchedule_Name] = useState("");
  const SearchPartsNoRef = useRef(null);
  const [isBarcodeInput, setIsBarcodeInput] = useState(false);

  const handlePathClick = (path) => {
    try {
      // 1. Replace all backslashes with forward slashes
      let correctedPath = path.replace(/\\/g, "/");

      // 2. Ensure the protocol has a double slash after the colon
      correctedPath = correctedPath.replace("http:/", "http://");

      // 3. Check if the file exists before opening it
      fetch(correctedPath, { method: "HEAD" })
        .then((response) => {
          if (response.ok) {
            // File exists, open it in a new window
            console.log("Opening URL:", correctedPath);

            const windowWidth = 1024;
            const windowHeight = 768;
            const left = (screen.width - windowWidth) / 2;
            const top = (screen.height - windowHeight) / 2;

            window.open(
              correctedPath,
              "_blank",
              `width=${windowWidth},height=${windowHeight},top=${top},left=${left},status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes,scrollbars=yes`
            );
          } else {
            // File doesn't exist, show error popup
            console.error("File not found:", correctedPath);
            Swal.fire({
              icon: "error",
              title: "File Not Found",
              text: "The requested PDF file was not found or is inaccessible.",
            });
          }
        })
        .catch((error) => {
          console.error("Error checking file:", error);
          Swal.fire({
            icon: "error",
            title: "Error Occurred",
            text: "An error occurred while verifying the file.",
          });
        });
    } catch (error) {
      console.error("Error processing path:", error);
      Swal.fire({
        icon: "error",
        title: "Error Occurred",
        text: "An error occurred while processing the file path.",
      });
    }
  };

  const handleAdminChange = (e) => {
    const isChecked = e.target.checked; // เก็บค่าปัจจุบัน

    Swal.fire({
      title: "Input Password",
      input: "password",
      inputPlaceholder: "Input Password!",
      showCancelButton: true,
      confirmButtonText: "OK",
      cancelButtonText: "Cancel",
      inputAttributes: {
        autocapitalize: "off",
      },
      preConfirm: (password) => {
        if (password !== "PCTK") {
          Swal.showValidationMessage("Not Password!");
        }
        return password === "PCTK";
      },
    }).then((result) => {
      if (result.isConfirmed) {
        handleResultInputChange({ target: { ...e.target, checked: false } });

        if (isChecked) {
          // รหัสผ่านถูกต้อง (สามารถเพิ่มการตรวจสอบได้)
          setIsEditable(true); // ตั้งค่าสถานะให้สามารถแก้ไขข้อมูลได้
        } else {
          setIsEditable(false); // ถ้าติ๊กออก จะไม่สามารถแก้ไขได้
        }
      } else {
        e.target.checked = true;
      }
    });
  };

  const formatDateTime = (date) => {
    if (!date) return "";
    const d = new Date(date);

    // ดึงข้อมูลวันที่ในรูปแบบ UTC
    const day = String(d.getUTCDate()).padStart(2, "0");
    const month = String(d.getUTCMonth() + 1).padStart(2, "0");
    const year = d.getUTCFullYear();

    // ดึงข้อมูลเวลาในรูปแบบ UTC
    const hours = String(d.getUTCHours()).padStart(2, "0");
    const minutes = String(d.getUTCMinutes()).padStart(2, "0");
    const seconds = String(d.getUTCSeconds()).padStart(2, "0");

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const handleOrderInputChange = async (event) => {
    const { id, value, type, checked } = event.target;

    setOrderData((prevOrderData) => ({
      ...prevOrderData,
      [id]: type === "checkbox" ? checked : value === "" ? null : value,
    }));

    if (id === "Search_Order_No") {
      setHasUserEditedOrderNo(true);
      setOrderNotFound(false);

      if (value === "") {
        // Reset fields ทั้งหมด
        setSearchOrderNo("");
        setSearchPlanNo("");
        setSearch_Odpt_No("");
        setSelectedCustomerAbb("");
        setSelectedWorkGName("");
        setPersonName("");
        setSelectedSalesPerson("");
        setSpecificName("");
        setOdProgressName("");
        setDeliveryName("");
        setTargetName("");
        setUnitName("");
        setPriceName("");
        setSupplyName("");
        setCoatingName("");
        setPerson_Name("");
        setPartName("");
        setProgressName("");
        setSchedule_Name("");
        setOrderData({});
        setPlanData({});
        setResultData({});
        setSelectedDates({
          Tg_St_Pl_Date: null,
          Tg_Ed_Pl_Date: null,
        });
        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
          F8: false,
          F10: false,
          F11: true,
          F12: true,
        }));
        setIsBarcodeInput(false);
      } else if (value.length === 12) {
        // กรณี barcode 12 หลัก

        setIsBarcodeInput(true);

        const orderNo = value.slice(0, 10);
        const partsNo = value.slice(10); // 2 ตัวท้าย

        setSearchOrderNo(orderNo); // โชว์แค่ 10 หลัก
        setSearchPlanNo(partsNo); // Auto เลือก parts
        setSearch_Odpt_No(`${orderNo}-${partsNo}`);

        try {
          const result = await selectPartsData(orderNo, partsNo);

          if (!result || result.length === 0) {
            Swal.fire({
              icon: "warning",
              title: "No data found",
              text: `No data found for Order No: ${orderNo} and Parts No: ${partsNo}`,
            }).then(() => {
              // Reset เมื่อไม่เจอข้อมูล
              setSearchOrderNo("");
              setSearchPlanNo("");
              setSearch_Odpt_No("");
              setSelectedCustomerAbb("");
              setSelectedWorkGName("");
              setPersonName("");
              setSelectedSalesPerson("");
              setSpecificName("");
              setOdProgressName("");
              setDeliveryName("");
              setTargetName("");
              setUnitName("");
              setPriceName("");
              setSupplyName("");
              setCoatingName("");
              setPerson_Name("");
              setPartName("");
              setProgressName("");
              setSchedule_Name("");
              setOrderData({});
              setPlanData({});
              setResultData({});
              setIsBarcodeInput(false);
            });
          } else {
            setSelectedPlanNo(result); // update dropdown options
            // Auto เลือก parts_no จาก dropdown (โดย match กับ partsNo ที่ยิงมา)
            const matchedOption = result.find(
              (item) => item.Parts_No === partsNo
            );
            if (matchedOption) {
              handlePlanInputChange({
                target: {
                  id: "Search_Parts_No",
                  value: matchedOption.Parts_No,
                },
              });
            }
          }
        } catch (error) {
          console.error("Error selecting parts data:", error);
          Swal.fire({
            icon: "error",
            title: "Error",
            text: "An error occurred while searching for parts data.",
          }).then(() => {
            setSearchOrderNo("");
            setIsBarcodeInput(false);
          });
        }
      } else {
        // ยังไม่ครบ 12 หลัก → กรอกตามปกติ
        setSearchOrderNo(value);
        setIsBarcodeInput(false);
      }
    }
  };

  // Add a blur handler to search when the user tabs out or clicks elsewhere
  const handleSearchOrderNoBlur = async (event) => {
    const value = event.target.value;

    if (value) {
      if (orderNotFound) {
        // ถ้าเคยแจ้งเตือนว่าไม่พบแล้ว ก็ไม่ต้องค้นหาอีก
        return;
      }

      if (isBarcodeInput) {
        // barcode กรอกมาแล้ว ไม่ต้อง searchPartsData ซ้ำ
        setIsBarcodeInput(false); // เคลียร์สถานะ
        return;
      }

      const orderExists = await searchOrderData(value);

      if (orderExists) {
        await searchPartsData(value);
        setOrderNotFound(false); // เคลียร์สถานะ ถ้าค้นเจอ

        if (SearchPartsNoRef.current) {
          setTimeout(() => {
            if (SearchPartsNoRef.current.clearValue) {
              SearchPartsNoRef.current.clearValue(); // 🆕 เพิ่ม
            }
            SearchPartsNoRef.current.focus();
            SearchPartsNoRef.current.openMenu();
          }, 100);
        }

        setButtonState((prevState) => ({
          ...prevState,
          F2: true,
          F8: true,
          F10: true,
          F11: true,
          F12: true,
        }));
      } else {
        setSearchPlanNo("");
        setSearch_Odpt_No("");
        setSelectedPlanNo([]);
        setOrderNotFound(true);

        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
          F8: false,
          F10: false,
          F11: true,
          F12: true,
        }));

        Swal.fire({
          title: "ไม่พบข้อมูล",
          html: `${value} is not yet registered !<br>${value} ไม่ได้ถูกลงทะเบียน !<br>${value} は登録されていません!`,
          icon: "warning",
          confirmButtonText: "ตกลง",
        });
      }
    }
  };

  // Keep the Enter key handler
  const handleSearchOrderKeyDown = async (e) => {
    if (e.key === "Enter" && searchOrderNo) {
      if (isBarcodeInput) {
        // barcode กรอกมาแล้ว ไม่ต้อง searchPartsData ซ้ำ
        setIsBarcodeInput(false); // เคลียร์สถานะ
        return;
      }

      const orderExists = await searchOrderData(searchOrderNo);

      if (orderExists) {
        await searchPartsData(searchOrderNo);
        setOrderNotFound(false); // เคลียร์สถานะ ถ้าค้นเจอ

        if (SearchPartsNoRef.current) {
          setTimeout(() => {
            if (SearchPartsNoRef.current.clearValue) {
              SearchPartsNoRef.current.clearValue(); // 🆕 เพิ่ม
            }
            SearchPartsNoRef.current.focus();
            SearchPartsNoRef.current.openMenu();
          }, 100);
        }

        setButtonState((prevState) => ({
          ...prevState,
          F2: true,
          F8: true,
          F10: true,
          F11: true,
          F12: true,
        }));
      } else {
        setSearchPlanNo("");
        setSearch_Odpt_No("");
        setSelectedPlanNo([]);
        setOrderNotFound(true);

        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
          F8: false,
          F10: false,
          F11: true,
          F12: true,
        }));

        Swal.fire({
          title: "ไม่พบข้อมูล",
          html: `${searchOrderNo} is not yet registered !<br>${searchOrderNo} ไม่ได้ถูกลงทะเบียน !<br>${searchOrderNo} は登録されていません!`,
          icon: "warning",
          confirmButtonText: "ตกลง",
        });
      }
    }
  };

  const handlePlanInputChange = async (event) => {
    const { id, value, type, checked } = event.target;

    setPlanData((prevPlanData) => ({
      ...prevPlanData,
      [id]: type === "checkbox" ? checked : value === "" ? null : value,
    }));
    setResultData({ ...ResultData, Plan_Edit: true });

    if (id === "Search_Parts_No") {
      setSearchPlanNo(value);
      setHasUserEditedOrderNo(true);
      if (value && searchOrderNo) {
        setSearch_Odpt_No(`${searchOrderNo || ""}-${value}`);
        await searchPartsData(searchOrderNo);
      } else if (!value) {
        setSearch_Odpt_No("");
      }
    }
  };

  const handleResultInputChange = async (event) => {
    const { id, value, type, checked } = event.target;

    setResultData((prevResultData) => {
      const updatedData = {
        ...prevResultData,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
      };
      // แปลงวันที่ให้เป็น ISO string
      if (updatedData[id] && id.startsWith("RPD")) {
        updatedData[id] = new Date(updatedData[id]).toISOString();
      }

      return updatedData;
    });
  };

  const handleF2Click = async () => {
    try {
      searchPermission(false);
      setButtonState((prevState) => ({
        ...prevState,
        F2: false,
        F4: true,
        F8: true,
        F9: true,
        F10: true,
        F11: true,
        F12: false,
      }));
      const checkbox = document.getElementById("Admin");
      checkbox.disabled = !checkbox.disabled;
      let FG = 0;
      let KN = 0;

      while (FG < 1) {
        KN += 1;

        if (KN !== 36) {
          if (
            !ResultData ||
            !ResultData["RPD" + KN] ||
            ResultData["RPD" + KN] === ""
          ) {
            FG = 1;
          }
        } else {
          FG = 1;
        }
      }
      document.getElementById("RPD" + KN)?.focus();
      if (ResultData) {
        ResultData.Target_Pr_No = KN;
      }

      setIsEditable(true);
    } catch (error) {
      alert("Error occurs when F2_Click\nPlease contact system administrator.");
    }
  };

  const handleF4Click = async () => {
    try {
      const orderExists = await searchOrderData(searchOrderNo);
      if (orderExists) {
        const updatedResultData = {
          Target_Pr_No: ResultData?.Target_Pr_No,
          Pt_NG_Qty: planData?.Pt_NG_Qty,
          Pt_Spare_Qty: planData?.Pt_Spare_Qty,
          Pt_Qty: planData?.Pt_Qty,
        };

        setStatusData((prevState) => ({
          ...prevState,
          Obj_Od_No: "",
          Obj_Pt_No: "",
        }));
        const queryString = encodeURIComponent(
          JSON.stringify(updatedResultData)
        );
        sessionStorage.setItem("searchOrderNo", searchOrderNo);
        sessionStorage.setItem("searchPlanNo", searchPlanNo);

        // ดึงขนาดหน้าจอของผู้ใช้
        const screenWidth = window.screen.width;
        const screenHeight = window.screen.height;

        // เปิดเป็นหน้าต่างใหม่แบบเต็มจอ
        window.open(
          `/cost-info?data=${queryString}`,
          "_blank",
          `width=${screenWidth},height=${screenHeight},top=0,left=0`
        );
      } else {
        await Swal.fire({
          title: "The information is incorrect.",
          text: "No order number found",
          icon: "warning",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      alert("Error occurs when F4_Click\nPlease contact system administrator.");
    }
  };

  const handleF5Click = async () => {
    try {
      setButtonState((prevState) => ({
        ...prevState,
        F3: false,
      }));
    } catch (error) {
      alert("Error occurs when F5_Click\nPlease contact system administrator.");
    }
  };

  const showSwalError = async () => {
    await Swal.fire({
      title: swalMessages.errorTitle,
      text: swalMessages.errorText,
      icon: "error",
      confirmButtonText: "ตกลง",
    });
  };

  const handleF6Click = async () => {
    try {
      const updatedStatusData = {
        Sort: planData?.Sort,
        Print_Object: null,
        Info_View: planData?.Info_View,
        Mark_Days: planData?.Mark_Days,
        Color_Separate: planData?.Color_Separate,
        Tg_St_Pl_Date: planData?.Tg_St_Pl_Date,
        Tg_Ed_Pl_Date: planData?.Tg_Ed_Pl_Date
          ? planData.Tg_Ed_Pl_Date
          : new Date(new Date().setDate(new Date().getDate() + 10)),
        TG_ProcessG: planData?.Tg_ProcessG,
        List: false,
        Graph: true,
        Settles: true,
        Settles_Day: new Date(),
      };

      const checkData = await QR_ProG_Plan(updatedStatusData);
      if (checkData && checkData.length > 0) {
        const newTab = window.open();
        const queryString = encodeURIComponent(
          JSON.stringify(updatedStatusData)
        );
        const url = `/reports/RD_ProG_Graph?data=${queryString}`;
        newTab.location.href = url;
      }
    } catch (error) {
      console.error("Error in handleF6Click:", error);
      await showSwalError();
    }
  };

  const handleF7Click = async () => {
    try {
      const updatedStatusData = {
        Sort: planData?.Sort,
        Print_Object: null,
        Info_View: planData?.Info_View,
        Mark_Days: planData?.Mark_Days,
        Color_Separate: planData?.Color_Separate,
        Tg_St_Pl_Date: planData?.Tg_St_Pl_Date,
        Tg_Ed_Pl_Date: planData?.Tg_Ed_Pl_Date
          ? planData.Tg_Ed_Pl_Date
          : new Date(new Date().setDate(new Date().getDate() + 10)),
        TG_ProcessG: planData?.Tg_ProcessG,
        List: false,
        Graph: true,
        Settles: true,
        Settles_Day: new Date(),
      };

      const checkData = await QR_ProG_Plan(updatedStatusData);
      if (checkData && checkData.length > 0) {
        const newTab = window.open();
        const queryString = encodeURIComponent(
          JSON.stringify(updatedStatusData)
        );
        const url = `/reports/RD_ProG_Plan?data=${queryString}`;
        newTab.location.href = url;
      }
    } catch (error) {
      console.error("Error in handleF7Click:", error);
      await showSwalError();
    }
  };

  const handleF8Click = async () => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        text: "Search other Parts_No?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        searchPermission(true);
        setButtonState({
          F1: false,
          F2: false,
          F3: false,
          F4: false,
          F5: false,
          F6: true,
          F7: true,
          F8: false,
          F9: false,
          F10: false,
          F11: false,
          F12: true,
        });
        setSelectedCustomerAbb("");
        setSelectedWorkGName("");
        setPersonName("");
        setSelectedSalesPerson("");
        setSpecificName("");
        setOdProgressName("");
        setDeliveryName("");
        setTargetName("");
        setUnitName("");
        setPriceName("");
        setSupplyName("");
        setCoatingName("");
        setSearch_Odpt_No("");
        setPerson_Name("");
        setPartName("");
        setProgressName("");
        setSchedule_Name("");
        setSearchPlanNo("");
        setOrderData("");
        setPlanData("");
        setResultData("");
      }
    } catch (error) {
      alert("Error occurs when F8_Click\nPlease contact system administrator.");
    }
  };

  const handleF9Click = async () => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        text: "Do you save data?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        setButtonState((prevState) => ({
          ...prevState,
          F2: true,
          F3: false,
          F4: false,
          F5: false,
          F9: false,
          F12: true,
        }));
        const requestData = {};
        for (let i = 1; i <= 36; i++) {
          requestData[`PMT${i}`] = planData?.[`PMT${i}`];
          requestData[`PPT${i}`] = planData?.[`PPT${i}`];
          requestData[`PPD${i}`] = planData?.[`PPD${i}`]
            ? new Date(planData[`PPD${i}`]).toISOString()
            : null;
          requestData[`PML${i}`] = planData?.[`PML${i}`];
          requestData[`PPL${i}`] = planData?.[`PPL${i}`];
          requestData[`RPD${i}`] = ResultData?.[`RPD${i}`]
            ? new Date(ResultData[`RPD${i}`]).toISOString()
            : null;
          requestData[`RMT${i}`] = ResultData?.[`RMT${i}`];
          requestData[`RPT${i}`] = ResultData?.[`RPT${i}`];
          requestData[`RPN${i}`] = ResultData?.[`RPN${i}`];
        }
        requestData.RPD = ResultData?.RPD
          ? new Date(ResultData?.RPD).toISOString()
          : null;
        requestData.RMT = ResultData?.RMT;
        requestData.RPT = ResultData?.RPT;
        requestData.RPN = ResultData?.RPN;
        requestData.PMT = planData?.PMT;
        requestData.PPT = planData?.PPT;
        requestData.PPD = planData?.PPD;
        requestData.PML = planData?.PML;
        requestData.PPL = planData?.PPL;
        requestData.Total_M_Time = planData?.Total_M_Time;
        requestData.Total_P_Time = planData?.Total_P_Time;
        requestData.Re_Pr_Qty = planData?.Re_Pr_Qty;
        requestData.Re_Total_M_Time = planData?.Re_Total_M_Time;
        requestData.Re_Total_P_Time = planData?.Re_Total_P_Time;
        requestData.Re_Total_N_Time = planData?.Re_Total_N_Time;
        requestData.Od_Progress_CD = orderData?.Od_Progress_CD;
        requestData.Pl_Progress_CD = planData?.Pl_Progress_CD;
        requestData.Pt_Complete_Date = planData?.Pt_Complete_Date;
        requestData.Pd_Complete_Date = orderData?.Pd_Complete_Date;
        requestData.Now_No = planData?.Now_No;
        requestData.End_No = planData?.End_No;
        requestData.Auto_Finish = autoFinish?.Auto_Finish;
        requestData.Plan_Edit = ResultData?.Plan_Edit;

        await Update_Result(requestData, searchOrderNo, searchPlanNo);
      }
    } catch (error) {
      Swal.fire({
        title: "Error",
        text: "An error occurred when F9_Click.\nPlease contact system administrator.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF10Click = async () => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        text: "Next Order_No input?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        const newOrderNo = incrementOrderNo(searchOrderNo);

        setSearchOrderNo(newOrderNo);

        searchPermission(true);
        setButtonState({
          F1: false,
          F2: false,
          F3: false,
          F4: false,
          F5: false,
          F6: true,
          F7: true,
          F8: false,
          F9: false,
          F10: false,
          F11: false,
          F12: true,
        });
        setSelectedCustomerAbb("");
        setSelectedWorkGName("");
        setPersonName("");
        setSelectedSalesPerson("");
        setSpecificName("");
        setOdProgressName("");
        setDeliveryName("");
        setTargetName("");
        setUnitName("");
        setPriceName("");
        setSupplyName("");
        setCoatingName("");
        setSearch_Odpt_No("");
        setPerson_Name("");
        setPartName("");
        setProgressName("");
        setSchedule_Name("");
        setSearchPlanNo("");
        setOrderData("");
        setPlanData("");
        setResultData("");
      }
    } catch (error) {
      alert(
        "Error occurs when F10_Click\nPlease contact system administrator."
      );
    }
  };

  const incrementOrderNo = (orderNo) => {
    const regex = /(\D+)(\d+)$/;
    const match = orderNo.match(regex);

    if (match) {
      const prefix = match[1];
      const number = parseInt(match[2], 10);
      const newNumber = number + 1;
      return `${prefix}${newNumber}`;
    }

    return orderNo;
  };

  const handleF11Click = async () => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        text: "Search other Parts_No?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        searchPermission(true);

        setButtonState({
          F1: false,
          F2: false,
          F3: false,
          F4: false,
          F5: false,
          F6: true,
          F7: true,
          F8: false,
          F9: false,
          F10: false,
          F11: false,
          F12: true,
        });

        // รีเซ็ตค่าทั้งหมดให้เป็นค่าว่าง
        setSelectedCustomerAbb("");
        setSelectedWorkGName("");
        setPersonName("");
        setSelectedSalesPerson("");
        setSpecificName("");
        setOdProgressName("");
        setDeliveryName("");
        setTargetName("");
        setUnitName("");
        setPriceName("");
        setSupplyName("");
        setCoatingName("");
        setSearch_Odpt_No("");
        setPerson_Name("");
        setPartName("");
        setProgressName("");
        setSchedule_Name("");
        setSearchPlanNo("");
        setOrderData("");
        setPlanData("");
        setSearchOrderNo("");
        setResultData("");
        setIsEditable(false);
      }
    } catch (error) {
      alert(
        "Error occurs when F11_Click\nPlease contact system administrator."
      );
    }
  };

  const handleF12Click = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You are about to reset the form data!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, reset it!",
      cancelButtonText: "No, cancel",
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        setSelectedCustomerAbb("");
        setSelectedWorkGName("");
        setPersonName("");
        setSelectedSalesPerson("");
        setSpecificName("");
        setOdProgressName("");
        setDeliveryName("");
        setTargetName("");
        setUnitName("");
        setPriceName("");
        setSupplyName("");
        setCoatingName("");
        setSearch_Odpt_No("");
        setPerson_Name("");
        setPartName("");
        setProgressName("");
        setSchedule_Name("");
        setSearchPlanNo("");
        setOrderData("");
        setPlanData("");
        setSearchOrderNo("");
        setResultData("");
        setOrderData({});

        Swal.fire("Reset!", "The form data has been reset.", "success");
        navigate("/dashboard");
      } else {
        // console.log("Reset action cancelled.");
      }
    });
  };

  const [selectedDates, setSelectedDates] = React.useState({
    Tg_St_Pl_Date: planData?.Tg_St_Pl_Date
      ? new Date(planData.Tg_St_Pl_Date)
      : null,
    Tg_Ed_Pl_Date: planData?.Tg_Ed_Pl_Date
      ? new Date(planData.Tg_Ed_Pl_Date)
      : new Date(new Date().setDate(new Date().getDate() + 10)),
    Mark_Days: planData?.Mark_Days
      ? new Date(planData.Mark_Days)
      : new Date(new Date().setDate(new Date().getDate() + 1)),
  });

  const handleChange = (date, field) => {
    setSelectedDates((prev) => ({ ...prev, [field]: date }));
    handlePlanInputChange({
      target: {
        id: field,
        value: date ? date.toISOString().split("T")[0] : "",
      },
    });
  };
  useEffect(() => {
    if (ResultData?.Target_Pr_No !== undefined) {
      setTargetNo(ResultData.Target_Pr_No);
    }
  }, [ResultData?.Target_Pr_No]);

  useEffect(() => {
    // ฟังก์ชันที่ดึงข้อมูลใหม่ทุกๆ 5 วินาที
    const fetchData = () => {
      if (Search_Odpt_No) {
        selectPartsData(searchOrderNo, searchPlanNo);
        SearchResultData(searchOrderNo, searchPlanNo);
      }
    };
    fetchData(); // เรียกครั้งแรกเมื่อ component ถูก mount
    const interval = setInterval(fetchData, 5000); // เรียกทุก 5 วินาที
    return () => clearInterval(interval); // เคลียร์ interval เมื่อ component ถูก unmount
  }, [Search_Odpt_No, searchOrderNo, searchPlanNo]);

  useEffect(() => {
    if (Search_Odpt_No) {
      const result = SearchResultData(searchOrderNo, searchPlanNo);
      if (result) {
        setButtonState((prevState) => ({
          ...prevState,
          F2: true,
          F8: true,
          F10: true,
          F11: true,
          F12: true,
        }));
        searchOrderData(searchOrderNo);
      } else {
        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
          F8: false,
          F10: false,
          F11: false,
          F12: false,
        }));
      }
    }
  }, [searchPlanNo]);

  useEffect(() => {
    if (orderData?.Customer_CD && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === orderData.Customer_CD
      );

      setSelectedCustomerAbb(selectedGroup ? selectedGroup.Customer_Name : "");
    }
    if (orderData?.Product_Grp_CD && WorkergData.length > 0) {
      const selectedGroup = WorkergData.find(
        (item) => item.WorkG_CD === orderData.Product_Grp_CD
      );
      setSelectedWorkGName(selectedGroup ? selectedGroup.WorkG_Name : "");
    }
    if (orderData?.Od_Ctl_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Od_Ctl_Person_CD
      );
      setPersonName(selectedGroup ? selectedGroup.Worker_Name : "");
    }
    if (orderData?.Sales_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Sales_Person_CD
      );
      setSelectedSalesPerson(selectedGroup ? selectedGroup.Worker_Abb : "");
    }
    if (orderData?.Specific_CD && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === orderData.Specific_CD
      );

      setSpecificName(selectedGroup ? selectedGroup.Specific_Name : "");
    }
    if (orderData?.Od_Progress_CD && OdProgressData.length > 0) {
      const selectedGroup = OdProgressData.find(
        (item) => item.Od_Progress_CD === orderData.Od_Progress_CD
      );

      setOdProgressName(selectedGroup ? selectedGroup.Od_Progress_Name : "");
    }
    if (orderData?.Delivery_CD && DeliveryData.length > 0) {
      const selectedGroup = DeliveryData.find(
        (item) => item.Delivery_CD === orderData.Delivery_CD
      );

      setDeliveryName(selectedGroup ? selectedGroup.Delivery_Name : "");
    }
    if (orderData?.Target_CD && TargetData.length > 0) {
      const selectedGroup = TargetData.find(
        (item) => item.Target_CD === orderData.Target_CD
      );

      setTargetName(selectedGroup ? selectedGroup.Target_Name : "");
    }
    if (orderData?.Unit_CD && UnitData.length > 0) {
      const selectedGroup = UnitData.find(
        (item) => item.Unit_CD === orderData.Unit_CD
      );

      setUnitName(selectedGroup ? selectedGroup.Unit_Name : "");
    }
    if (orderData?.Unit_Price && PriceData.length > 0) {
      const selectedGroup = PriceData.find(
        (item) => item.Price_CD === orderData.Unit_Price
      );

      setPriceName(selectedGroup ? selectedGroup.Price_Name : "");
    }
    if (orderData?.Supply_CD && SupplyData.length > 0) {
      const selectedGroup = SupplyData.find(
        (item) => item.Supply_CD === orderData.Supply_CD
      );

      setSupplyName(selectedGroup ? selectedGroup.Supply_Name : "");
    }
    if (orderData?.Coating_CD && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === orderData.Coating_CD
      );

      setCoatingName(selectedGroup ? selectedGroup.Coating_Name : "none");
    }
  }, [
    orderData?.Customer_CD,
    orderData?.Product_Grp_CD,
    orderData?.Od_Ctl_Person_CD,
    orderData?.Specific_CD,
    orderData?.Od_Progress_CD,
    orderData?.Delivery_CD,
    orderData?.Target_CD,
    DeliveryData,
    SpecificData,
    CustomerData,
    OdProgressData,
    TargetData,
  ]);

  useEffect(() => {
    if (planData?.Pl_Reg_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === planData.Pl_Reg_Person_CD
      );
      setPerson_Name(selectedGroup ? selectedGroup.Worker_Abb : "");
    }

    if (planData?.Parts_CD && PartsData.length > 0) {
      const selectedGroup = PartsData.find(
        (item) => item.Parts_CD === planData.Parts_CD
      );
      setPartName(selectedGroup ? selectedGroup.Parts_Name : "");
    }
    if (planData?.Pl_Progress_CD && plprogressData.length > 0) {
      const selectedGroup = plprogressData.find(
        (item) => item.Pl_Progress_CD === planData.Pl_Progress_CD
      );

      setProgressName(selectedGroup ? selectedGroup.Pl_Progress_Symbol : "");
    }
    if (planData?.Pl_Schedule_CD && ScheduleData.length > 0) {
      const selectedGroup = ScheduleData.find(
        (item) => item.Schedule_CD === planData.Pl_Schedule_CD
      );

      setSchedule_Name(selectedGroup ? selectedGroup.Schedule_Symbol : "");
    }
  }, [
    planData?.Pl_Progress_CD,
    planData?.Pl_Schedule_CD,
    ScheduleData,
    plprogressData,
  ]);

  useEffect(() => {
    // console.log("reset data");

    // รีเซ็ตค่า
    return () => {
      // Code to run on unmount
      setSearchOrderNo("");
      setOrderData({});
      setPlanData({});
      // setCostData({});
      // SearchCostData("");
      // setSelectedPlanNo("");
      // console.log("Component is being unmounted");
    };
  }, []);

  useEffect(() => {
    if (!hasUserEditedOrderNo && StatusData?.Obj_Od_No) {
      setSearchOrderNo(StatusData.Obj_Od_No);
    }
    if (!hasUserEditedOrderNo && StatusData?.Obj_Pt_No) {
      setSearchPlanNo(StatusData.Obj_Pt_No);
      setSearch_Odpt_No(
        `${StatusData.Obj_Od_No || ""}-${StatusData.Obj_Pt_No}`
      );
      searchPartsData(StatusData.Obj_Od_No);
    }
  }, [StatusData, hasUserEditedOrderNo]);

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col overflow-x-hidden flex-grow p-2 bg-white mt-2 rounded-md">
          <div className="grid grid-cols-1">
            <h1 className="text-2xl font-bold mt-3 text-center">Result Info</h1>
            <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

            <div>
              <div className="w-full overflow-x-auto">
                <div className="min-w-[1600px] w-full">
                  <div className="mx-5 py-4">
                    <div className="grid grid-cols-5 w-full items-center gap-2 mb-3">
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Search_Order_No
                        </label>
                        <div className="w-3/5">
                          <input
                            id="Search_Order_No"
                            value={searchOrderNo || ""}
                            onChange={handleOrderInputChange}
                            onBlur={handleSearchOrderNoBlur}
                            onKeyDown={handleSearchOrderKeyDown}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Search_Parts_No
                        </label>
                        <div className="w-3/5">
                          <Select
                            id="Search_Parts_No"
                            ref={SearchPartsNoRef}
                            value={
                              searchPlanNo
                                ? { label: searchPlanNo, value: searchPlanNo }
                                : null
                            }
                            onChange={(selectedOption) => {
                              if (selectedOption) {
                                handlePlanInputChange({
                                  target: {
                                    id: "Search_Parts_No",
                                    value: selectedOption.value,
                                  },
                                });
                              } else {
                                handlePlanInputChange({
                                  target: {
                                    id: "Search_Parts_No",
                                    value: "",
                                  },
                                });
                              }
                            }}
                            options={
                              Array.isArray(selectedPlanNo) &&
                              selectedPlanNo.length > 0
                                ? selectedPlanNo.map((item) => ({
                                    label: item.Parts_No,
                                    value: item.Parts_No,
                                  }))
                                : []
                            }
                            className="w-full"
                            styles={{
                              control: (base) => ({
                                ...base,
                                borderColor: "rgb(107, 114, 128)",
                                borderWidth: "2px",
                                borderRadius: "0.375rem",
                                backgroundColor: "#ffff99",
                              }),
                              menu: (base) => ({
                                ...base,
                                fontSize: "12px",
                              }),
                              option: (base) => ({
                                ...base,
                                fontSize: "12px",
                              }),
                            }}
                            placeholder="Parts No"
                            isDisabled={!searchOrderNo}
                            menuPortalTarget={document.body}
                            noOptionsMessage={() => "No data found"}
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Search_OdPtL_No
                        </label>
                        <div className="w-3/5">
                          <input
                            id="Search_Odpt_No"
                            value={Search_Odpt_No || ""}
                            onChange={(e) => handlePlanInputChange(e)}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          End_No
                        </label>
                        <div className="w-3/5">
                          <input
                            id="End_No"
                            value={planData?.End_No || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Total(min)
                        </label>
                        <div className="w-3/5 flex justify-between items-center gap-2">
                          <input
                            id="Total_M_Time"
                            value={planData?.Total_M_Time || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">~</label>
                          <input
                            id="Total_P_Time"
                            value={planData?.Total_P_Time || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-5 w-full items-center gap-2 mb-3">
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Target_ProcessL_Grp
                        </label>
                        <div className="w-3/5">
                          <select
                            id="Tg_ProcessG"
                            value={planData?.Tg_ProcessG || ""}
                            onChange={handlePlanInputChange}
                            className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                          >
                            <option value=""></option>
                            {Array.isArray(ProcessGData) &&
                            ProcessGData.length > 0 ? (
                              ProcessGData.map((item, index) => (
                                <option key={index} value={item.ProcessG_CD}>
                                  {item.ProcessG_Mark} || {item.ProcessG_Abb}
                                </option>
                              ))
                            ) : (
                              <option value="">No data found</option>
                            )}
                          </select>
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Target_Process_Date
                        </label>
                        <div className="w-3/5 flex justify-between items-center gap-2">
                          <DatePicker
                            selected={selectedDates.Tg_St_Pl_Date}
                            onChange={(date) =>
                              handleChange(date, "Tg_St_Pl_Date")
                            }
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32"
                          />
                          <label className="font-medium text-xs">~</label>
                          <DatePicker
                            selected={selectedDates.Tg_Ed_Pl_Date}
                            onChange={(date) =>
                              handleChange(date, "Tg_Ed_Pl_Date")
                            }
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32"
                          />
                        </div>
                      </div>
                      <div></div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Now_No
                        </label>
                        <div className="w-3/5">
                          <input
                            id="Now_No"
                            value={planData?.Now_No || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Re_Tatal
                        </label>
                        <div className="w-3/5 flex justify-between items-center gap-2">
                          <input
                            id="Re_Total_M_Time"
                            value={planData?.Re_Total_M_Time || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">~</label>
                          <input
                            id="Re_Total_P_Time"
                            value={planData?.Re_Total_P_Time || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-5 items-center gap-2 mb-3">
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Sort
                        </label>
                        <div className="w-3/5">
                          <select
                            id="Sort"
                            value={planData?.Sort || ""}
                            onChange={handlePlanInputChange}
                            className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                          >
                            <option value="Plan_Process_Date">
                              Plan_Process_Date
                            </option>
                            <option value="Product_Delivery">
                              Product_Delivery
                            </option>
                          </select>
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Mark_Days
                        </label>
                        <div className="w-3/5">
                          <DatePicker
                            selected={selectedDates.Mark_Days}
                            onChange={(date) => handleChange(date, "Mark_Days")}
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex justify-center gap-5">
                        <div className="flex gap-2 items-center">
                          <input
                            id="Color_Separate"
                            checked={planData.Color_Separate ?? true} // ค่าเริ่มต้นเป็น true
                            onChange={handlePlanInputChange}
                            type="checkbox"
                            className="w-6 h-6"
                          />
                          <label className="text-xs font-medium">
                            Color_Separate
                          </label>
                        </div>
                        <div className="flex gap-2 items-center">
                          <input
                            id="Info_View"
                            checked={planData.Info_View ?? true}
                            onChange={handlePlanInputChange}
                            type="checkbox"
                            className="w-6 h-6"
                          />
                          <label className="text-xs font-medium">
                            Info_View
                          </label>
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Re_Process
                        </label>
                        <div className="w-3/5">
                          <input
                            id="Re_Pr_Qty"
                            value={planData?.Re_Pr_Qty || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <label className="text-xs font-medium w-2/5 text-end">
                          Re_Total_Net
                        </label>
                        <div className="w-3/5">
                          <input
                            id="Re_Total_N_Time"
                            value={planData?.Re_Total_N_Time || 0}
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full overflow-x-auto pr-10">
                <div className="min-w-[1600px] w-full">
                  <div className="grid grid-cols-12 mx-5 py-2">
                    <div className="flex items-start">
                      <label className="text-xs font-bold">
                        Order_Info_Search
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-1">
                      <div className="grid grid-cols-5 gap-2 items-center mb-3">
                        <div className="flex gap-2">
                          <label className="font-medium text-xs w-2/5">
                            Product_Grp
                          </label>
                          <div className="w-3/5 flex gap-1 justify-between">
                            <input
                              id="Product_Grp_CD"
                              value={orderData?.Product_Grp_CD || ""}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                            <input
                              id="Product_Grp_Name"
                              value={selectedWorkGName || ""}
                              onChange={(event) => setWorkergData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-3 items-center">
                          <div className="flex gap-2 items-center justify-center">
                            <input type="checkbox" className="w-6 h-6" />
                            <label className="text-xs font-medium">
                              Od_Pending
                            </label>
                          </div>
                          <div className="flex gap-2 items-center">
                            <label className="text-xs font-medium w-2/5 text-end">
                              Ctl_Person
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Od_Ctl_Person"
                                value={personName || ""}
                                onChange={(event) => setWorkerData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center">
                            <label className="text-xs font-medium w-2/5 text-end">
                              Sales_Person
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Sales_Person"
                                value={selectedSalesPersonAbb}
                                onChange={(event) => setWorkerData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <label className="text-xs font-medium w-2/5 text-end">
                            Specific
                          </label>
                          <div className="flex gap-2 w-3/5">
                            <select
                              id="Specific_CD"
                              value={orderData?.Specific_CD || ""}
                              onChange={handleOrderInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={orderData?.Specific_CD || ""}>
                                {orderData?.Specific_CD || ""}
                              </option>
                              {Array.isArray(SpecificData) &&
                              SpecificData.length > 0 ? (
                                SpecificData.map((item, index) => (
                                  <option key={index} value={item.Specific_CD}>
                                    {item.Specific_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Specific_Name"
                              value={SpecificName || ""}
                              onChange={(event) => setSpecificData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 gap-2 items-center mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Received
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Pd_Received_Date"
                              value={formatDateTime(
                                orderData?.Pd_Received_Date || ""
                              )}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-4 gap-2 items-center ">
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Customer
                            </label>
                            <div className="w-3/5 flex justify-between gap-2">
                              <input
                                id="Customer_CD"
                                value={orderData?.Customer_CD || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                              <input
                                id="Customer_CD_Name"
                                value={selectedCustomerAbb || ""}
                                onChange={(event) => setCustomerData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Qty
                            </label>
                            <div className="w-3/5 flex justify-between gap-2">
                              <input
                                id="Quantity"
                                value={orderData?.Quantity || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                              <input
                                id="Unit_CD_Name"
                                value={unitName || ""}
                                onChange={(event) => setUnitData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Price
                            </label>
                            <div className="w-3/5 flex justify-between gap-2">
                              <input
                                id="Price_Name"
                                value={PriceName || ""}
                                onChange={(event) => setPriceData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                              <input
                                id="Unit_Price"
                                value={orderData?.Unit_Price || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Supply
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Supply_CD_Name"
                                value={supplyName || ""}
                                onChange={(event) => setSupplyData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Progress
                          </label>
                          <div className="w-3/5 flex justify-between gap-2">
                            <select
                              id="Od_Progress_CD"
                              value={orderData?.Od_Progress_CD || ""}
                              onChange={handleOrderInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={orderData?.Od_Progress_CD || ""}>
                                {orderData?.Od_Progress_CD || ""}
                              </option>
                              {Array.isArray(OdProgressData) &&
                              OdProgressData.length > 0 ? (
                                OdProgressData.map((item, index) => (
                                  <option
                                    key={index}
                                    value={item.Od_Progress_CD}
                                  >
                                    {item.Od_Progress_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Od_Progress_Name"
                              value={OdProgressName || ""}
                              onChange={(event) => setOdProgressData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 gap-2 items-center mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Request
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Request_Delivery"
                              value={formatDateTime(
                                orderData?.Request_Delivery || ""
                              )}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-4 items-center gap-2">
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Product
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Product_Name"
                                value={orderData?.Product_Name || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Req3
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Request3_CD"
                                value={orderData?.Request3_CD || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Coating
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Coating_Name"
                                value={coatingName || "none"}
                                onChange={(event) => setCoatingData(event)}
                                type="text"
                                className="bg-[#ffff00] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Detail
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Coating"
                                value={orderData?.Coating || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Delivery
                          </label>
                          <div className="w-3/5 flex justify-between gap-2">
                            <select
                              id="Delivery_CD"
                              value={orderData?.Delivery_CD || ""}
                              onChange={handleOrderInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={orderData?.Delivery_CD || ""}>
                                {orderData?.Delivery_CD || ""}
                              </option>
                              {Array.isArray(DeliveryData) &&
                              DeliveryData.length > 0 ? (
                                DeliveryData.map((item, index) => (
                                  <option key={index} value={item.Delivery_CD}>
                                    {item.Delivery_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Delivery_Name"
                              value={DeliveryName || ""}
                              onChange={(event) => setDeliveryData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 gap-2 items-center mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs ">
                            Product
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Product_Delivery"
                              value={formatDateTime(
                                orderData?.Product_Delivery || ""
                              )}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-3 items-center gap-2">
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Size
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Product_Size"
                                value={orderData?.Product_Size || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Mate1
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Material1"
                                value={orderData?.Material1 || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              P_Docu
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Product_Docu"
                                value={orderData?.Product_Docu || ""}
                                onChange={handleOrderInputChange}
                                onClick={() =>
                                  handlePathClick(orderData?.Product_Docu)
                                }
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Target
                          </label>
                          <div className="w-3/5 flex justify-between gap-2">
                            <select
                              id="Target_CD"
                              value={orderData?.Target_CD || ""}
                              onChange={handleOrderInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={orderData?.Target_CD || ""}>
                                {orderData?.Target_CD || ""}
                              </option>
                              {Array.isArray(TargetData) &&
                              TargetData.length > 0 ? (
                                TargetData.map((item, index) => (
                                  <option key={index} value={item.Target_CD}>
                                    {item.Target_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Target_Name"
                              value={targetName || ""}
                              onChange={(event) => setTargetData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 gap-2 items-center mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs ">
                            Confirm
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Confirm_Delivery"
                              value={formatDateTime(
                                orderData?.Confirm_Delivery || ""
                              )}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-3 gap-2 items-center">
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Draw
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Product_Draw"
                                value={orderData?.Product_Draw || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Mate2
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Material2"
                                value={orderData?.Material2 || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              S_Docu
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Supple_Docu"
                                value={orderData?.Supple_Docu || ""}
                                onChange={handleOrderInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Pd_Complete
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Pd_Complete_Date"
                              value={orderData?.Pd_Complete_Date || ""}
                              onChange={handleOrderInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full mt-5 overflow-x-auto pr-10">
                <div className="min-w-[1800px] w-full">
                  <div className="grid grid-cols-12 py-2">
                    <div className="flex items-start ml-7">
                      <label className="text-xs font-bold">Parts_Info</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-1 -ml-1">
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Parts_No
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Parts_No"
                              value={planData?.Parts_No || ""}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-3 gap-2">
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Order_Parts_No
                            </label>
                            <div className="w-3/5">
                              <input
                                id="OdPt_No"
                                value={planData?.OdPt_No || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-center items-center gap-2">
                            <input
                              id="Pt_Pending"
                              value={planData?.Pt_Pending}
                              onChange={handlePlanInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                            <label className="w-2/5 font-medium text-xs">
                              Pt_Pending
                            </label>
                          </div>
                          <div className="flex justify-between items-center gap-2">
                            <label className="w-2/5 font-medium text-xs text-end">
                              Pl_Req_Person
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Pl_Reg_Person_Name"
                                value={Person_Name || ""}
                                onChange={(event) => setWorkerData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Pl_Ed_Rev
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Pl_Ed_Rev_Day"
                              value={planData?.Pl_Ed_Rev_Day || ""}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Pt_Name
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Parts_Name"
                              value={PartName || ""}
                              onChange={(event) => selectPartsData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 flex justify-between items-center gap-2">
                          <div className="flex gap-2 items-center">
                            <label className="text-xs font-medium w-2/5">
                              Pt_Mate
                            </label>
                            <div className="w-4/5">
                              <input
                                id="Pt_Material"
                                value={planData?.Pt_Material || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center">
                            <label className="text-xs font-medium ml-5">
                              Pt_Qty
                            </label>
                            <div className="w-24">
                              <input
                                id="Pt_Qty"
                                value={planData?.Pt_Qty || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <div className="w-24">
                              <input
                                id="Pt_Unit"
                                value={unitName || ""}
                                onChange={(event) => setUnitData(event)}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-center gap-2 items-center w-2/12">
                            <input
                              id="Pt_Split"
                              value={planData?.Pt_Split}
                              checked={planData?.Pt_Split}
                              onChange={handlePlanInputChange}
                              type="checkbox"
                              className="h-6 w-6"
                            />
                            <label className="text-xs font-medium">Split</label>
                          </div>
                          <div className="fle x justify-between gap-1 items-center w-2/12">
                            <label className="text-xs font-medium w-1/5">
                              Spare
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Pt_Spare_Qty"
                                value={planData?.Pt_Spare_Qty || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between gap-2 items-center w-2/12">
                            <label className="text-xs font-medium w-1/5">
                              NG
                            </label>
                            <div className="w-4/5">
                              <input
                                id="Pt_NG_Qty"
                                value={planData?.Pt_NG_Qty || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between gap-2 items-center w-5/12">
                            <label className="text-xs font-medium w-4/12">
                              Connect_Od_No
                            </label>
                            <div className="w-7/12">
                              <input
                                id="Connect_Od_No"
                                value={planData?.Connect_Od_No || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <label className="text-xs font-medium w-2/5 text-end">
                            Progress
                          </label>
                          <div className="flex gap-2 w-3/5">
                            <select
                              id="Pl_Progress_CD"
                              value={planData?.Pl_Progress_CD || ""}
                              onChange={handlePlanInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff00] w-full"
                            >
                              <option value={planData?.Pl_Progress_CD || ""}>
                                {planData?.Pl_Progress_CD || ""}
                              </option>
                              {Array.isArray(plprogressData) &&
                              plprogressData.length > 0 ? (
                                plprogressData.map((item, index) => (
                                  <option
                                    key={index}
                                    value={item.Pl_Progress_CD}
                                  >
                                    {item.Pl_Progress_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Pl_Progress_Name"
                              value={ProgressName || ""}
                              onChange={(event) => setPlProgressData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Parts_Deli
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Pt_Delivery"
                              value={formatDateTime(
                                planData?.Pt_Delivery || ""
                              )}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-2 items-center gap-2">
                          <div className="flex justify-start items-center gap-2 ml-32">
                            <label className="font-medium text-xs w-2/12">
                              Pt_Note
                            </label>
                            <div className="w-40">
                              <input
                                id="Pt_Instructions"
                                value={planData?.Pt_Instructions || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-start items-center gap-2">
                            <label className="font-medium text-xs w-auto">
                              Connect_Pt_No
                            </label>
                            <div className="w-24">
                              <input
                                id="Connect_Pt_No"
                                value={planData?.Connect_Pt_No || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <div className="w-24">
                              <input
                                id="Connect_Pt_Name"
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <label className="text-xs font-medium w-2/5 text-end items-center mt-1">
                            Schedule
                          </label>
                          <div className="flex gap-2 w-3/5">
                            <select
                              id="Pl_Schedule_CD"
                              value={planData?.Pl_Schedule_CD || ""}
                              onChange={handlePlanInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={planData?.Pl_Schedule_CD || ""}>
                                {planData?.Pl_Schedule_CD || ""}
                              </option>
                              {Array.isArray(ScheduleData) &&
                              ScheduleData.length > 0 ? (
                                ScheduleData.map((item, index) => (
                                  <option key={index} value={item.Schedule_CD}>
                                    {item.Schedule_CD}
                                  </option>
                                ))
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                            <input
                              id="Pl_Schedule_Name"
                              value={Schedule_Name || ""}
                              onChange={(event) => setScheduleData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Plan_Reg
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Pl_Reg_Date"
                              value={formatDateTime(
                                planData?.Pl_Reg_Date || ""
                              )}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-3 grid grid-cols-2 items-center gap-2">
                          <div className="flex gap-2 items-center justify-start ml-32">
                            <label className="w-2/12 font-medium text-xs">
                              Pt_Info
                            </label>
                            <div className="w-40">
                              <input
                                id="Pt_Information"
                                value={planData?.Pt_Information || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center justify-start">
                            <label className="text-xs font-medium w-auto text-start">
                              Connect_Pr_No
                            </label>
                            <div className="w-24">
                              <input
                                id="Connect_Pr_No"
                                value={planData?.Connect_Pr_No || ""}
                                onChange={handlePlanInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <div className="w-24">
                              <input
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <label className="w-2/5 font-medium text-xs text-end">
                            Pt_Complete
                          </label>
                          <div className="w-3/5">
                            <input
                              id="Pt_Complete"
                              value={planData?.Pt_Complete || ""}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Plan_Upd
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Pl_Upd_Date"
                              value={formatDateTime(
                                planData?.Pl_Upd_Date || ""
                              )}
                              onChange={handlePlanInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-4 flex justify-between items-center gap-2">
                          <label className="font-medium text-xs">Info</label>
                          {inputs4.map((id) => (
                            <div key={id} className="flex gap-1 items-center">
                              <label className="font-medium text-xs">
                                {id}
                              </label>
                              <div className="flex gap-2 pr-5">
                                <input
                                  type="text"
                                  className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                                />
                                <input
                                  id={`Info_Chk${id}`}
                                  value={planData?.[`Info_Chk${id}`] || ""}
                                  onChange={handlePlanInputChange}
                                  type="checkbox"
                                  className="h-6 w-6"
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="grid grid-cols-5 items-center gap-2 mb-3">
                        <div className="flex gap-2">
                          <label className="w-2/5 font-medium text-xs">
                            Order_No
                          </label>
                          <div className="w-40 -ml-4">
                            <input
                              id="Order_No"
                              value={ResultData?.Order_No || ""}
                              onChange={handleResultInputChange}
                              type="text"
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        <div className="col-span-4 flex items-center gap-2">
                          <div className="flex gap-2 items-center w-2/12">
                            <label className="font-medium text-xs">
                              Parts_No
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Parts_No"
                                value={ResultData?.Parts_No || ""}
                                onChange={handleResultInputChange}
                                type="text"
                                className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center w-2/12 mt-3">
                            <label className="font-medium text-xs">
                              Target_No
                            </label>
                            <div className="w-3/5">
                              <input
                                id="Target_Pr_No"
                                value={targetNo}
                                onChange={(e) => {
                                  // อัปเดต state ของ targetNo และส่ง event ไปที่ handleResultInputChange
                                  setTargetNo(e.target.value);
                                  handleResultInputChange(e);
                                }}
                                type="text"
                                className="bg-[#FFCC99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center w-2/12">
                            <label className="font-medium text-xs">
                              MV_Range
                            </label>
                            <div className="w-3/5">
                              <select
                                id="Move_Range"
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full"
                                defaultValue="Plan_Exclude"
                              >
                                <option value="Plan_Exclude">
                                  Plan_Exclude
                                </option>
                                <option value="Plan_Include">
                                  Plan_Include
                                </option>
                                <option value="Plan_Exclude">
                                  Plan_Exclude
                                </option>
                                <option value="ResultDay_Only">
                                  ResultDay_Only
                                </option>
                                <option value="ResultDay_Except">
                                  ResultDay_Except
                                </option>
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2 items-center w-2/12">
                            <label className="font-medium text-xs">
                              Date_Rev_Days
                            </label>
                            <div className="w-2/5">
                              <input
                                id="Comp_Date_Rev_Days"
                                value={ResultData?.Comp_Date_Rev_Days || 0}
                                onChange={handleResultInputChange}
                                type="text"
                                className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 items-center pr-3">
                            <label className="font-medium text-xs">
                              Auto_Finish
                            </label>
                            <input
                              id="Auto_Finish"
                              checked={autoFinish === true}
                              onChange={handleResultInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          </div>
                          <div className="flex gap-2 items-center pr-3">
                            <label className="font-medium text-xs">
                              Plan_Edit
                            </label>
                            <input
                              disabled
                              id="Plan_Edit"
                              checked={ResultData?.Plan_Edit}
                              onChange={handleResultInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          </div>
                          <div className="flex gap-2 items-center">
                            <label className="font-medium text-xs">Admin</label>
                            <input
                              disabled
                              id="Admin"
                              checked={ResultData?.Admin}
                              onChange={handleAdminChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full overflow-x-auto">
                <div className="min-w-[1600px] w-full">
                  <div className="grid grid-cols-12 mx-5 pb-7">
                    <div></div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 text-center">
                      {inputs.map((id) => (
                        <label key={id} className="font-medium text-xs">
                          No{id}
                        </label>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Process_Name
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => {
                        const processKey = planData?.[`PPC${id}`];
                        const ProcessNamesForRow = (ProcessCData || [])
                          .filter(
                            (Process) => Process.Process_CD === processKey
                          )
                          .map((Process) => Process.Process_Abb)
                          .join(", ");

                        return (
                          <div key={id}>
                            <select
                              id={`PPC${id}`}
                              value={planData?.[`PPC${id}`] || ""}
                              onChange={handlePlanInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={planData?.[`PPC${id}`] || ""}>
                                {ProcessNamesForRow}
                              </option>
                              {/* เพิ่ม option อื่น ๆ ถ้ามี */}
                              {(ProcessCData || []).map((proc) => (
                                <option
                                  key={proc.Process_CD}
                                  value={proc.Process_CD}
                                >
                                  {proc.Process_Abb}
                                </option>
                              ))}
                            </select>
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_M_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PMT${id}`}
                            value={
                              planData?.[`PMT${id}`] !== undefined &&
                              planData?.[`PMT${id}`] !== null
                                ? planData?.[`PMT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_P_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PPT${id}`}
                            value={
                              planData?.[`PPT${id}`] !== undefined &&
                              planData?.[`PPT${id}`] !== null
                                ? planData?.[`PPT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Plan_Process_Date
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div key={id}>
                          <DatePicker
                            id={`PPD${id}`}
                            selected={
                              planData?.[`PPD${id}`]
                                ? new Date(planData[`PPD${id}`])
                                : null
                            }
                            onChange={(date) =>
                              handlePlanInputChange({
                                target: {
                                  id: `PPD${id}`,
                                  value: date
                                    ? date.toISOString().split("T")[0]
                                    : "",
                                },
                              })
                            }
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            disabled={!isEditable}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Date</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => {
                        // ใช้ ref สำหรับ DatePicker แต่ละตัว
                        const datePickerRef = useRef(null);

                        return (
                          <div key={id} className="relative">
                            <DatePicker
                              ref={datePickerRef}
                              id={`RPD${id}`}
                              selected={
                                ResultData?.[`RPD${id}`]
                                  ? new Date(ResultData[`RPD${id}`])
                                  : null
                              }
                              onChange={(date) =>
                                handleResultInputChange({
                                  target: {
                                    id: `RPD${id}`,
                                    value: date
                                      ? date.toISOString().split("T")[0]
                                      : "",
                                  },
                                })
                              }
                              // เมื่อได้รับ focus ให้อัปเดต targetNo ให้เป็น id นี้
                              onFocus={() => setTargetNo(id)}
                              dateFormat="yyyy/MM/dd"
                              className="bg-[#fecc99] border-solid border-2 border-gray-500 rounded-md px-1 w-full cursor-pointer"
                              popperPlacement="bottom-start"
                              portalId="root-portal"
                              disabled={!isEditable}
                            />
                            {isEditable && (
                              <AiTwotoneCalendar
                                className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-700 cursor-pointer"
                                size={18}
                                onClick={() => {
                                  // เมื่อคลิก icon ให้ set targetNo และเปิด DatePicker
                                  setTargetNo(id);
                                  datePickerRef.current.setOpen(true);
                                }}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_M(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RMT${id}`}
                            value={ResultData?.[`RMT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_P(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPT${id}`}
                            value={ResultData?.[`RPT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Qty</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPN${id}`}
                            value={
                              ResultData?.[`RPN${id}`] === 0
                                ? ""
                                : ResultData?.[`RPN${id}`] ?? ""
                            }
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#99ccff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full overflow-x-auto">
                <div className="min-w-[1600px] w-full">
                  <div className="grid grid-cols-12 mx-5 pb-7">
                    <div></div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 text-center">
                      {inputs2.map((id) => (
                        <label key={id} className="font-medium text-xs">
                          No{id}
                        </label>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Process_Name
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => {
                        const processKey = planData?.[`PPC${id}`];
                        const ProcessNamesForRow = (ProcessCData || [])
                          .filter(
                            (Process) => Process.Process_CD === processKey
                          )
                          .map((Process) => Process.Process_Abb)
                          .join(", ");

                        return (
                          <div key={id}>
                            <select
                              id={`PPC${id}`}
                              value={planData?.[`PPC${id}`] || ""}
                              onChange={handlePlanInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={planData?.[`PPC${id}`] || ""}>
                                {ProcessNamesForRow}
                              </option>
                              {/* เพิ่ม option อื่น ๆ ถ้ามี */}
                              {(ProcessCData || []).map((proc) => (
                                <option
                                  key={proc.Process_CD}
                                  value={proc.Process_CD}
                                >
                                  {proc.Process_Abb}
                                </option>
                              ))}
                            </select>
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_M_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PMT${id}`}
                            value={
                              planData?.[`PMT${id}`] !== undefined &&
                              planData?.[`PMT${id}`] !== null
                                ? planData?.[`PMT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_P_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PPT${id}`}
                            value={
                              planData?.[`PPT${id}`] !== undefined &&
                              planData?.[`PPT${id}`] !== null
                                ? planData?.[`PPT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Plan_Process_Date
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div key={id}>
                          <DatePicker
                            id={`PPD${id}`}
                            selected={
                              planData?.[`PPD${id}`]
                                ? new Date(planData[`PPD${id}`])
                                : null
                            }
                            onChange={(date) =>
                              handlePlanInputChange({
                                target: {
                                  id: `PPD${id}`,
                                  value: date
                                    ? date.toISOString().split("T")[0]
                                    : "",
                                },
                              })
                            }
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            disabled={!isEditable}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Date</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => {
                        const datePickerRef2 = useRef(null); // สร้าง ref ให้ DatePicker

                        return (
                          <div key={id} className="relative">
                            <DatePicker
                              ref={datePickerRef2}
                              id={`RPD${id}`}
                              selected={
                                ResultData?.[`RPD${id}`]
                                  ? new Date(ResultData[`RPD${id}`])
                                  : null
                              }
                              onChange={(date) =>
                                handleResultInputChange({
                                  target: {
                                    id: `RPD${id}`,
                                    value: date
                                      ? date.toISOString().split("T")[0]
                                      : "",
                                  },
                                })
                              }
                              onFocus={() => setTargetNo(id)}
                              dateFormat="yyyy/MM/dd"
                              className="bg-[#fecc99] border-solid border-2 border-gray-500 rounded-md px-1 w-full cursor-pointer"
                              popperPlacement="bottom-start"
                              portalId="root-portal"
                              disabled={!isEditable}
                            />

                            {isEditable && (
                              <AiTwotoneCalendar
                                className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-700 cursor-pointer"
                                size={18}
                                onClick={() => {
                                  // เมื่อคลิก icon ให้ set targetNo และเปิด DatePicker
                                  setTargetNo(id);
                                  datePickerRef2.current.setOpen(true);
                                }}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_M(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RMT${id}`}
                            value={ResultData?.[`RMT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_P(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPT${id}`}
                            value={ResultData?.[`RPT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Qty</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs2.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPN${id}`}
                            value={
                              ResultData?.[`RPN${id}`] === 0
                                ? ""
                                : ResultData?.[`RPN${id}`] ?? ""
                            }
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#99ccff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full overflow-x-auto">
                <div className="min-w-[1600px] w-full">
                  <div className="grid grid-cols-12 mx-5 pb-7">
                    <div></div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 text-center">
                      {inputs3.map((id) => (
                        <label key={id} className="font-medium text-xs">
                          No{id}
                        </label>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Process_Name
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => {
                        const processKey = planData?.[`PPC${id}`];
                        const ProcessNamesForRow = (ProcessCData || [])
                          .filter(
                            (Process) => Process.Process_CD === processKey
                          )
                          .map((Process) => Process.Process_Abb)
                          .join(", ");

                        return (
                          <div key={id}>
                            <select
                              id={`PPC${id}`}
                              value={planData?.[`PPC${id}`] || ""}
                              onChange={handlePlanInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-white w-full"
                            >
                              <option value={planData?.[`PPC${id}`] || ""}>
                                {ProcessNamesForRow}
                              </option>
                              {/* เพิ่ม option อื่น ๆ ถ้ามี */}
                              {(ProcessCData || []).map((proc) => (
                                <option
                                  key={proc.Process_CD}
                                  value={proc.Process_CD}
                                >
                                  {proc.Process_Abb}
                                </option>
                              ))}
                            </select>
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_M_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PMT${id}`}
                            value={
                              planData?.[`PMT${id}`] !== undefined &&
                              planData?.[`PMT${id}`] !== null
                                ? planData?.[`PMT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Plan_P_Time</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div className="flex gap-2 items-center" key={id}>
                          <input
                            disabled={!isEditable}
                            id={`PPT${id}`}
                            value={
                              planData?.[`PPT${id}`] !== undefined &&
                              planData?.[`PPT${id}`] !== null
                                ? planData?.[`PPT${id}`]
                                : ""
                            }
                            onChange={handlePlanInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                          <label className="font-medium text-xs">min/L</label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Plan_Process_Date
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div key={id}>
                          <DatePicker
                            id={`PPD${id}`}
                            selected={
                              planData?.[`PPD${id}`]
                                ? new Date(planData[`PPD${id}`])
                                : null
                            }
                            onChange={(date) =>
                              handlePlanInputChange({
                                target: {
                                  id: `PPD${id}`,
                                  value: date
                                    ? date.toISOString().split("T")[0]
                                    : "",
                                },
                              })
                            }
                            dateFormat="yyyy/MM/dd"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            disabled={!isEditable}
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Date</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => {
                        const datePickerRef3 = useRef(null); // สร้าง ref ให้ DatePicker

                        return (
                          <div key={id} className="relative">
                            <DatePicker
                              ref={datePickerRef3}
                              id={`RPD${id}`}
                              selected={
                                ResultData?.[`RPD${id}`]
                                  ? new Date(ResultData[`RPD${id}`])
                                  : null
                              }
                              onChange={(date) =>
                                handleResultInputChange({
                                  target: {
                                    id: `RPD${id}`,
                                    value: date
                                      ? date.toISOString().split("T")[0]
                                      : "",
                                  },
                                })
                              }
                              onFocus={() => setTargetNo(id)}
                              dateFormat="yyyy/MM/dd"
                              className="bg-[#fecc99] border-solid border-2 border-gray-500 rounded-md px-1 w-full cursor-pointer"
                              popperPlacement="bottom-start"
                              portalId="root-portal"
                              disabled={!isEditable}
                            />

                            {isEditable && (
                              <AiTwotoneCalendar
                                className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-700 cursor-pointer"
                                size={18}
                                onClick={() => {
                                  // เมื่อคลิก icon ให้ set targetNo และเปิด DatePicker
                                  setTargetNo(id);
                                  datePickerRef3.current.setOpen(true);
                                }}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_M(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RMT${id}`}
                            value={ResultData?.[`RMT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">
                        Rs_P(min/lot)
                      </label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPT${id}`}
                            value={ResultData?.[`RPT${id}`] ?? ""}
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#ccffcc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                    <div className="mt-3">
                      <label className="font-medium text-xs">Result_Qty</label>
                    </div>
                    <div className="col-span-11 grid grid-cols-12 gap-2 mt-3">
                      {inputs3.map((id) => (
                        <div key={id}>
                          <input
                            disabled={!isEditable}
                            id={`RPN${id}`}
                            value={
                              ResultData?.[`RPN${id}`] === 0
                                ? ""
                                : ResultData?.[`RPN${id}`] ?? ""
                            }
                            onChange={handleResultInputChange}
                            type="text"
                            className="bg-[#99ccff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 gap-4">
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F1}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F1
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Search <br /> 検索 (F1)
              </button>
              <button
                disabled={!buttonState.F2}
                onClick={handleF2Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F2
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Edit <br /> 編集(F2)
              </button>
              <button
                disabled={!buttonState.F3}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F3
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Finish <br /> 完了(F3)
              </button>
              <button
                disabled={!buttonState.F4}
                onClick={handleF4Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F4
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                PDS <br /> Input (F4)
              </button>
            </div>

            <div className="grid grid-cols-4 gap-2">
              <button
                onClick={handleF5Click}
                disabled={!buttonState.F5}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F5
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Re_WIP <br /> 仕戻(F5)
              </button>
              <button
                disabled={!buttonState.F6}
                onClick={handleF6Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F6
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Graph <br /> 山積(F6)
              </button>
              <button
                disabled={!buttonState.F7}
                onClick={handleF7Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F7
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                List <br /> 一覧 (F7)
              </button>
              <button
                disabled={!buttonState.F8}
                onClick={handleF8Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F8
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                NextParts <br /> 別部(F8)
              </button>
            </div>

            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F9}
                onClick={handleF9Click}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F9
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Save <br /> 登録(F9)
              </button>
              <button
                onClick={handleF10Click}
                disabled={!buttonState.F10}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F10
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Next_MI <br /> 連番(F10)
              </button>
              <button
                onClick={handleF11Click}
                disabled={!buttonState.F11}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F11
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                NextInput <br /> 次へ(F11)
              </button>
              <button
                onClick={handleF12Click}
                disabled={!buttonState.F12}
                className={`p-3 rounded-lg font-medium ${
                  buttonState.F12
                    ? "bg-blue-500 hover:bg-blue-700 text-white"
                    : "bg-gray-300 cursor-not-allowed text-gray-500"
                }`}
              >
                Exit <br /> 終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
const searchPermission = (status) => {
  document.getElementById("Search_Order_No").disabled = !status;
  document.getElementById("Search_Parts_No").disabled = !status;
};
