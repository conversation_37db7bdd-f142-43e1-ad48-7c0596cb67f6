import React, { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import axios from "axios";

export function FG_SUM_Date() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);

  const fetchData = async () => {
    try {
      // ดึงข้อมูลจาก localStorage
      const storedData = localStorage.getItem("fgSumTable");
      const parsedData = storedData ? JSON.parse(storedData) : null;
  
      // ตรวจสอบว่า parsedData มี QD_Order_FG หรือไม่
      const requestData = parsedData && parsedData.QD_Order_FG ? parsedData.QD_Order_FG : [];
  
      console.log("Sending data to server:", requestData);
  
      // ตรวจสอบว่า requestData มีข้อมูลหรือไม่ ก่อนส่ง
      if (requestData.length === 0) {
        console.log("No data to send.");
        return;
      }
  
      // ส่งคำขอไปยังเซิร์ฟเวอร์พร้อมข้อมูล
      const response = await axios.post(
        `${apiUrl_4000}/fg-sum/fg-sum-date`,
        { fgSumTable: requestData },  // ส่งข้อมูลที่อยู่ใน QD_Order_FG
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
  
      console.log("Received response:", response.data);
  
      // ตรวจสอบสถานะจากการตอบกลับของเซิร์ฟเวอร์
      if (response.data.status === "success") {
        setData(response.data.data); // ใช้ข้อมูลที่ได้จากเซิร์ฟเวอร์โดยตรง
      } else {
        console.error("Error: Server returned failure status.");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
  
  useEffect(() => {
    fetchData();
  }, []);  

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const columns = [
    { name: "Product_Grp_CD", selector: (row) => row.Product_Grp_CD, width: "180px" },
    { name: "Comp_Date", selector: (row) => row.Comp_Date, width: "180px" },
    { name: "Actual", selector: (row) => row.Actual, width: "180px" },
  ];

  return (
    <>
      <div className="flex items-center justify-center py-5">
        <span className="text-xl font-semibold">FG_SUM_Date</span>
      </div>

      <div className="ml-5 text-lg flex justify-between">
        <input
          className="border-2 border-gray-500 rounded-md w-52 h-9"
          type="text"
          placeholder=" Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <div className="flex justify-left items-center mt-5 mb-3">
        <div className="w-full sm:w-auto text-center px-5">
          <DataTable
            columns={columns}
            data={filteredData}
            pagination
            paginationPerPage={5}
            paginationRowsPerPageOptions={[5, 10, 15, 20]}
            customStyles={{
              rows: {
                style: {
                  "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                  "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                  minHeight: "50px",
                  textAlign: "center",
                  justifyContent: "center",
                  borderBottom: "1px solid #ccc",
                  borderRight: "1px solid #ccc",
                },
              },
              headCells: {
                style: {
                  backgroundColor: "#DCDCDC",
                  fontSize: "14px",
                  textAlign: "center",
                  justifyContent: "center",
                  border: "1px solid #ccc",
                },
              },
              cells: {
                style: {
                  textAlign: "center",
                  justifyContent: "center",
                  border: "1px solid #ccc",
                },
              },
              table: {
                style: {
                  borderCollapse: "collapse",
                },
              },
            }}
          />
        </div>
      </div>
    </>
  );
}
