{"name": "tenkei-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.7", "chart.js": "^4.4.6", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jspdf": "^3.0.0", "jspdf-autotable": "^3.8.4", "papaparse": "^5.4.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-data-table-component": "^7.6.2", "react-datepicker": "^8.1.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-select": "^5.9.0", "sweetalert2": "^11.6.13"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.44", "tailwindcss": "^3.4.10", "vite": "^5.4.11"}}