import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import <PERSON> from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function None_FG_Data() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([
    {
      Order_No: "ORD001",
      Parts_No: "PART001",
      Cost_No: "COST001",
      Process_No: "PROC001",
      OdPt_No: "ODPT001",
      OdPtCs_No: "ODPTCS001",
      OdPtPr_No: "ODPTPR001",
      CMC: "CMC001",
      CMT: 100,
      CPC: "CPC001",
      CPT: 10,
      CPD: "CPD001",
      CPN: 80,
      Cs_Progress_CD: "P001",
      Cs_Complete_Date: "2024-11-01",
      Cs_Complete_Qty: 150,
      Cs_Label_CSV: "Label001",
      Cs_All_Complete: true,
      Cs_Order_All_Complete: true,
      Cs_Parts_Complete: true,
      Cs_Final_Complete: false,
      Cs_Remark: "First Order",
      Cs_Register_Date: "2024-10-01",
      Cs_Modify_Date: "2024-11-15",
      Cs_Reg_Person_CD: "USER001",
      Cs_Upd_Person_CD: "USER002",
      Sequence_No: 1,
      ProcessCD: "P001",
      Comp_Month: "2024-11",
      Amount: 1500.0,
    },
    {
      Order_No: "ORD002",
      Parts_No: "PART002",
      Cost_No: "COST002",
      Process_No: "PROC002",
      OdPt_No: "ODPT002",
      OdPtCs_No: "ODPTCS002",
      OdPtPr_No: "ODPTPR002",
      CMC: "CMC002",
      CMT: 50,
      CPC: "CPC002",
      CPT: 100,
      CPD: "CPD002",
      CPN: 30,
      Cs_Progress_CD: "P002",
      Cs_Complete_Date: "2024-11-05",
      Cs_Complete_Qty: 200,
      Cs_Label_CSV: "Label002",
      Cs_All_Complete: false,
      Cs_Order_All_Complete: false,
      Cs_Parts_Complete: true,
      Cs_Final_Complete: false,
      Cs_Remark: "Second Order",
      Cs_Register_Date: "2024-10-05",
      Cs_Modify_Date: "2024-11-17",
      Cs_Reg_Person_CD: "USER003",
      Cs_Upd_Person_CD: "USER004",
      Sequence_No: 2,
      ProcessCD: "P002",
      Comp_Month: "2024-11",
      Amount: 2000.0,
    },
    {
      Order_No: "ORD003",
      Parts_No: "PART003",
      Cost_No: "COST003",
      Process_No: "PROC003",
      OdPt_No: "ODPT003",
      OdPtCs_No: "ODPTCS003",
      OdPtPr_No: "ODPTPR003",
      CMC: "CMC003",
      CMT: 10,
      CPC: "CPC003",
      CPT: 90,
      CPD: "CPD003",
      CPN: 20,
      Cs_Progress_CD: "P003",
      Cs_Complete_Date: "2024-11-10",
      Cs_Complete_Qty: 300,
      Cs_Label_CSV: "Label003",
      Cs_All_Complete: true,
      Cs_Order_All_Complete: true,
      Cs_Parts_Complete: false,
      Cs_Final_Complete: true,
      Cs_Remark: "Third Order",
      Cs_Register_Date: "2024-10-10",
      Cs_Modify_Date: "2024-11-18",
      Cs_Reg_Person_CD: "USER005",
      Cs_Upd_Person_CD: "USER006",
      Sequence_No: 3,
      ProcessCD: "P003",
      Comp_Month: "2024-11",
      Amount: 3000.0,
    },
  ]);
  const [editedData, setEditedData] = useState({});
  const [isChanged, setIsChanged] = useState(false);
  const editedDataRef = useRef(editedData);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();

  // const fetchCost = async () => {
  //   try {
  //     const response = await axios.get(`${apiUrl_4000}/cost/fetch-cost`);
  //     const formattedData = response.data.data.cost.map((row) => ({
  //       ...row,
  //       CPD: formatDateForInput(row.CPD),
  //       Cs_Complete_Date: formatDateForInput(row.Cs_Complete_Date),
  //       Cs_Register_Date: formatDateForInput(row.Cs_Register_Date),
  //       Cs_Modify_Date: formatDateForInput(row.Cs_Modify_Date),
  //     }));
  //     // console.log("Fetched data:", response.data);
  //     setData(formattedData);
  //   } catch (error) {
  //     // console.error("Error fetching cost:", error);
  //   }
  // };

  // useEffect(() => {
  //   fetchCost();
  // }, []);

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row) => {
      if (!editedData[row.Cost_No]) {
        acc[row.Cost_No] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    if (isNaN(date)) return "";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  };

  const handleChange = (e, costNo, field) => {
    const newValue = e.target.value;

    if (editedDataRef.current[costNo]?.[field] !== newValue) {
      setIsChanged(true);

      const updatedData = { ...editedDataRef.current };

      updatedData[costNo] = updatedData[costNo] || {};
      updatedData[costNo][field] = newValue;

      setEditedData(updatedData);
      editedDataRef.current = updatedData;
    }
  };

  // const handleSave = async (costNo, field) => {
  //   const newValue = editedData[costNo]?.[field];
  //   const oldValue = data.find((row) => row.Cost_No === costNo)?.[field];

  //   if (newValue !== oldValue) {
  //     try {
  //       // หาข้อมูล `Order_No` และ `Parts_No` ที่เกี่ยวข้องกับ `costNo`
  //       const rowData = data.find((row) => row.Cost_No === costNo);

  //       // สร้าง payload ที่ส่งค่า `Order_No`, `Parts_No`, และ `Cost_No` เสมอ
  //       const payload = {
  //         Order_No: rowData?.Order_No, // ใช้ข้อมูลเดิมจาก row
  //         Parts_No: rowData?.Parts_No,
  //         Cost_No: costNo,
  //         [field]: newValue === "" ? null : newValue, // ฟิลด์ที่ต้องการอัปเดต
  //       };

  //       console.log("Payload to be sent:", payload);

  //       // ส่ง request ไปยัง backend
  //       const response = await axios.put(
  //         `${apiUrl_4000}/cost/update-cost`,
  //         payload
  //       );

  //       // อัปเดตข้อมูลใน frontend
  //       const updatedData = [...data];
  //       const rowIndex = updatedData.findIndex((row) => row.Cost_No === costNo);
  //       if (rowIndex !== -1) {
  //         updatedData[rowIndex][field] = newValue;
  //         setData(updatedData);
  //       }

  //       alert("Edit Successfully!");
  //       setIsChanged(false);
  //     } catch (error) {
  //       alert("Something went wrong!");
  //       console.error(error);
  //     }
  //   }
  // };

  // const handleKeyDown = (e, index, field) => {
  //   if (e.key === "Enter") {
  //     handleSave(index, field);
  //     setIsChanged(false);
  //   }
  // };

  const handleCheckboxChange = (e, row, field) => {
    const isChecked = e.target.checked;

    // console.log(`Row Cost_No: ${row.Cost_No}, Field: ${field}, Checked: ${isChecked}`);

    // setData((prevData) =>
    //   prevData.map((item) =>
    //     item.Cost_No === row.Cost_No ? { ...item, [field]: isChecked } : item
    //   )
    // );
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleBackToAdminClick = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to admin?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า admin หรือไม่?<br>データは編集されました。admin に戻りますか？"
          : "Do you want to go back to admin?<br>คุณต้องการกลับไปที่หน้า admin หรือไม่?<br>admin に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/admin-menu");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "เกิดข้อผิดพลาด",
        text: "กรุณาลองอีกครั้ง",
        icon: "error",
        confirmButtonText: "ตกลง",
      });
    }
  };

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Order_No: row.Order_No,
      Parts_No: row.Parts_No,
      Cost_No: row.Cost_No,
      Process_No: row.Process_No,
      OdPt_No: row.OdPt_No,
      OdPtCs_No: row.OdPtCs_No,
      OdPtPr_No: row.OdPtPr_No,
      CMC: row.CMC,
      CMT: row.CMT,
      CPC: row.CPC,
      CPT: row.CPT,
      CPD: row.CPD,
      CPN: row.CPN,
      Cs_Progress_CD: row.Cs_Progress_CD,
      Cs_Complete_Date: row.Cs_Complete_Date,
      Cs_Complete_Qty: row.Cs_Complete_Qty,
      Cs_Label_CSV: row.Cs_Label_CSV,
      Cs_All_Complete: row.Cs_All_Complete,
      Cs_Order_All_Complete: row.Cs_Order_All_Complete,
      Cs_Parts_Complete: row.Cs_Parts_Complete,
      Cs_Final_Complete: row.Cs_Final_Complete,
      Cs_Remark: row.Cs_Remark,
      Cs_Register_Date: row.Cs_Register_Date,
      Cs_Modify_Date: row.Cs_Modify_Date,
      Cs_Reg_Person_CD: row.Cs_Reg_Person_CD,
      Cs_Upd_Person_CD: row.Cs_Upd_Person_CD,
      Sequence_No: row.Sequence_No,
      ProcessCD: row.ProcessCD,
      Comp_Month: row.Comp_Month,
      Amount: row.Amount,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "None_FG_Data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns = [
    {
      name: "Order_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Order_No ?? row.Order_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Order_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Order_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Parts_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Parts_No ?? row.Parts_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Parts_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Parts_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Cost_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Cost_No ?? row.Cost_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Cost_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cost_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Process_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Process_No ?? row.Process_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Process_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Process_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "OdPt_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.OdPt_No ?? row.OdPt_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "OdPt_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "OdPt_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "OdPtCs_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.OdPtCs_No ?? row.OdPtCs_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "OdPtCs_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "OdPtCs_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "OdPtPr_No",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.OdPtPr_No ?? row.OdPtPr_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "OdPtPr_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "OdPtPr_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "CMC",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.CMC ?? row.CMC ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "CMC")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CMC")}
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "CMT",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="number"
          value={editedData[row.Cost_No]?.CMT ?? row.CMT ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "CMT")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CMT")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "CPC",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.CPC ?? row.CPC ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "CPC")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CPC")}
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "CPT",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="number"
          value={editedData[row.Cost_No]?.CPT ?? row.CPT ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "CPT")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CPT")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "CPD",
      selector: (row) => {
        const date = row.CPD ? new Date(row.CPD) : null;
        if (!date || isNaN(date)) return "";

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear() + 543;

        return `${day}/${month}/${year}`;
      },
      width: "170px",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="date"
          value={
            editedData[row.Cost_No]?.CPD ?? formatDateForInput(row.CPD) ?? ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "CPD")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CPD")}
          disabled
        />
      ),
    },
    {
      name: "CPN",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="number"
          value={editedData[row.Cost_No]?.CPN ?? row.CPN ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "CPN")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "CPN")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Cs_Progress_CD",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={
            editedData[row.Cost_No]?.Cs_Progress_CD ?? row.Cs_Progress_CD ?? ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Progress_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Progress_CD")}
          disabled
        />
      ),
      width: "180px",
    },
    {
      name: "Cs_Complete_Date",
      selector: (row) => {
        const date = row.Cs_Complete_Date
          ? new Date(row.Cs_Complete_Date)
          : null;
        if (!date || isNaN(date)) return "";

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear() + 543;

        return `${day}/${month}/${year}`;
      },
      width: "170px",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="date"
          value={
            editedData[row.Cost_No]?.Cs_Complete_Date ??
            formatDateForInput(row.Cs_Complete_Date) ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Complete_Date")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Complete_Date")}
          disabled
        />
      ),
    },
    {
      name: "Cs_Complete_Qty",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="number"
          value={
            editedData[row.Cost_No]?.Cs_Complete_Qty ??
            row.Cs_Complete_Qty ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Complete_Qty")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Complete_Qty")}
          disabled
        />
      ),
      width: "180px",
    },
    {
      name: "Cs_Label_CSV",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Label_CSV ?? false}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Cs_Label_CSV")}
          className="mx-auto"
          disabled
        />
      ),
      width: "180px",
    },
    {
      name: "Cs_All_Complete",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_All_Complete ?? false}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Cs_All_Complete")}
          className="mx-auto"
          disabled
        />
      ),
      width: "180px",
    },
    {
      name: "Cs_Order_All_Complete",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Order_All_Complete ?? false}
          style={{ pointerEvents: "none" }}
          onChange={(e) =>
            handleCheckboxChange(e, row, "Cs_Order_All_Complete")
          }
          className="mx-auto"
          disabled
        />
      ),
      width: "220px",
    },
    {
      name: "Cs_Parts_Complete",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Parts_Complete ?? false}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Cs_Parts_Complete")}
          className="mx-auto"
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "Cs_Final_Complete",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Final_Complete ?? false}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Cs_Final_Complete")}
          className="mx-auto"
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "Cs_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Cs_Remark ?? row.Cs_Remark ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Remark")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Remark")}
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "Cs_Register_Date",
      selector: (row) => {
        const date = row.Cs_Register_Date
          ? new Date(row.Cs_Register_Date)
          : null;
        if (!date || isNaN(date)) return "";

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear() + 543;

        return `${day}/${month}/${year}`;
      },
      width: "170px",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="date"
          value={
            editedData[row.Cost_No]?.Cs_Register_Date ??
            formatDateForInput(row.Cs_Register_Date) ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Register_Date")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Register_Date")}
          disabled
        />
      ),
    },
    {
      name: "Cs_Modify_Date",
      selector: (row) => {
        const date = row.Cs_Modify_Date ? new Date(row.Cs_Modify_Date) : null;
        if (!date || isNaN(date)) return "";

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear() + 543;

        return `${day}/${month}/${year}`;
      },
      width: "170px",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="date"
          value={
            editedData[row.Cost_No]?.Cs_Modify_Date ??
            formatDateForInput(row.Cs_Modify_Date) ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Modify_Date")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Modify_Date")}
          disabled
        />
      ),
    },
    {
      name: "Cs_Reg_Person_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={
            editedData[row.Cost_No]?.Cs_Reg_Person_CD ??
            row.Cs_Reg_Person_CD ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Reg_Person_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Reg_Person_CD")}
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "Cs_Upd_Person_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={
            editedData[row.Cost_No]?.Cs_Upd_Person_CD ??
            row.Cs_Upd_Person_CD ??
            ""
          }
          onChange={(e) => handleChange(e, row.Cost_No, "Cs_Upd_Person_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Cs_Upd_Person_CD")}
          disabled
        />
      ),
      width: "200px",
    },
    {
      name: "Sequence_No",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="number"
          value={editedData[row.Cost_No]?.Sequence_No ?? row.Sequence_No ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Sequence_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Sequence_No")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "ProcessCD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.ProcessCD ?? row.ProcessCD ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "ProcessCD")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "ProcessCD")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Comp_Month",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Comp_Month ?? row.Comp_Month ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Comp_Month")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Comp_Month")}
          disabled
        />
      ),
      width: "150px",
    },
    {
      name: "Amount",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500
"
          type="text"
          value={editedData[row.Cost_No]?.Amount ?? row.Amount ?? ""}
          onChange={(e) => handleChange(e, row.Cost_No, "Amount")}
          onKeyDown={(e) => handleKeyDown(e, row.Cost_No, "Amount")}
          disabled
        />
      ),
      width: "150px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[150vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <div className="flex flex-col sm:flex-row justify-between items-center mt-5 px-5 text-base">
                <h1 className="text-2xl font-bold text-center flex-1 lg:pl-40">
                  None FG DATA In NAV
                </h1>
                <button
                  onClick={handleBackToAdminClick}
                  className="bg-red-500 text-white px-4 py-2 rounded-md shadow-md ease-in-out hover:bg-red-600 mt-3 sm:mt-0"
                >
                  Back to Admin
                </button>
              </div>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="flex flex-col sm:flex-row justify-center sm:justify-between pl-5 text-lg">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5 hover:bg-blue-600 w-52 sm:w-auto mt-3 sm:mt-0"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={5}
                    paginationRowsPerPageOptions={[5, 10, 15, 20]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
