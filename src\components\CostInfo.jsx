import React, { useState, useEffect, useRef } from "react";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";
import { useLocation, useNavigate } from "react-router-dom";
import { useOrderV2 } from "../hooks/use-order";
import { usePlan } from "../hooks/use-plan";
import { useCost } from "../hooks/use-cost";
import { useResult } from "../hooks/use-result";
import Swal from "sweetalert2";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { AiTwotoneCalendar } from "react-icons/ai";
import { format } from "date-fns";
import Select from "react-select";

const CostInfo = () => {
  const [isOdPtNoHidden, setIsOdPtNoHidden] = useState(false);
  const SearchPartsNoRef = useRef(null);
  const SearchorderNoRef = useRef(null);
  const SearchCostNoRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [isDisabled, setIsDisabled] = useState(true);
  const queryParams = new URLSearchParams(window.location.search);
  const updatedResultData = JSON.parse(
    decodeURIComponent(queryParams.get("data"))
  );
  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    F3: false,
    F4: false,
    F5: false,
    F6: false,
    F7: false,
    F8: false,
    F9: false,
    F10: false,
    F11: false,
    F12: true,
  });
  const [EnabledState, setEnabledState] = useState({
    Cs_Complete_Date: false,
    Cs_Complete_Qty: false,
    Cs_Final_Complete: false,
    Outside: false,
  });
  const { searchOrderNo: initialSearchOrderNo = "" } = location.state || {};
  const [searchOrderNo, setSearchOrderNo] = useState(initialSearchOrderNo);
  const [searchPlanNo, setSearchPlanNo] = useState("");
  const [searchCostNo, setSearchCostNo] = useState("");
  const [Search_OdPt_No, setSearch_OdPt_No] = useState("");
  const [OdProgressName, setOdProgressName] = useState("");
  const [Search_OdPtCs_No, setSearch_OdPtCs_No] = useState("");
  const [plProgressName, setPlProgressName] = useState("");
  const [Process_Name, setProcess_Name] = useState([]);
  const [Person_Name, setPerson_Name] = useState("");
  const [Resource_Name, setResource_Name] = useState("");
  const [Cs_Progress_Abb_Name, setCs_Progress_Abb_Name] = useState("");
  const [hasUserEditedOrderNo, setHasUserEditedOrderNo] = useState(false);
  const [ProcessNameForRow, setProcessNameForRow] = useState("");
  const [targetNo, setTargetNo] = useState("");
  //ButtonState true means the button (F1-F12) is enabled and false means the button (F1-F12) is disabled

  const [dates, setDates] = useState({
    Pd_Complete_Date: null,
    I_Completed_Date: null,
    Od_Upd_Date: null,

    ptCompDate: null,
    qcCompDate: null,
    planUpdDate: null,
  });
  const ptCompDateRef = useRef(null);
  const qcCompDateRef = useRef(null);
  const planUpdDateRef = useRef(null);
  const dateRefs = useRef({
    Pd_Complete_Date: null,
    I_Completed_Date: null,
    Od_Upd_Date: null,
  });
  const generateSpaces = (count) => "\u00A0".repeat(count);
  const {
    planData,
    setPlanData,
    searchPartsData,
    selectedPlanNo,
    selectPartsData,
    processData,
    setSelectedPlanNo,
    plprogressData,
    setPlProgressData,
    StatusData,
    setStatusData,
  } = usePlan();
  const {
    orderData,
    WorkerData,
    setOrderData,
    searchOrderData,
    setWorkerData,
    OdProgressData,
    setOdProgressData,
  } = useOrderV2();
  const {
    SearchCostData,
    CostData,
    setCostData,
    CostNoData,
    setCostNo,
    ResourceData,
    setResourceData,
    CsProgressData,
    SearchCostNo,
    editCost,
    createCost,
    PlanppcData,
    setPlanppcData,
    ProcessCData,
    setProcessCData,
    setCsProgressData,
    UpdateCost,
    Cs_Progress_CD_AfterUpdate,
    UpdateCostConfirms,
    fetchPlanppc,
    sData,
  } = useCost();
  const { ResultData, SearchResultData } = useResult();
  const headers = [
    "Cost_No",
    "Process_No",
    "Process_Name",
    "Machine_CD",
    "Machine_Name",
    "Machine_Time",
    "Worker_CD",
    "Worker_Name",
    "Person_Time",
    "Process_Date",
    "Process_Qty",
    "Cost_Progress_CD",
    "Cost_Progress",
    "Complete_Date",
    "Complete_Qty",
    "Cost_Remark",
    "Register_Date",
    "Modify_Date",
  ];
  const headersPartTable = [
    "P.",
    "PI.",
    "Part",
    "Pt_Delivery",
    "Pt_Material",
    "Pt_Qty",
    "Pt_Spare_Qty",
    "Pt_NG_Qty",
    "Pt_Instructions",
    "Pt_Remark",
    "Pt_Information",
    "Connect_Od_No",
    "Connect_Pt_No",
    "Connect_Pr_No",
    "Pl_Ed_Rev_Day",
    "Pl_Schedule_CD",
    "Pl_Reg_Date",
    "Pt_Complete_Date",
    "Pl_Upd_Date",
    "PPC1",
    "PPC2",
    "PPC3",
    "PPC4",
    "PPC5",
    "PPC6",
    "PPC7",
    "PPC8",
    "PPC9",
    "PPC10",
    "PPC11",
    "PPC12",
    "PPC13",
    "PPC14",
    "PPC15",
    "PPC16",
    "PPC17",
    "PPC18",
    "PPC19",
    "PPC20",
    "PPC21",
    "PPC22",
    "PPC23",
    "PPC24",
    "PPC25",
    "PPC26",
    "PPC27",
    "PPC28",
    "PPC29",
    "PPC30",
    "PPC31",
    "PPC32",
    "PPC33",
    "PPC34",
    "PPC35",
    "PPC36",
  ];

  const formatDateTime = (date) => {
    if (!date) return "";

    const d = new Date(date);

    // ดึงค่าแบบ UTC แล้วบวก 7 ชั่วโมง
    const year = d.getUTCFullYear();
    const month = d.getUTCMonth();
    const day = d.getUTCDate();
    const hours = d.getUTCHours();
    const minutes = d.getUTCMinutes();
    const seconds = d.getUTCSeconds();

    // สร้างวันที่ใหม่หลังจากบวก 7 ชั่วโมง
    const thaiDate = new Date(
      Date.UTC(year, month, day, hours + 7, minutes, seconds)
    );

    const dd = String(thaiDate.getUTCDate()).padStart(2, "0");
    const mm = String(thaiDate.getUTCMonth() + 1).padStart(2, "0");
    const yyyy = thaiDate.getUTCFullYear();
    const hh = String(thaiDate.getUTCHours()).padStart(2, "0");
    const min = String(thaiDate.getUTCMinutes()).padStart(2, "0");
    const ss = String(thaiDate.getUTCSeconds()).padStart(2, "0");

    return `${dd}/${mm}/${yyyy} ${hh}:${min}:${ss}`;
  };

  const handleFButtonStateChange = (state) => {
    //True means enabled the button
    //False means disabled the button
    if (state === "f2") {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: true,
        F8: true,
        F9: true,
        F10: false,
        F11: true,
        F12: false,
      });
    } else if (state === "f3") {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: true,
        F8: true,
        F9: true,
        F10: false,
        F11: true,
        F12: false,
      });
    } else if (state === "f7") {
      setButtonState({
        F1: false,
        F2: false,
        F3: true,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    } else if (state === "f8") {
      setButtonState({
        F1: false,
        F2: false,
        F3: true,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    } else if (state === "f9") {
      setButtonState({
        F1: false,
        F2: true,
        F3: true,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    } else if (state === "f11") {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: false,
        F9: false,
        F10: false,
        F11: false,
        F12: true,
      });
    }
  };

  const editPermission = (status) => {
    const fields = [
      "Process_No",
      "CMC",
      "CMT",
      "CPC",
      "CPT",
      "CPD",
      "CPN",
      "Cs_Progress_CD",
      "Cs_Remark",
    ];

    fields.forEach((field) => {
      const element = document.getElementById(field);
      if (element) {
        element.disabled = !status;
      }
    });
  };
  const searchPermission = (status) => {
    const fields = ["Search_Order_No", "Search_Parts_No", "Search_Cost_No"];

    fields.forEach((field) => {
      const element = document.getElementById(field);
      if (element) {
        element.disabled = !status;
      }
    });
  };

  const handleF2Click = async () => {
    searchPermission(false);
    editPermission(true);
    handleFButtonStateChange("f2");
    setIsDisabled(false);
  };

  const handleF3Click = async () => {
    searchPermission(false);
    editPermission(true);
    handleFButtonStateChange("f3");

    // รีเซ็ตค่าก่อนเรียก API
    setCostData({
      Order_No: "",
      Parts_No: "",
      OdPt_No: "",
      Cost_No: "",
      CPD: "",
    });
    let FG = 0;
    let KN = 0;
    const result = await SearchResultData(searchOrderNo, searchPlanNo);

    if (result) {
      while (FG < 1) {
        KN += 1;

        if (KN !== 36) {
          if (!result["RPD" + KN] || result["RPD" + KN] === "") {
            FG = 1;
          }
        } else {
          FG = 1;
        }
      }

      console.log("Next Process No:", KN);
    }

    const isDataFetched = await SearchCostData(searchOrderNo, searchPlanNo);
    const {
      Pt_NG_Qty = 0,
      Pt_Spare_Qty = 0,
      Pt_Qty = 0,
    } = updatedResultData || {};

    if (searchPlanNo === "") {
      Swal.fire({
        title: "กรุณากรอกข้อมูล Search Part No.",
        icon: "warning",
        confirmButtonText: "ตกลง",
      });
      return;
    }

    // หากดึงข้อมูลสำเร็จ
    if (isDataFetched) {
      // ตรวจสอบว่า CostNoData เป็นอาร์เรย์ที่มีข้อมูล
      const costList =
        Array.isArray(CostNoData) && CostNoData.length > 0
          ? CostNoData.map((item) => item.Cost_No)
          : [];
      let newCostNo = "001";

      if (costList.length > 0) {
        const maxCostNo = costList.reduce((max, current) => {
          const numericValue = parseInt(current, 10);
          return !isNaN(numericValue) && numericValue > max
            ? numericValue
            : max;
        }, 0);
        newCostNo = String(maxCostNo + 1).padStart(3, "0"); // เพิ่ม 1 และเติม 0 ด้านหน้า
      }

      // อัปเดต CostData
      setCostData((prevState) => ({
        ...prevState,
        Order_No: searchOrderNo,
        Parts_No: searchPlanNo,
        OdPt_No: searchOrderNo + searchPlanNo,
        Cost_No: newCostNo,
        CPD: new Date().toISOString(),
        Process_No: String(KN || ""),
        CPN: Pt_Qty + Pt_Spare_Qty - Pt_NG_Qty,
      }));

      setSearch_OdPt_No(`${searchOrderNo || ""}${searchPlanNo || ""}`);
      setIsOdPtNoHidden(true);
      setIsDisabled(false);
    } else {
      Swal.fire({
        title: "ไม่พบข้อมูล Cost",
        icon: "error",
        confirmButtonText: "ตกลง",
      });
    }
  };

  const handleF6Click = async () => {
    Swal.fire({
      title: "An error occurred.",
      text: "Please try again.",
      icon: "error",
      confirmButtonText: "OK",
    });
  };
  const handleF7Click = async () => {
    const result = await Swal.fire({
      title: "Search other Cost_No ?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancle",
    });
    if (result.isConfirmed) {
      handleFButtonStateChange("f7");
      setCostData("");
      setSearchCostNo("");
      const element = document.getElementById("Search_Cost_No");
      element.disabled = false;
      // console.log(CostData);
    }
  };
  const handleF8Click = async () => {
    const result = await Swal.fire({
      title: "Search other Parts_No ?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancle",
    });
    if (result.isConfirmed) {
      handleFButtonStateChange("f8");
      setCostData("");
      setPlanData("");
      setSearchCostNo("");
      setSearchPlanNo("");
      const elements = document.getElementById("Search_Parts_No");
      elements.disabled = false;
      const element = document.getElementById("Search_Cost_No");
      element.disabled = false;
      // console.log(CostData, planData);
    }
  };

  const handleF9Click = async () => {
    try {
      // แสดง confirm ก่อนทำการบันทึก
      const Confirms = await Swal.fire({
        title: "Confirms",
        text: "Do you save data?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
      });

      // ถ้าผู้ใช้ยืนยัน
      if (Confirms.isConfirmed) {
        // แสดงไอคอนโหลดก่อนทำการบันทึก
        const loadingSwal = Swal.fire({
          title: "Processing...",
          text: "Please wait while we save the data.",
          icon: "info",
          showCancelButton: false,
          showConfirmButton: false,
          allowOutsideClick: false, // ป้องกันไม่ให้คลิกข้างนอก SweetAlert
        });

        // เริ่มทำการบันทึก
        const result = await UpdateCost(
          CostData,
          orderData,
          planData,
          StatusData
        );

        if (result.status === "warning") {
          // แสดง warning
          Swal.fire({
            title: "Warning",
            text: result.message,
            icon: "warning",
            confirmButtonText: "OK",
          });
          return;
        }

        if (result.status === "question") {
          const swalResult = await Swal.fire({
            title: "question",
            text: result.message,
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "Cancel",
          });

          // ถ้าผู้ใช้ยืนยัน
          if (swalResult.isConfirmed) {
            Swal.fire({
              title: "Processing...",
              text: "Please wait while we confirm the cost.",
              icon: "info",
              showCancelButton: false,
              showConfirmButton: false,
              allowOutsideClick: false,
            });
            if (StatusData) {
              setStatusData((prevState) => ({
                ...prevState,
                Obj_Od_No: searchOrderNo,
                Obj_Pt_No: searchPlanNo,
                Old_Obj_Cs_No: sData?.Old_Obj_Cs_No,
                Old_Obj_Pr_No: sData?.Old_Obj_Pr_No,
                Old_Od_Progress: sData?.Old_Od_Progress,
                Old_Pl_Progress: sData?.Old_Pl_Progress,
                Cost_New: sData?.Cost_New,
                Old_Cs_Final: sData?.Old_Cs_Final,
              }));
            }
            await UpdateCostConfirms(CostData, orderData, planData, StatusData);
            Swal.close();
          }
        }

        if (result.status === "error") {
          // แสดง error
          Swal.fire({
            title: result.errorCode,
            text: `${result.message}\n${result.details}`,
            icon: "error",
            confirmButtonText: "OK",
          });
          return;
        }

        await Promise.all([
          searchOrderData(searchOrderNo),
          SearchCostData(searchOrderNo, searchPlanNo),
          selectPartsData(searchOrderNo, searchPlanNo),
          SearchCostNo(searchOrderNo, searchPlanNo, CostData?.Cost_No),
        ]);

        if (StatusData) {
          setStatusData((prevState) => ({
            ...prevState,
            Obj_Od_No: searchOrderNo,
            Obj_Pt_No: searchPlanNo,
            Old_Obj_Cs_No: sData?.Old_Obj_Cs_No,
            Old_Obj_Pr_No: sData?.Old_Obj_Pr_No,
            Old_Od_Progress: sData?.Old_Od_Progress,
            Old_Pl_Progress: sData?.Old_Pl_Progress,
            Cost_New: sData?.Cost_New,
            Old_Cs_Final: sData?.Old_Cs_Final,
          }));
        }
        loadingSwal.close();
        Swal.fire({
          title: "Success!",
          text: "Cost updated successfully.",
          icon: "success",
          confirmButtonText: "OK",
        }).then(() => {
          window.close(); // ปิดแท็บหรือหน้าต่าง
        });
        setIsDisabled(true);

        handleFButtonStateChange("f9");
      } else {
        return;
      }
    } catch (error) {
      console.error("Error in handleF9Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF11Click = async () => {
    const result = await Swal.fire({
      title: "Search other Order_No ?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancle",
    });
    if (result.isConfirmed) {
      handleFButtonStateChange("f11");
      setCostData({});
      setPlanData({});
      setSearchCostNo("");
      setSearchPlanNo("");
      setSearchOrderNo("");
      setCostNo("");
      // setResourceData("");
      // setPlanppcData("");
      // setProcessCData("");
      // setCsProgressData("");
      setOrderData({}); // Reset orderData เป็นอ็อบเจกต์ว่าง
      setSelectedPlanNo("");
      setSearch_OdPtCs_No("");
      setSearch_OdPt_No("");
      setDates({});
      setResource_Name("");
      setPerson_Name("");
      setProcess_Name("");
      setCs_Progress_Abb_Name("");
      searchPermission(true);
      editPermission(false);
      setIsDisabled(true);
      sessionStorage.clear();
    }
  };

  const handleF12Click = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You are about to reset the form data!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, reset it!",
      cancelButtonText: "No, cancel",
      reverseButtons: true,
    }).then((result) => {
      if (result.isConfirmed) {
        setSearchOrderNo("");
        setOrderData({});
        setPlanData({});
        setCostData({});
        SearchCostData("");
        setSelectedPlanNo("");
        sessionStorage.clear();
        Swal.fire("Reset!", "The form data has been reset.", "success");
        navigate("/dashboard");
      } else {
        // console.log("Reset action cancelled.");
      }
    });
  };

  const rows = Array.from({ length: 10 }, (_, rowIndex) => (
    <tr
      key={rowIndex}
      className={rowIndex % 2 === 0 ? "bg-gray-50" : "bg-white"}
    >
      {headers.map((_, colIndex) => (
        <td key={colIndex} className="px-4 py-2 border border-black text-sm">
          <p type="text" className="w-28 h-7 p-1" />
        </td>
      ))}
    </tr>
  ));

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true); // เปิด DatePicker ถ้า ref.current มีค่า
    }
  };
  const CustomDateInput = React.forwardRef(
    (
      {
        value,
        onClick,
        onClear,
        dateValue,
        bgColor = "bg-[#ccffff]",
        width = "w-full sm:min-w-[150px]",
        showTime = false,
      },
      ref
    ) => {
      return (
        <div
          ref={ref}
          onClick={onClick}
          className={`flex items-center border-2 border-gray-500 rounded-md px-2 ${width} ${bgColor} cursor-pointer min-h-[28px]`}
        >
          <span className="flex-grow truncate">
            {dateValue
              ? format(
                  dateValue,
                  showTime ? "dd/MM/yyyy HH:mm:ss" : "dd/MM/yyyy"
                )
              : ""}
          </span>
          <AiTwotoneCalendar className="text-gray-600" />
          {dateValue && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onClear();
              }}
              className="ml-2 text-gray-600"
            >
              X
            </button>
          )}
        </div>
      );
    }
  );

  const handleDateChange = (field, date) => {
    if (date === null) {
      setDates((prevDates) => ({ ...prevDates, [field]: null }));
      return;
    }

    const parsedDate = date instanceof Date ? date : new Date(date);
    if (parsedDate && parsedDate instanceof Date && !isNaN(parsedDate)) {
      setDates((prevDates) => ({ ...prevDates, [field]: parsedDate }));

      // ปิด DatePicker หลังจากเลือกวันที่
      if (dateRefs.current[field]) {
        dateRefs.current[field].setOpen(false); // ปิด DatePicker
      }
    } else {
      console.error("Invalid date selected", date);
    }
  };
  // ✅ ฟังก์ชันเคลียร์วันที่
  const handleClear = (field) => {
    setDates((prevDates) => ({
      ...prevDates,
      [field]: null,
    }));
    console.log(`Start handleClear`);
    setOrderData((prev) => ({
      ...prev,
      [field]: null, // 🛑 เคลียร์ค่าใน orderData ด้วย
    }));
  };
  const [costInfoData, setCostInfoData] = useState({});

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search).get("data");
    const storedSearchOrderNo = sessionStorage.getItem("searchOrderNo") || "";
    const storedSearchPlanNo = sessionStorage.getItem("searchPlanNo") || "";

    // console.log("🔍 Query String:", window.location.search);
    // console.log("🗄 searchOrderNo from sessionStorage:", storedSearchOrderNo);
    // console.log("🗄 searchPlanNo from sessionStorage:", storedSearchPlanNo);

    if (queryParams) {
      try {
        const decodedData = JSON.parse(decodeURIComponent(queryParams));
        // console.log("✅ Data from query string:", decodedData);
        setCostInfoData(decodedData);
      } catch (error) {
        console.error("❌ Invalid JSON data:", error);
      }
    }

    setSearchOrderNo(storedSearchOrderNo);
    setSearchPlanNo(storedSearchPlanNo);

    if (storedSearchOrderNo) {
      searchPartsData(storedSearchOrderNo);
      searchOrderData(storedSearchOrderNo);
    }

    const timer = setTimeout(() => {
      sessionStorage.removeItem("searchOrderNo");
      sessionStorage.removeItem("searchPlanNo");
      // console.log(
      //   "🗑 Removed searchOrderNo and searchPlanNo from sessionStorage"
      // );
    }, 50000);

    return () => clearTimeout(timer); // Cleanup timeout when the component unmounts
  }, []); // Empty dependency array ensures this effect runs only once when the component mounts

  useEffect(() => {
    if (orderData) {
      // อัปเดตค่าใน orderDataRef
      // orderDataRef.current = orderData;
      setDates((prevDates) => {
        const updatedDates = { ...prevDates };

        const parsedPdCompleteDate = orderData.Pd_Complete_Date
          ? new Date(orderData.Pd_Complete_Date)
          : null;
        const parsedICompletedDate = orderData.I_Completed_Date
          ? new Date(orderData.I_Completed_Date)
          : null;
        const parsedOdUpdDate = orderData.Od_Upd_Date
          ? new Date(orderData.Od_Upd_Date)
          : null;

        updatedDates.Pd_Complete_Date =
          parsedPdCompleteDate && !isNaN(parsedPdCompleteDate.getTime())
            ? parsedPdCompleteDate
            : prevDates.Pd_Complete_Date;
        updatedDates.I_Completed_Date =
          parsedICompletedDate && !isNaN(parsedICompletedDate.getTime())
            ? parsedICompletedDate
            : prevDates.I_Completed_Date;
        updatedDates.Od_Upd_Date =
          parsedOdUpdDate && !isNaN(parsedOdUpdDate.getTime())
            ? parsedOdUpdDate
            : prevDates.Od_Upd_Date;

        return updatedDates;
      });
    }
  }, [orderData]);

  useEffect(() => {
    if (planData) {
      setDates((prevDates) => {
        const updatedDates = { ...prevDates };

        const parsedPtIDate = planData.Pt_I_Date
          ? new Date(planData.Pt_I_Date)
          : null;
        const parsedPtCompDate = planData.Pt_Complete_Date
          ? new Date(planData.Pt_Complete_Date)
          : null;
        const parsedPlanUpdDate = planData.Pl_Upd_Date
          ? new Date(planData.Pl_Upd_Date)
          : null;

        updatedDates.ptCompDate =
          parsedPtCompDate && !isNaN(parsedPtCompDate.getTime())
            ? parsedPtCompDate
            : prevDates.ptCompDate;
        updatedDates.qcCompDate =
          parsedPtIDate && !isNaN(parsedPtIDate.getTime())
            ? parsedPtIDate
            : prevDates.qcCompDate;
        updatedDates.planUpdDate =
          parsedPlanUpdDate && !isNaN(parsedPlanUpdDate.getTime())
            ? parsedPlanUpdDate
            : prevDates.planUpdDate;

        // console.log("Updated Dates (planData):", updatedDates);
        return updatedDates;
      });
    }
  }, [planData]);
  // ถ้า planData เปลี่ยนแปลง จะอัปเดต

  const handleSearch = async () => {
    if (!searchOrderNo.trim()) return; // ถ้าไม่มีค่าก็ไม่ต้องเรียก API

    const result = await searchOrderData(searchOrderNo);
    if (result) {
      searchPartsData(searchOrderNo);

      if (SearchPartsNoRef.current) {
        SearchPartsNoRef.current.focus();
        SearchPartsNoRef.current.openMenu();
      }

      setButtonState((prevState) => ({
        ...prevState,
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F11: true,
      }));
    } else {
      setButtonState((prevState) => ({
        ...prevState,
        F1: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: false,
        F11: false,
      }));
      Swal.fire({
        title: "ไม่พบข้อมูล",
        html: `${searchOrderNo} is not yet registered !<br>${searchOrderNo} ที่ป้อนไปยังไม่ได้ถูกลงทะเบียน !<br>${searchOrderNo} は登録されていません!`,
        icon: "warning",
        confirmButtonText: "ตกลง",
      });
      setSearchOrderNo(value);

      if (value) {
        const result = await searchOrderData(value);

        if (result) {
          searchPartsData(value);

          setButtonState((prevState) => ({
            ...prevState,
            F1: false,
            F2: false,
            F3: false,
            F4: false,
            F5: false,
            F6: true,
            F7: false,
            F8: true,
            F11: true,
          }));
        } else {
          Swal.fire({
            title: "ไม่พบข้อมูล",
            html: `${value} is not yet registered!`,
            icon: "warning",
            confirmButtonText: "ตกลง",
          });

          setButtonState((prevState) => ({
            ...prevState,
            F1: false,
            F3: false,
            F4: false,
            F5: false,
            F6: false,
            F7: false,
            F8: false,
            F11: false,
          }));
        }
      }
    }
  };
  const handleSearchOrderNoBlur = async (event) => {
    const value = event.target.value;

    if (value) {
      const result = await searchOrderData(value);
      if (result) {
      } else {
        Swal.fire({
          title: "ไม่พบข้อมูล",
          text: `${value} is not yet registered!`,
          icon: "warning",
        });
      }
    }
  };
  const handleOrderInputChange = async (event) => {
    const { id, value, type, checked } = event.target;

    // console.log(`Changing ${id} to:`, value);
    // console.log(`Start handleOrderInputChange`);
    setOrderData((prev) => {
      const newData = {
        ...prev,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
      };

      // console.log("New orderData after input change:", newData);
      return newData;
    });

    if (id === "Search_Order_No") {
      setSearchOrderNo(value);
      setHasUserEditedOrderNo(true);
    }
  };

  const handlePlanInputChange = async (
    event,
    selectedOption = null,
    inputId = null,
    dateValue = null
  ) => {
    // 1. Handle date picker
    if (dateValue && inputId) {
      const formattedDate = dateValue.toISOString();
      setPlanData((prev) => ({
        ...prev,
        [inputId]: formattedDate,
      }));
      return;
    }

    // 2. Handle select dropdown
    if (selectedOption && inputId) {
      const newValue = selectedOption.value || "";
      if (inputId === "Search_Parts_No") {
        setSearchPlanNo(newValue);
        setHasUserEditedOrderNo(true);
        setButtonState((prev) => ({
          ...prev,
          F1: false,
          F2: false,
          F3: true,
          F6: false,
          F7: true,
          F10: true,
          F11: true,
        }));
      }
      setPlanData((prev) => ({
        ...prev,
        [inputId]: newValue,
      }));
      return;
    }

    // 3. Handle regular input / datetime-local
    if (!event?.target) return;
    const { id, value, type } = event.target;
    let formattedValue = value;

    if (type === "datetime-local" && value) {
      const dateWithCurrentTime = new Date(value);
      const currentMinute = dateWithCurrentTime.getMinutes();
      if (lastMinute !== null && currentMinute !== lastMinute) {
        event.target.blur();
      }
      setLastMinute(currentMinute);
      formattedValue = dateWithCurrentTime.toISOString();
    }

    setPlanData((prev) => ({
      ...prev,
      [id]: formattedValue,
    }));

    // 4. Special case for Search_Parts_No
    if (id === "Search_Parts_No") {
      setSearchPlanNo(value);
      if (value) {
        setSearch_OdPt_No(`${searchOrderNo || ""}${value}`);
        setButtonState((prev) => ({
          ...prev,
          F1: false,
          F2: true,
          F3: true,
          F7: true,
          F10: true,
          F11: true,
        }));
      } else {
        setButtonState((prev) => ({
          ...prev,
          F1: false,
          F2: false,
          F3: false,
          F7: false,
          F10: false,
          F11: false,
        }));
      }
    }

    // 5. Recalculate amount if needed
    const amount = await calculateAmount();
  };

  const handleCostInputChange = async (
    event,
    selectedOption = null,
    inputId = null
    // dateValue = null
  ) => {
    if (selectedOption && inputId) {
      const newValue = selectedOption?.value || "";

      if (inputId === "Search_Cost_No") {
        setSearchCostNo(newValue);
        setHasUserEditedOrderNo(true);
      }

      setCostData((prevCostData) => ({
        ...prevCostData,
        [inputId]: newValue,
      }));

      if (inputId === "Search_Cost_No" && newValue) {
        setButtonState((prevState) => ({
          ...prevState,
          F1: false,
          F2: true,
          F3: true,
          F6: false,
          F7: true,
          F10: true,
          F11: true,
        }));
      }
      return;
    }
    const { id, value, type, checked } = event.target;

    setCostData((prevCostData) => ({
      ...prevCostData,
      [id]: type === "checkbox" ? checked : value === "" ? null : value,
    }));

    if (id === "Search_Cost_No") {
      setSearchCostNo(value);
      setSearch_OdPt_No(`${searchOrderNo || ""}${searchPlanNo || ""}`);
      setSearch_OdPtCs_No(
        `${searchOrderNo || ""}${searchPlanNo || ""}${value}`
      );
    }
  };

  // เมื่อ searchPlanNo เปลี่ยนแปลง (และใช้ searchOrderNo ร่วมด้วย)
  useEffect(() => {
    if (!searchPlanNo) return;

    // เคลียร์ searchCostNo เพื่อรีเซ็ตก่อนโหลดใหม่
    setSearchCostNo(null);

    // ใช้ Promise.all เพื่อรัน async พร้อมกัน (ถ้าไม่จำเป็นต้องรอทีละตัว)
    Promise.all([
      SearchCostData(searchOrderNo, searchPlanNo),
      selectPartsData(searchOrderNo, searchPlanNo),
      fetchPlanppc(searchOrderNo, searchPlanNo),
    ])
      .then(() => {
        // ถ้าต้องการทำอะไรหลังโหลดข้อมูลครบ
        // console.log("All searchPlanNo data loaded");
      })
      .catch((err) => {
        console.error("Error loading data on searchPlanNo change:", err);
      });
  }, [searchPlanNo, searchOrderNo]);

  // เมื่อ searchCostNo และ searchPlanNo พร้อม ให้เรียก SearchCostNo
  useEffect(() => {
    if (!searchCostNo || !searchPlanNo) return;

    SearchCostNo(searchOrderNo, searchPlanNo, searchCostNo)
      .then(() => {
        // console.log("SearchCostNo completed");
      })
      .catch((err) => {
        console.error("Error in SearchCostNo:", err);
      });
  }, [searchCostNo, searchPlanNo, searchOrderNo]);

  useEffect(() => {
    // console.log({ searchOrderNo, searchPlanNo, searchCostNo }); // เพิ่มการตรวจสอบค่า
    if (searchOrderNo === "" && searchPlanNo === "" && searchCostNo === "") {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: false,
        F7: false,
        F8: false,
        F9: false,
        F10: false,
        F11: false,
        F12: true,
      });
    } else if (
      searchOrderNo !== "" &&
      searchPlanNo === "" &&
      (searchCostNo === "" || searchCostNo === null)
    ) {
      setButtonState({
        F1: false,
        F2: false,
        F3: false,
        F4: false,
        F5: false,
        F6: true,
        F7: false,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    } else if (
      searchOrderNo !== "" &&
      searchPlanNo !== "" &&
      (searchCostNo === "" || searchCostNo === null)
    ) {
      setButtonState({
        F1: false,
        F2: false, // F2 ปิดไว้ตามที่คุณต้องการ
        F3: true,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    } else if (
      searchOrderNo !== "" &&
      searchCostNo !== "" &&
      searchPlanNo !== ""
    ) {
      setButtonState({
        F1: false,
        F2: true,
        F3: true,
        F4: false,
        F5: false,
        F6: true,
        F7: true,
        F8: true,
        F9: false,
        F10: false,
        F11: true,
        F12: true,
      });
    }
  }, [searchOrderNo, searchPlanNo, searchCostNo]);

  useEffect(() => {
    if (CostData?.Cs_Progress_CD) {
      Cs_Progress_CD_AfterUpdate();
    }
  }, [CostData?.Cs_Progress_CD]);

  useEffect(() => {
    if (CostData && Object.keys(CostData).length > 0) {
      if (CostData?.CPC && Array.isArray(WorkerData) && WorkerData.length > 0) {
        const selectedGroup = WorkerData.find(
          (item) => item.Worker_CD === CostData?.CPC
        );
        setPerson_Name(selectedGroup ? selectedGroup.Worker_Abb : "");
      }

      if (
        CostData?.CMC &&
        Array.isArray(ResourceData) &&
        ResourceData.length > 0
      ) {
        const selectedGroup = ResourceData.find(
          (item) => item.Resource_CD === CostData?.CMC
        );
        setResource_Name(selectedGroup ? selectedGroup.Resource_Symbol : "");
      }

      if (
        CostData?.Cs_Progress_CD &&
        Array.isArray(CsProgressData) &&
        CsProgressData.length > 0
      ) {
        const selectedGroup = CsProgressData.find(
          (item) => item.Cs_Progress_CD === CostData?.Cs_Progress_CD
        );
        setCs_Progress_Abb_Name(
          selectedGroup ? selectedGroup.Cs_Progress_Abb : ""
        );
      }
    }
  }, [
    CostData?.Cs_Progress_CD,
    CostData?.CPC,
    WorkerData,
    CostData?.CMC,
    ResourceData,
    processData,
    CsProgressData,
    CostData,
  ]);

  useEffect(() => {
    if (!hasUserEditedOrderNo && StatusData?.Obj_Od_No) {
      setSearchOrderNo(StatusData.Obj_Od_No);
      searchOrderData(StatusData.Obj_Od_No);
      searchPartsData(StatusData.Obj_Od_No);
    }
    if (!hasUserEditedOrderNo && StatusData?.Obj_Pt_No) {
      setSearchPlanNo(StatusData.Obj_Pt_No);
      SearchCostData(StatusData.Obj_Od_No, StatusData.Obj_Pt_No);
    }
  }, [StatusData, hasUserEditedOrderNo]);

  useEffect(() => {
    if (!orderData || Object.keys(orderData).length === 0) {
      setOdProgressName("");
      return;
    }

    if (orderData?.Od_Progress_CD && OdProgressData?.length > 0) {
      const selectedGroup = OdProgressData.find(
        (item) => item.Od_Progress_CD === orderData.Od_Progress_CD
      );
      setOdProgressName(selectedGroup?.Od_Progress_Name || "");
    }
  }, [orderData?.Od_Progress_CD, OdProgressData]);

  useEffect(() => {
    if (!planData || Object.keys(planData).length === 0) {
      setPlProgressName("");
      return;
    }

    if (planData?.Pl_Progress_CD && plprogressData?.length > 0) {
      const selectedGroup = plprogressData.find(
        (item) => item.Pl_Progress_CD === planData.Pl_Progress_CD
      );
      setPlProgressName(selectedGroup?.Pl_Progress_Name || "");
    }
  }, [planData?.Pl_Progress_CD, plprogressData]);

  useEffect(() => {
    const found = (PlanppcData || []).find(
      (process) => process.No === CostData?.Process_No
    );
    setProcessNameForRow(found?.Pr_Abb || "None");
  }, [PlanppcData, CostData]);

  useEffect(() => {
    // console.log("reset data");
    // รีเซ็ตค่า
    return () => {
      // Code to run on unmount
      setSearchOrderNo("");
      setOrderData({});
      setPlanData({});
      setCostData({});
      SearchCostData("");
      setSelectedPlanNo("");
      // console.log("Component is being unmounted");
    };
  }, [location.pathname]);

  const customOption = (props) => {
    const { data, innerRef, innerProps, isFocused, isSelected } = props;

    let optionStyle = {};
    if (isSelected) {
      optionStyle = { backgroundColor: "#1E90FF", color: "white" };
    } else if (isFocused) {
      optionStyle = { backgroundColor: "#e0e0e0" };
    }

    return (
      <div
        ref={innerRef}
        {...innerProps}
        style={{
          ...optionStyle,
          display: "table",
          width: "100%",
          tableLayout: "fixed",
          borderCollapse: "collapse",
        }}
      >
        <div
          style={{
            display: "table-row",
            textAlign: "center",
            borderBottom: "1px solid #ddd",
          }}
        >
          <div
            style={{
              display: "table-cell",
              width: "100%",
              padding: "8px",
              borderRight: "1px solid #ddd",
              verticalAlign: "middle",
              textAlign: "center",
            }}
          >
            {data.No}
          </div>
          <div
            style={{
              display: "table-cell",
              width: "100%",
              padding: "8px",
              verticalAlign: "middle",
              textAlign: "center",
            }}
          >
            {data.Pr_Abb}
          </div>
        </div>
      </div>
    );
  };

  const groupedOptions =
    Array.isArray(PlanppcData) && PlanppcData?.length > 0
      ? [
          {
            label: (
              <div
                style={{
                  display: "table",
                  width: "100%",
                  tableLayout: "fixed",
                  borderCollapse: "collapse",
                  fontWeight: "bold",
                  background: "#f0f0f0",
                  textAlign: "center",
                  borderBottom: "1px solid #ddd",
                }}
              >
                <div style={{ display: "table-row" }}>
                  <div
                    style={{
                      display: "table-cell",
                      width: "100%",
                      padding: "8px",
                      borderRight: "1px solid #ddd",
                      textAlign: "center",
                    }}
                  >
                    No
                  </div>
                  <div
                    style={{
                      display: "table-cell",
                      width: "100%",
                      padding: "8px",
                      textAlign: "center",
                    }}
                  >
                    Pr_Abb
                  </div>
                </div>
              </div>
            ),
            options: PlanppcData.sort((a, b) => a.No - b.No).map((item) => ({
              value: item.No,
              label: (
                <div
                  style={{
                    display: "table",
                    width: "100%",
                    tableLayout: "fixed",
                    borderCollapse: "collapse",
                    textAlign: "center",
                    borderBottom: "1px solid #ddd",
                  }}
                >
                  <div style={{ display: "table-row" }}>
                    <div
                      style={{
                        display: "table-cell",
                        width: "100px",
                        padding: "8px",
                        borderRight: "1px solid #ddd",
                        textAlign: "center",
                      }}
                    >
                      {item.No}
                    </div>
                    <div
                      style={{
                        display: "table-cell",
                        width: "200px",
                        padding: "8px",
                        textAlign: "center",
                      }}
                    >
                      {item.Pr_Abb}
                    </div>
                  </div>
                </div>
              ),
              No: item.No,
              Pr_Abb: item.Pr_Abb,
            })),
          },
        ]
      : [
          {
            label: "No information",
            options: [{ value: "", label: "No information" }],
          },
        ];

  const createOptions = (data, valueKey, labelKeys) => {
    return Array.isArray(data) && data.length > 0
      ? data.map((item) => ({
          value: item[valueKey],
          label: `${item[valueKey]}${generateSpaces(2)}| ${labelKeys
            .map((key) => item[key])
            .join(" ")}`,
        }))
      : [{ value: "", label: "" }];
  };

  const groupedMultiOptions = (label, options) => [{ label, options }];

  const createResourceOptions = (data) =>
    createOptions(data, "Resource_CD", ["Resource_Symbol"]);
  const createWorkerOptions = (data) =>
    createOptions(data, "Worker_CD", ["Worker_Abb"]);

  const options = createResourceOptions(ResourceData);

  const selectedOption =
    options.find((option) => option.value === (CostData?.CMC || "")) || null;

  const optionsCPC = createWorkerOptions(WorkerData);

  const selectedCPC =
    optionsCPC.find((option) => option.value === (CostData?.CPC || "")) || null;

  const customOptionMac = (props) => {
    const { data, innerRef, innerProps, isFocused, isSelected } = props;

    let optionStyle = {};
    if (isSelected) {
      optionStyle = { backgroundColor: "#1E90FF", color: "white" };
    } else if (isFocused) {
      optionStyle = { backgroundColor: "#e0e0e0" };
    }

    return (
      <div
        ref={innerRef}
        {...innerProps}
        style={{
          ...optionStyle,
          display: "table",
          width: "100%",
          tableLayout: "fixed",
          borderCollapse: "collapse",
        }}
      >
        <div
          style={{
            display: "table-row",
            textAlign: "center",
            borderBottom: "1px solid #ddd",
          }}
        >
          <div
            style={{
              display: "table-cell",
              width: "100%",
              padding: "8px",
              borderRight: "1px solid #ddd",
              verticalAlign: "middle",
              textAlign: "center",
            }}
          >
            {data.Machine_CD}
          </div>
          <div
            style={{
              display: "table-cell",
              width: "100%",
              padding: "8px",
              borderRight: "1px solid #ddd",
              verticalAlign: "middle",
              textAlign: "center",
            }}
          >
            {data.Machine_Symbol}
          </div>
          <div
            style={{
              display: "table-cell",
              width: "100%",
              padding: "8px",
              verticalAlign: "middle",
              textAlign: "center",
            }}
          >
            {data.Machine_Remark}
          </div>
        </div>
      </div>
    );
  };

  const groupedOptionsmac =
    Array.isArray(ResourceData) && ResourceData.length > 0
      ? [
          {
            label: (
              <div
                style={{
                  display: "table",
                  width: "100%",
                  tableLayout: "fixed",
                  borderCollapse: "collapse",
                  fontWeight: "bold",
                  background: "#f0f0f0",
                  textAlign: "center",
                  borderBottom: "1px solid #ddd",
                }}
              >
                <div style={{ display: "table-row" }}>
                  <div
                    style={{
                      display: "table-cell",
                      width: "100%",
                      padding: "8px",
                      borderRight: "1px solid #ddd",
                      textAlign: "center",
                    }}
                  >
                    Machine_CD
                  </div>

                  <div
                    style={{
                      display: "table-cell",
                      width: "110%",
                      padding: "8px",
                      borderRight: "1px solid #ddd",
                      textAlign: "center",
                    }}
                  >
                    Machine_Symbol
                  </div>

                  <div
                    style={{
                      display: "table-cell",
                      width: "100%",
                      padding: "8px",
                      textAlign: "center",
                    }}
                  >
                    Machine_Remark
                  </div>
                </div>
              </div>
            ),
            options: ResourceData.map((item) => ({
              value: item.Resource_CD,
              label: (
                <div
                  style={{
                    display: "table",
                    width: "100%",
                    tableLayout: "fixed",
                    borderCollapse: "collapse",
                    textAlign: "center",
                    borderBottom: "1px solid #ddd",
                  }}
                >
                  <div style={{ display: "table-row" }}>
                    <div
                      style={{
                        display: "table-cell",
                        width: "100%",
                        padding: "8px",
                        borderRight: "1px solid #ddd",
                        textAlign: "center",
                      }}
                    >
                      {item.Resource_CD}
                    </div>

                    <div
                      style={{
                        display: "table-cell",
                        width: "100%",
                        padding: "8px",
                        borderRight: "1px solid #ddd",
                        textAlign: "center",
                      }}
                    >
                      {item.Resource_Symbol}
                    </div>

                    <div
                      style={{
                        display: "table-cell",
                        width: "100%",
                        padding: "8px",
                        textAlign: "center",
                      }}
                    >
                      {item.Resource_Mark}
                    </div>
                  </div>
                </div>
              ),
              Machine_CD: item.Resource_CD,
              Machine_Symbol: item.Resource_Symbol,
              Machine_Remark: item.Resource_Remark,
            })),
          },
        ]
      : [
          {
            label: "No information",
            options: [{ value: "", label: "No information" }],
          },
        ];

  const customOptionCPC = (props) => {
    const { data, innerRef, innerProps, isFocused, isSelected } = props;

    const optionStyle = {
      backgroundColor: isSelected ? "#1E90FF" : isFocused ? "#e0e0e0" : "white",
      color: isSelected ? "white" : "black",
    };

    return (
      <div
        ref={innerRef}
        {...innerProps}
        style={{ ...optionStyle, ...tableStyle }}
      >
        <div style={rowStyle}>
          {["Worker_CD", "Worker_Abb"].map((key, index) => (
            <div key={index} style={cellStyle}>
              {data[key]}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const tableStyle = {
    display: "table",
    width: "100%",
    tableLayout: "fixed",
    borderCollapse: "collapse",
  };

  const rowStyle = {
    display: "table-row",
    textAlign: "center",
    borderBottom: "1px solid #ddd",
  };

  const cellStyle = {
    display: "table-cell",
    width: "50%",
    padding: "8px",
    borderRight: "1px solid #ddd",
    verticalAlign: "middle",
    textAlign: "center",
  };

  const groupedOptionsCPC =
    Array.isArray(WorkerData) && WorkerData.length > 0
      ? [
          {
            label: (
              <div
                style={{
                  ...tableStyle,
                  fontWeight: "bold",
                  background: "#f0f0f0",
                  textAlign: "center",
                }}
              >
                <div style={rowStyle}>
                  {["Worker_CD", "Worker_Abb"].map((key, index) => (
                    <div key={index} style={cellStyle}>
                      {key}
                    </div>
                  ))}
                </div>
              </div>
            ),
            options: WorkerData.map((item) => ({
              value: item.Worker_CD,
              label: (
                <div style={{ ...tableStyle, textAlign: "center" }}>
                  <div style={rowStyle}>
                    {["Worker_CD", "Worker_Abb"].map((key, index) => (
                      <div key={index} style={cellStyle}>
                        {item[key]}
                      </div>
                    ))}
                  </div>
                </div>
              ),
              ...item,
            })),
          },
        ]
      : [
          {
            label: "No information",
            options: [{ value: "", label: "No information" }],
          },
        ];
  const progressOptions =
    Array.isArray(CsProgressData) && CsProgressData.length > 0
      ? CsProgressData.map((item) => ({
          value: item.Cs_Progress_CD,
          label: item.Cs_Progress_CD, // หรือ item.Cs_Progress_NM ถ้ามีชื่อ
        }))
      : [];

  const selectedOptionP = progressOptions.find(
    (option) => option.value === CostData?.Cs_Progress_CD
  );

  // สีพื้นหลังตามค่าที่เลือก
  const getBgHex = (code) => {
    switch (code) {
      case "5":
        return "#ef4444"; // tailwind red-500
      case "4":
        return "#facc15"; // tailwind yellow-500
      case "3":
        return "#f97316"; // tailwind orange-500
      default:
        return "#ffff99"; // tailwind yellow-100
    }
  };
  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-screen mr-2 ml-2">
        <Navbar />
        <div
          className="flex-1 flex-col overflow-x-auto p-2 bg-white mt-2 rounded-md"
          style={{ maxWidth: "100%" }}
        >
          <h1 className="text-2xl font-bold mt-3 text-center">Cost Info</h1>
          <hr className="my-6 h-0.5 border-t-0 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-2 mb-2">
            <div className="flex flex-col space-y-1">
              <label className="text-xs font-bold">Search Order No.</label>
              <input
                ref={SearchorderNoRef}
                id="Search_Order_No"
                value={searchOrderNo || ""}
                onChange={handleOrderInputChange}
                onDoubleClick={() => {
                  const windowWidth = window.screen.width;
                  const windowHeight = window.screen.height;
                  const left = 0;
                  const top = 0;

                  window.open(
                    "/cost-multi",
                    "costMultiWindow",
                    `width=${windowWidth},height=${windowHeight},top=${top},left=${left},status=yes,toolbar=no,menubar=no,location=no,resizable=yes,scrollbars=yes`
                  );
                }}
                type="text"
                onBlur={handleSearchOrderNoBlur}
                onKeyDown={async (e) => {
                  if (e.key === "Enter") {
                    handleSearch(); // กด Enter แล้วค้นหา
                    setTimeout(() => {
                      SearchPartsNoRef.current?.focus();
                      SearchPartsNoRef.current?.onMenuOpen();
                    }, 100); // หน่วงเล็กน้อยให้ค้นหาเสร็จก่อน
                  }
                }}
                className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-full xl:w-46"
              />
            </div>

            <div className="flex flex-col space-y-1 relative">
              <label className="text-xs font-bold">Search Part No.</label>
              <div className="relative w-full">
                <Select
                  id="Search_Parts_No"
                  ref={SearchPartsNoRef}
                  value={
                    searchPlanNo
                      ? { label: searchPlanNo, value: searchPlanNo }
                      : null
                  }
                  onChange={(selectedOption) =>
                    handlePlanInputChange(
                      null,
                      selectedOption,
                      "Search_Parts_No"
                    )
                  }
                  options={
                    Array.isArray(selectedPlanNo) && selectedPlanNo.length > 0
                      ? selectedPlanNo.map((item) => ({
                          label: item.Parts_No,
                          value: item.Parts_No,
                        }))
                      : [{ label: "No data found", value: "" }]
                  }
                  className="text-xs w-full"
                  styles={{
                    control: (base) => ({
                      ...base,
                      borderColor: "gray",
                      backgroundColor: "#ccffff",
                    }),
                    menu: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                    option: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handlePlanInputChange(); // confirm เลือก part
                      setTimeout(() => {
                        SearchCostNoRef.current?.focus();
                        SearchCostNoRef.current?.onMenuOpen();
                      }, 100);
                    }
                  }}
                  isDisabled={!searchOrderNo}
                  menuPortalTarget={document.body}
                  noOptionsMessage={() => "No data found"}
                  placeholder="Part No"
                />
              </div>
            </div>
            <div className="flex flex-col space-y-1 relative">
              <label className="text-xs font-bold">Search Cost No.</label>
              <div className="relative w-full">
                <Select
                  id="Search_Cost_No"
                  ref={SearchCostNoRef}
                  value={
                    searchCostNo
                      ? { label: searchCostNo, value: searchCostNo }
                      : null
                  }
                  onChange={(selectedOption) =>
                    handleCostInputChange(
                      null,
                      selectedOption,
                      "Search_Cost_No"
                    )
                  }
                  options={
                    Array.isArray(CostNoData) && CostNoData.length > 0
                      ? CostNoData.map((item) => ({
                          label: item.Cost_No,
                          value: item.Cost_No,
                        }))
                      : [{ label: "No data found", value: "" }]
                  }
                  className="text-xs w-full"
                  styles={{
                    control: (base) => ({
                      ...base,
                      borderColor: "gray",
                      backgroundColor: "#ccffff",
                    }),
                    menu: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                    option: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                  }}
                  isDisabled={!searchPlanNo}
                  menuPortalTarget={document.body}
                  noOptionsMessage={() => "No data found"}
                  placeholder="Cost No"
                />
              </div>
            </div>
            <div className="flex items-center pl-3 w-full pt-4">
              <input type="checkbox" id="checkbox1" className="mr-2" />
              <label htmlFor="checkbox1" className="text-sm">
                Process List View
              </label>
              <input
                type="checkbox"
                id="Auto_Year_Change"
                className="mr-2 ml-3"
              />
              <label htmlFor="Auto_Year_Change" className="text-sm">
                Auto Year Change
              </label>
            </div>
            <div className="flex flex-col space-y-1 relative pr-2">
              <label className="text-xs font-bold">Search_OdPt_No</label>
              <div className="relative w-full">
                <input
                  disabled
                  id="Search_OdPt_No"
                  value={Search_OdPt_No || ""}
                  onChange={(e) => handleCostInputChange(e)}
                  type="text"
                  className="border-gray-500 border-solid border-2 rounded-md bg-white px-2 w-full h-8"
                ></input>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2">
            {/* แถวที่ 1: Header Row */}
            {/* คอลัมน์ที่ 1: Order No. */}
            <div className="col-span-1 flex flex-col gap-2">
              {/* Order No. */}
              <div className="flex items-center space-x-2">
                <label className="flex-shrink text-xs font-bold w-[70px]">
                  Order No.
                </label>
                <input
                  disabled
                  id="Order_No"
                  value={CostData?.Order_No || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="bg-white border-2 border-gray-500 rounded-md py-0.5 px-2 w-full md:w-60"
                />
              </div>

              {/* Parts No. */}
              <div className="flex items-center space-x-2">
                <label className="text-xs font-bold w-[70px]">Parts No.</label>
                <input
                  disabled
                  id="Parts_No"
                  value={CostData?.Parts_No || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="bg-white border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                />
              </div>

              {/* Cost No. */}
              <div className="flex items-center space-x-2">
                <label className="text-xs font-bold w-[70px]">Cost No.</label>
                <input
                  disabled
                  id="Cost_No"
                  value={CostData?.Cost_No || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="bg-[#ff99cc] border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                />
              </div>

              {/* Process No. */}
              <div className="flex items-center space-x-2">
                <label className="text-xs font-bold w-[105px] lg:w-[70px]">
                  Process No.
                </label>
                <div className="flex items-center gap-2">
                  <div className="relative w-full sm:w-20 lg:w-20 xl:w-20">
                    <Select
                      id="Process_No"
                      value={
                        CostData?.Process_No
                          ? {
                              value: CostData.Process_No,
                              label: CostData.Process_No,
                            }
                          : null
                      }
                      isDisabled={isDisabled}
                      onChange={(selectedOption) => {
                        setCostData((prev) => ({
                          ...prev,
                          Process_No: selectedOption?.value || "",
                        }));
                      }}
                      options={groupedOptions}
                      components={{ Option: customOption }}
                      styles={{
                        control: (provided) => ({
                          ...provided,
                          border: "2px solid #6b7280",
                          borderRadius: "0.375rem",
                          backgroundColor: "#ffff99",
                          width: "100%",
                          minHeight: "2rem",
                        }),
                        menu: (provided) => ({
                          ...provided,
                          width: "300px",
                          maxHeight: "200px",
                          overflowY: "auto",
                          padding: "0",
                        }),
                        menuList: (provided) => ({
                          ...provided,
                          maxHeight: "200px",
                          overflowY: "auto",
                        }),
                      }}
                    />
                  </div>
                  <input
                    id="Process_Name"
                    value={ProcessNameForRow || ""}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={true}
                    className="bg-white border-2 border-gray-500 rounded-md py-0.5 px-2 w-full sm:w-[90px] lg:w-[95px] xl:w-[150px] h-[40px]"
                  />
                </div>
              </div>
            </div>

            {/* คอลัมน์ที่ 2: Order Info */}
            <div className="col-span-1 flex flex-col gap-2">
              <h3 className="flex-shrink text-xs font-bold">[Order Info]</h3>
              <div className="grid grid-cols-1 gap-2">
                {/* แถวที่ 1 */}
                <div className="flex flex-wrap items-center gap-2">
                  <label className="text-xs font-semibold min-w-24">
                    Od_Progress
                  </label>
                  <div className="flex gap-2 w-full sm:w-auto">
                    <select
                      id="Od_Progress_CD"
                      value={orderData?.Od_Progress_CD || ""}
                      onChange={handleOrderInputChange}
                      disabled={isDisabled}
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-full md:w-16"
                    >
                      <option value={orderData?.Od_Progress_CD}>
                        {orderData?.Od_Progress_CD}
                      </option>
                      {Array.isArray(OdProgressData) &&
                      OdProgressData.length > 0 ? (
                        OdProgressData.map((item, index) => (
                          <option key={index} value={item.Od_Progress_CD}>
                            {item.Od_Progress_CD}
                          </option>
                        ))
                      ) : (
                        <option value="">No data found</option>
                      )}
                    </select>
                    <input
                      value={OdProgressName || ""}
                      onChange={(event) => setOdProgressData(event)}
                      type="text"
                      id="Od_Progress_Name"
                      disabled
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                    />
                  </div>
                </div>

                {/* แถวที่ 2 */}
                <div className="flex flex-wrap items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24 ">
                    Od_Pd_Comp
                  </label>
                  <div className="min-w-0 w-full sm:w-auto">
                    <DatePicker
                      ref={(el) => (dateRefs.current.Pd_Complete_Date = el)}
                      selected={dates.Pd_Complete_Date}
                      timeFormat="HH:mm:ss"
                      onChange={(date) =>
                        handleDateChange("Pd_Complete_Date", date)
                      }
                      dateFormat="dd/MM/yyyy HH:mm:ss"
                      isClearable
                      disabled={isDisabled}
                      popperClassName="z-50"
                      className="border-2 border-gray-500 rounded-md px-2 w-full max-w-xs sm:max-w-sm"
                      customInput={
                        <CustomDateInput
                          dateValue={dates.Pd_Complete_Date}
                          onClear={() => handleClear("Pd_Complete_Date", null)}
                          bgColor="bg-white"
                          width="w-full sm:min-w-[200px]"
                          showTime
                        />
                      }
                    />
                  </div>
                </div>

                {/* แถวที่ 3 */}
                <div className="flex flex-wrap items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24">
                    Od_Qc_Comp
                  </label>
                  <div className="min-w-0">
                    <DatePicker
                      ref={(el) => (dateRefs.current.I_Completed_Date = el)}
                      selected={dates.I_Completed_Date}
                      timeFormat="HH:mm:ss"
                      onChange={(date) =>
                        handleDateChange("I_Completed_Date", date)
                      }
                      dateFormat="dd/MM/yyyy HH:mm:ss"
                      isClearable
                      disabled={isDisabled}
                      popperClassName="z-50"
                      popperContainer={({ children }) => (
                        <div className="relative">{children}</div>
                      )}
                      className="border-2 border-gray-500 rounded-md px-2 w-full max-w-xs"
                      customInput={
                        <CustomDateInput
                          dateValue={dates.I_Completed_Date}
                          onClear={() => handleClear("I_Completed_Date", null)}
                          bgColor="bg-white"
                          width="min-w-[200px]"
                          showTime
                        />
                      }
                    />
                  </div>
                </div>
                <div className="flex flex-wrap items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24">
                    Od_Upd_Date
                  </label>
                  <div className="min-w-0">
                    <DatePicker
                      ref={(el) => (dateRefs.current.Od_Upd_Date = el)}
                      selected={dates.Od_Upd_Date}
                      timeFormat="HH:mm:ss"
                      onChange={(date) => handleDateChange("Od_Upd_Date", date)}
                      dateFormat="dd/MM/yyyy HH:mm:ss"
                      isClearable
                      disabled={isDisabled}
                      popperClassName="z-50"
                      popperContainer={({ children }) => (
                        <div className="relative">{children}</div>
                      )}
                      className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      customInput={
                        <CustomDateInput
                          dateValue={dates.Od_Upd_Date}
                          onClear={() => handleClear("Od_Upd_Date", null)}
                          bgColor="bg-white"
                          width="min-w-[200px]" // ให้ความกว้างเต็ม
                          showTime
                        />
                      }
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-xs font-semibold min-w-10">Qty</label>
                  <div className="flex gap-2 w-full">
                    <input
                      id="Quantity" // ใส่ id ให้ตรงกับที่ใช้ใน handleOrderInputChange
                      value={orderData?.Quantity ?? ""}
                      disabled={isDisabled}
                      onChange={handleOrderInputChange} // เชื่อมโยงกับฟังก์ชันการอัปเดตค่า
                      type="text"
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-10"
                    />
                  </div>
                  <label className="text-xs font-semibold min-w-24">
                    QC_Comp_Qty
                  </label>
                  <div className="flex gap-2 w-full">
                    <input
                      id="I_Complete_Qty" // ใส่ id ให้ตรงกับที่ใช้ใน handleOrderInputChange
                      value={orderData?.I_Complete_Qty ?? ""}
                      disabled={isDisabled}
                      onChange={handleOrderInputChange} // เชื่อมโยงกับฟังก์ชันการอัปเดตค่า
                      type="text"
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-16"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* คอลัมน์ที่ 3: Part No. */}
            <div className="col-span-1 flex flex-col gap-2">
              <h3 className="text-xs font-bold">[Part Info]</h3>
              <div className="grid grid-cols-1 gap-3">
                {/* แถวที่ 1 */}
                <div className="flex items-center gap-1">
                  <label className="text-xs font-semibold min-w-24">
                    Pl_Progress
                  </label>
                  <div className="flex gap-2 w-full">
                    <select
                      id="Pl_Progress_CD"
                      value={planData?.Pl_Progress_CD || ""}
                      disabled={isDisabled}
                      onChange={handlePlanInputChange}
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs bg-[#ffff99] w-full md:w-16"
                    >
                      <option value={planData?.Pl_Progress_CD}>
                        {planData?.Pl_Progress_CD}
                      </option>
                      {Array.isArray(plprogressData) &&
                      plprogressData.length > 0 ? (
                        plprogressData.map((item, index) => (
                          <option key={index} value={item.Pl_Progress_CD}>
                            {item.Pl_Progress_CD}
                          </option>
                        ))
                      ) : (
                        <option value="">No data found</option>
                      )}
                    </select>
                    <input
                      disabled
                      type="text"
                      id="Pl_Progress_Name"
                      value={plProgressName || ""}
                      onChange={(event) => setPlProgressData(event)}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-28"
                    />
                  </div>
                </div>
                {/* แถวที่ 2 */}
                <div className="flex grid-cols-2 items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24">
                    Pt_Comp
                  </label>

                  <div className="relative">
                    <DatePicker
                      selected={dates.ptCompDate || null}
                      onChange={(date) => handleDateChange("ptCompDate", date)}
                      dateFormat="dd/MM/yyyy HH:mm"
                      showTimeSelect
                      timeFormat="HH:mm"
                      timeIntervals={15}
                      ref={ptCompDateRef}
                      isClearable
                      disabled={isDisabled}
                      popperClassName="z-50"
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[200px]"
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false} // ตั้งค่า inline ให้ false
                    />
                    {!dates.ptCompDate && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-2 text-gray-500 cursor-pointer"
                        size={16}
                        onClick={() => handleOpenDatePicker(ptCompDateRef)} // เปิด DatePicker เมื่อคลิก
                      />
                    )}
                  </div>
                </div>

                {/* แถวที่ 3 */}
                <div className="flex grid-cols-2 items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24">
                    QC_Comp
                  </label>
                  <div className="relative">
                    <DatePicker
                      selected={dates.qcCompDate || null}
                      onChange={(date) => handleDateChange("qcCompDate", date)}
                      dateFormat="dd/MM/yyyy HH:mm"
                      showTimeSelect
                      timeFormat="HH:mm"
                      timeIntervals={15}
                      ref={qcCompDateRef}
                      isClearable
                      disabled={isDisabled}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[200px]"
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      popperClassName="z-50"
                      inline={false} // ตั้งค่า inline ให้ false
                    />
                    {!dates.qcCompDate && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-2 text-gray-500 cursor-pointer"
                        size={16}
                        onClick={() => handleOpenDatePicker(qcCompDateRef)} // เปิด DatePicker เมื่อคลิก
                      />
                    )}
                  </div>
                </div>
                <div className="flex grid-cols-2 items-center gap-x-2 mb-1">
                  <label className="text-xs font-semibold min-w-24">
                    Plan_Upd
                  </label>
                  <div className="relative">
                    <DatePicker
                      selected={dates.planUpdDate}
                      onChange={(date) => handleDateChange("planUpdDate", date)}
                      dateFormat="dd/MM/yyyy HH:mm:ss"
                      showTimeSelect
                      timeFormat="HH:mm:ss"
                      timeIntervals={15}
                      ref={planUpdDateRef}
                      isClearable
                      disabled={isDisabled}
                      popperClassName="z-50"
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-[200px]"
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false} // ตั้งค่า inline ให้ false
                    />
                    {!dates.planUpdDate && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-2 text-gray-500 cursor-pointer"
                        size={16}
                        onClick={() => handleOpenDatePicker(planUpdDateRef)} // เปิด DatePicker เมื่อคลิก
                      />
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs font-semibold min-w-24">
                    Pt/Sp/NG/Comp
                  </label>
                  <div className="flex gap-1 w-full">
                    <input
                      value={planData?.Pt_Qty ?? 0}
                      onChange={(event) => handlePlanInputChange(event)}
                      type="text"
                      disabled={isDisabled}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-10"
                    />
                  </div>
                  <div className="flex gap-1 w-full">
                    <input
                      value={planData?.Pt_Spare_Qty ?? 0}
                      onChange={(event) => handlePlanInputChange(event)}
                      type="text"
                      disabled={isDisabled}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-10"
                    />
                  </div>
                  <div className="flex gap-1 w-full">
                    <input
                      value={planData?.Pt_NG_Qty ?? 0}
                      onChange={(event) => handlePlanInputChange(event)}
                      type="text"
                      disabled={isDisabled}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-10"
                    />
                  </div>
                  <div className="flex gap-1 w-full">
                    <input
                      value={planData?.Pt_l_Comp_Qty ?? 0}
                      onChange={(event) => handlePlanInputChange(event)}
                      type="text"
                      disabled={isDisabled}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-10"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-span-1 sm:col-span-2 flex flex-col gap-2">
              {/* แถวที่ 1 - [Part List] และ Search OdPtCs No. */}
              <div className="flex justify-between items-top">
                <h3 className="text-xs font-bold">[Part List]</h3>
                <div className="flex justify-end items-top">
                  <label className="text-xs font-bold block whitespace-nowrap mr-2">
                    Search OdPtCs No.
                  </label>
                  <input
                    disabled
                    id="Search_OdPtCs_No"
                    value={Search_OdPtCs_No || ""}
                    onChange={(e) => handleCostInputChange(e)}
                    type="text"
                    className="bg-white border-2 border-gray-500 rounded-md py-0.5 px-2 w-80 h-8 text-left"
                  />
                </div>
              </div>

              <div className="col-span-2 w-full overflow-x-auto">
                <div className="w-full border border-gray-300 overflow-y-auto max-h-[180px]">
                  <table className="w-full text-xs border-separate border-spacing-0">
                    <thead className="bg-gray-100 sticky top-0 z-50">
                      <tr>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-0 bg-gray-100 z-20">
                          P.
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[51px] bg-gray-100 z-20">
                          PI.
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[107px] bg-gray-100 z-20">
                          Part
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Delivery
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Material
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Spare_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_NG_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Instructions
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Remark
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Information
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Od_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pt_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pr_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Ed_Rev_Day
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Schedule_CD
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Reg_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Complete_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Upd_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC1
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC2
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC3
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC4
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC5
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC6
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC7
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC8
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC9
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC10
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC11
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC12
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC13
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC14
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC15
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC16
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC17
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC18
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC19
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC20
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC21
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC22
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC23
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC24
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC25
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC26
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC27
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC28
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC29
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC30
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC31
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC32
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC33
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC34
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC35
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          PPC36
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(selectedPlanNo) &&
                      selectedPlanNo.length > 0 ? (
                        selectedPlanNo.map((item, index) => (
                          <tr
                            key={index}
                            className={
                              index % 2 === 0 ? "bg-white" : "bg-gray-100"
                            }
                          >
                            {/* คอลัมน์ sticky */}
                            <td className="border border-gray-300 h-8 sticky top-0 left-0 bg-inherit z-20 group-hover:bg-gray-200">
                              {item.Parts_No}
                            </td>
                            <td className="border border-gray-300 h-8 sticky top-0 left-[51px] bg-inherit z-20 group-hover:bg-gray-200">
                              {item.Pl_Progress_CD}
                            </td>
                            <td className="border border-gray-300 h-8 sticky top-0 left-[107px] bg-inherit z-20 group-hover:bg-gray-200">
                              {item.Parts_No}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {new Date(item.Pt_Delivery).toLocaleDateString(
                                "en-GB",
                                {
                                  day: "2-digit",
                                  month: "2-digit",
                                  year: "numeric",
                                }
                              )}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Material}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Qty}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Spare_Qty}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_NG_Qty}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Instructions}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Remark}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pt_Information}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Connect_Od_No}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Connect_Pt_No}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Connect_Pr_No}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pl_Ed_Rev_Day}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.Pl_Schedule_CD}
                            </td>

                            <td className="border border-gray-300 h-8">
                              {new Date(item.Pl_Reg_Date).toLocaleDateString(
                                "en-GB",
                                {
                                  day: "2-digit",
                                  month: "2-digit",
                                  year: "numeric",
                                }
                              )}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {new Date(
                                item.Pt_Complete_Date
                              ).toLocaleDateString("en-GB", {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                              })}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {new Date(item.Pl_Upd_Date).toLocaleDateString(
                                "en-GB",
                                {
                                  day: "2-digit",
                                  month: "2-digit",
                                  year: "numeric",
                                }
                              )}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC1}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC2}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC3}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC4}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC5}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC6}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC7}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC8}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC9}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC10}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC11}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC12}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC13}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC14}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC15}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC16}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC17}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC18}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC19}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC20}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC21}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC22}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC23}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC24}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC25}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC26}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC27}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC28}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC29}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC30}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC31}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC32}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC33}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC34}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC35}
                            </td>
                            <td className="border border-gray-300 h-8">
                              {item.PPC36}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                          <td className="border border-gray-300 h-8"></td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div className="lg:col-span-1">
              <div className="mt-3  lg:mb-0 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 gap-2">
                <div className="flex items-center space-x-2">
                  <label className="text-xs font-bold whitespace-nowrap">
                    Machine CD(CMC)
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="relative w-full xl:w-28">
                      <Select
                        id="CMC"
                        value={selectedOption}
                        onChange={(selectedOption) => {
                          setCostData((prev) => ({
                            ...prev,
                            CMC: selectedOption?.value || "",
                          }));
                        }}
                        isDisabled={isDisabled}
                        options={groupedOptionsmac}
                        components={{ Option: customOptionMac }}
                        placeholder=""
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                          control: (provided) => ({
                            ...provided,
                            border: "2px solid #6b7280",
                            borderRadius: "0.375rem",
                            backgroundColor: "#ccffcc",
                            width: "100%",
                            minHeight: "2rem",
                          }),
                          menu: (provided) => ({
                            ...provided,
                            width: "450px",
                            maxHeight: "200px",
                            overflowY: "auto",
                            padding: "0",
                          }),
                          menuList: (provided) => ({
                            ...provided,
                            maxHeight: "200px",
                            overflowY: "auto",
                          }),
                        }}
                      />
                    </div>
                    <input
                      id="Resource_Name"
                      value={Resource_Name || ""}
                      onChange={(event) => setResourceData(event)}
                      type="text"
                      disabled={true}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-full xl:w-[110px] h-[40px]"
                    />
                  </div>
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-8">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Machine Time
                  </label>
                  <input
                    id="CMT"
                    value={CostData?.CMT ?? "0"}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="lg:mb-0 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 gap-2">
                <div className="flex items-center space-x-5">
                  <label className="text-xs font-bold whitespace-nowrap">
                    Worker CD(CPC)
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="relative w-full xl:w-28">
                      <Select
                        id="CPC"
                        value={selectedCPC}
                        onChange={(selectedCPC) => {
                          setCostData((prev) => ({
                            ...prev,
                            CPC: selectedCPC?.value || "",
                          }));
                        }}
                        options={groupedOptionsCPC}
                        isDisabled={isDisabled}
                        components={{ Option: customOptionCPC }}
                        placeholder=""
                        className="react-select-container"
                        classNamePrefix="react-select"
                        styles={{
                          control: (provided) => ({
                            ...provided,
                            border: "2px solid #6b7280",
                            borderRadius: "0.375rem",
                            backgroundColor: "#ccffcc",
                            width: "100%",
                            minHeight: "2rem",
                          }),
                          menu: (provided) => ({
                            ...provided,
                            width: "300px",
                            maxHeight: "200px",
                            overflowY: "auto",
                            padding: "0",
                          }),
                          menuList: (provided) => ({
                            ...provided,
                            maxHeight: "200px",
                            overflowY: "auto",
                          }),
                        }}
                      />
                    </div>
                    <input
                      id="Worker_Name"
                      value={Person_Name ?? ""}
                      onChange={(event) => setWorkerData(event)}
                      type="text"
                      disabled={true}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-full xl:w-[110px] h-[40px]"
                    />
                  </div>
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-2">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Person_Time(CPT)
                  </label>
                  <input
                    id="CPT"
                    value={CostData?.CPT ?? ""}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={isDisabled}
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-9">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Process_Date
                  </label>
                  <DatePicker
                    id="CPD"
                    selected={
                      CostData && CostData.CPD ? new Date(CostData.CPD) : null
                    }
                    onChange={(date) =>
                      handleCostInputChange({
                        target: {
                          id: "CPD",
                          value: date ? date.toISOString().split("T")[0] : "",
                        },
                      })
                    }
                    dateFormat="dd/MM/yyyy"
                    disabled={isDisabled}
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-2">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Process_Qty(CPN)
                  </label>
                  <input
                    id="CPN"
                    value={CostData?.CPN ?? ""}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={isDisabled}
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="lg:mb-0 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 gap-2">
                <div className="flex items-center space-x-8">
                  <label className="text-xs font-bold whitespace-nowrap">
                    Cost_Progress
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="relative w-full xl:w-28">
                      <Select
                        id="Cs_Progress_CD"
                        className="text-xs w-full"
                        options={progressOptions}
                        value={
                          progressOptions.find(
                            (o) => o.value === CostData?.Cs_Progress_CD
                          ) || null
                        }
                        onChange={(selected) =>
                          handleCostInputChange({
                            target: {
                              id: "Cs_Progress_CD",
                              value: selected?.value ?? "",
                            },
                          })
                        }
                        isDisabled={isDisabled}
                        styles={{
                          // 1) ปรับกล่อง Select เอง
                          control: (base, { isDisabled: disabled }) => ({
                            ...base,
                            borderColor: "#6B7280", // Tailwind gray-500
                            backgroundColor: getBgHex(CostData?.Cs_Progress_CD),
                            minHeight: "2.5rem",
                            opacity: disabled ? 0.6 : 1,
                          }),
                          // 2) ปรับข้อความที่โชว์เมื่อ select แล้ว
                          singleValue: (base) => ({
                            ...base,
                            fontSize: "12px",
                            color: "#000",
                          }),
                          // 3) เมนู dropdown
                          menu: (base) => ({
                            ...base,
                            fontSize: "12px",
                          }),
                          // 4) แต่ละ option
                          option: (base, state) => {
                            const hex = getBgHex(state.data.value);
                            if (state.isSelected) {
                              return {
                                ...base,
                                fontSize: "12px",
                                backgroundColor: hex,
                                color: "#fff",
                              };
                            }
                            if (state.isFocused) {
                              return {
                                ...base,
                                fontSize: "12px",
                                backgroundColor: "#bfdbfe", // Tailwind blue-200
                                color: "#000",
                              };
                            }
                            return {
                              ...base,
                              fontSize: "12px",
                              backgroundColor: "#fff",
                              color: "#000",
                            };
                          },
                        }}
                      />
                    </div>
                    <input
                      id="Cs_Progress_Abb"
                      value={Cs_Progress_Abb_Name || ""}
                      onChange={(event) => setCsProgressData(event)}
                      type="text"
                      disabled={true}
                      className={`border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-full xl:w-[150px] ${
                        CostData?.Cs_Progress_CD === "5"
                          ? "bg-red-500"
                          : CostData?.Cs_Progress_CD === "4"
                          ? "bg-yellow-500"
                          : CostData?.Cs_Progress_CD === "3"
                          ? "bg-orange-500"
                          : "bg-[#ffff99]"
                      }`}
                    />
                  </div>
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-6">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Complete_Date
                  </label>
                  <DatePicker
                    id="Cs_Complete_Date"
                    selected={
                      CostData?.Cs_Complete_Date
                        ? new Date(CostData.Cs_Complete_Date)
                        : null
                    }
                    onChange={(date) =>
                      handleCostInputChange({
                        target: {
                          id: "Cs_Complete_Date",
                          value: date ? date.toISOString().split("T")[0] : "",
                        },
                      })
                    }
                    dateFormat="dd/MM/yyyy"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-7">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Complete_Qty
                  </label>
                  <input
                    id="Cs_Complete_Qty"
                    value={CostData?.Cs_Complete_Qty ?? ""}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="flex flex-col pl-28 pt-1 w-full">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="Outside"
                    checked={
                      CostData?.Outside !== undefined
                        ? CostData?.Outside
                        : false
                    }
                    onChange={handleCostInputChange}
                    className="mr-2 w-5 h-5 rounded-full"
                    disabled={
                      CostData?.Outsides !== undefined
                        ? CostData?.Outside
                        : true
                    }
                  />
                  <label htmlFor="Outside" className="text-sm">
                    Outside
                  </label>
                </div>
                <div className="flex items-center mt-2">
                  <input
                    type="checkbox"
                    id="Cs_Final_Complete"
                    checked={
                      CostData?.Cs_Final_Complete !== undefined
                        ? CostData?.Cs_Final_Complete
                        : false
                    }
                    onChange={handleCostInputChange}
                    className="mr-2 w-5 h-5 rounded-full"
                    disabled={isDisabled}
                  />
                  <label htmlFor="Cs_Final_Complete" className="text-sm">
                    Final_Complete
                  </label>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 gap-2 mb-2">
                <div className="flex items-center">
                  <label className="text-xs font-bold w-40 xl:w-28 whitespace-nowrap">
                    Cs_Remark
                  </label>
                  <textarea
                    id="Cs_Remark"
                    value={CostData?.Cs_Remark || ""}
                    onChange={handleCostInputChange}
                    disabled={isDisabled}
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-full xl:w-60"
                    rows="2"
                  />
                </div>
              </div>

              <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-4">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Cs_Register_Date
                  </label>
                  <input
                    id="Cs_Register_Date"
                    value={formatDateTime(CostData?.Cs_Register_Date)}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={true}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-5">
                  <label className="text-xs font-bold w-28 whitespace-nowrap">
                    Cs_Modify_Date
                  </label>
                  <input
                    id="Cs_Modify_Date"
                    value={formatDateTime(CostData?.Cs_Modify_Date)}
                    onChange={handleCostInputChange}
                    type="text"
                    disabled={true}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 xl:w-60"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-2 mb-2">
                <div className="flex items-center lg:space-x-9">
                  <div className="flex items-center -mt-9 ">
                    <label className="text-xs font-bold w-28 whitespace-nowrap">
                      Sequence_No
                    </label>
                    <input
                      id="Sequence_No"
                      value={CostData?.Sequence_No || ""}
                      onChange={handleCostInputChange}
                      type="text"
                      disabled={true}
                      className="bg-white border-solid border-2 border-gray-500 rounded-md -ml-0.5 py-0.5 px-2 w-20 sm:w-[182px] xl:w-[182px]"
                    />
                  </div>
                  <div className="flex items-center flex-wrap">
                    <div className="flex items-center space-x-3 pl-2 sm:pl-5 lg:pl-0">
                      <label className="text-xs font-bold w-auto whitespace-nowrap">
                        Pr_No
                      </label>
                      <input
                        id="Pr_No"
                        value={CostData?.Pr_No || ""}
                        onChange={handleCostInputChange}
                        type="text"
                        disabled={isDisabled}
                        className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-20 sm:w-40 xl:w-40"
                      />
                    </div>
                    <div className="flex items-center space-x-2 mt-2 pl-2 sm:pl-5 lg:pl-0 w-full">
                      <label className="text-xs font-bold w-auto whitespace-nowrap">
                        Err_No
                      </label>
                      <input
                        id="Err_N"
                        value={CostData?.Err_N || ""}
                        onChange={handleCostInputChange}
                        type="text"
                        disabled={isDisabled}
                        className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-20 sm:w-40 xl:w-40"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-2 p-4 flex flex-col items-start w-full">
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-2 mb-2">
                <div className="flex items-center space-x-7">
                  <label className="text-xs font-bold w-28 me-1 whitespace-nowrap">
                    [Costs_List] QC-FG_Comp
                  </label>
                  <input
                    id="QCFG_Comp_Qty"
                    value={orderData?.Quantity || ""}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-40 sm:w-full"
                  />
                  <label className="text-xs font-bold w-28 me-1 whitespace-nowrap">
                    End|Now_No
                  </label>
                  <input
                    id="End"
                    value={planData?.End_No || ""}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-40 sm:w-full"
                  />
                  <input
                    id="Now_No"
                    value={planData?.Now_No || ""}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-40 sm:w-full"
                  />
                  <label className="text-xs font-bold w-28 me-1 whitespace-nowrap">
                    Re_Pr_Qty
                  </label>
                  <input
                    id="Re_Pr_Qty"
                    value={planData?.Re_Pr_Qty}
                    onChange={handlePlanInputChange}
                    type="text"
                    disabled={isDisabled}
                    className="bg-white border-solid border-2 border-gray-500 rounded-md py-0.5 px-2 w-40 sm:w-full"
                  />
                </div>
              </div>
              <div className="w-full overflow-x-auto">
                <div className="max-h-[540px] overflow-y-auto">
                  <table className="w-full min-w-full bg-white border-separate border-spacing-0">
                    <thead className="sticky top-0 z-30 bg-gray-200">
                      <tr>
                        {headers.map((header, index) => (
                          <th
                            key={index}
                            className={`px-4 py-2 text-xs font-medium text-gray-700 text-left border border-black ${
                              index === 0
                                ? "sticky top-0 left-0 bg-gray-200 z-20"
                                : index === 1
                                ? "sticky top-0 left-[146px] bg-gray-200 z-20"
                                : index === 2
                                ? "sticky top-0 left-[292px] bg-gray-200 z-20"
                                : ""
                            }`}
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(CostNoData) && CostNoData.length > 0
                        ? CostNoData.map((item, rowIndex) => {
                            const rowColor =
                              rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50";

                            const workerNamesForRow = Array.isArray(WorkerData)
                              ? WorkerData.filter(
                                  (worker) => worker.Worker_CD === item.CPC
                                ).map((worker) => worker.Worker_Abb)
                              : [];

                            const ResourceNamesForRow = Array.isArray(
                              ResourceData
                            )
                              ? ResourceData.filter(
                                  (resource) =>
                                    resource.Resource_CD === item.CMC
                                ).map((resource) => resource.Resource_Abb)
                              : [];

                            const CsProgressNamesForRow = Array.isArray(
                              CsProgressData
                            )
                              ? CsProgressData.filter(
                                  (csprogress) =>
                                    csprogress.Cs_Progress_CD ===
                                    item.Cs_Progress_CD
                                ).map(
                                  (csprogress) => csprogress.Cs_Progress_Symbol
                                )
                              : [];

                            const processKey = item.Process_No;
                            const ProcessNamesForRow = Array.isArray(
                              PlanppcData
                            )
                              ? PlanppcData.filter(
                                  (Process) => Process.No === processKey
                                ).map((Process) => Process.Pr_Abb)
                              : [];

                            return (
                              <tr key={rowIndex} className={rowColor}>
                                <td
                                  className="px-4 py-2 border border-black text-sm sticky left-0 bg-white border-r cursor-pointer"
                                  onClick={() => {
                                    setSearchCostNo(item.Cost_No);
                                  }}
                                >
                                  <p className="w-28 h-7 p-1">
                                    <a>{item.Cost_No}</a>
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm sticky left-[146px] bg-white border-r">
                                  <p className="w-28 h-7 p-1">
                                    {item.Process_No}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm sticky left-[292px] bg-white border-r">
                                  <p className="w-48 h-7 p-1">
                                    {ProcessNamesForRow.length > 0
                                      ? ProcessNamesForRow.map((name) =>
                                          name.trim() === "" ? "None" : name
                                        ).join(", ")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">{item.CMC}</p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-48 h-7 p-1">
                                    {ResourceNamesForRow.length > 0
                                      ? ResourceNamesForRow.join(", ")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item?.CMT ?? 0}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">{item.CPC}</p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {workerNamesForRow.length > 0
                                      ? workerNamesForRow.join(", ")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">{item.CPT}</p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.CPD
                                      ? new Date(item.CPD).toLocaleDateString(
                                          "en-GB"
                                        )
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">{item.CPN}</p>
                                </td>
                                <td
                                  className={`px-4 py-2 border border-black text-sm ${
                                    item.Cs_Progress_CD === "5"
                                      ? "bg-red-500 text-white"
                                      : item.Cs_Progress_CD === "4"
                                      ? "bg-yellow-500 text-black"
                                      : item.Cs_Progress_CD === "3"
                                      ? "bg-orange-500 text-white"
                                      : ""
                                  }`}
                                >
                                  <p className="w-full h-full p-1">
                                    {item.Cs_Progress_CD}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {CsProgressNamesForRow.length > 0
                                      ? CsProgressNamesForRow.join(", ")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.Cs_Complete_Date
                                      ? new Date(
                                          item.Cs_Complete_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.Cs_Complete_Qty}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.Cs_Remark}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.Cs_Register_Date
                                      ? new Date(
                                          item.Cs_Register_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </p>
                                </td>
                                <td className="px-4 py-2 border border-black text-sm">
                                  <p className="w-28 h-7 p-1">
                                    {item.Cs_Modify_Date
                                      ? new Date(
                                          item.Cs_Modify_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </p>
                                </td>
                              </tr>
                            );
                          })
                        : new Array(10).fill(null).map((_, rowIndex) => (
                            <tr
                              key={rowIndex}
                              className={
                                rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"
                              }
                            >
                              {new Array(18).fill(null).map((_, colIndex) => (
                                <td
                                  key={colIndex}
                                  className="px-4 py-2 border border-black text-sm"
                                >
                                  <p className="w-28 h-7 p-1"></p>
                                </td>
                              ))}
                            </tr>
                          ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 gap-4">
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F1}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F1 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                (F1)
              </button>
              <button
                disabled={!buttonState.F2}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F2 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF2Click}
              >
                Edit <br />
                編集(F2)
              </button>
              <button
                disabled={!buttonState.F3}
                onClick={handleF3Click}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F3 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                New_Add <br />
                追加(F3)
              </button>
              <button
                disabled={!buttonState.F4}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F4 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                (F4)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F5}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F5 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                (F5)
              </button>
              <button
                disabled={!buttonState.F6}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F6 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF6Click}
              >
                Result_View <br />
                (F6)
              </button>
              <button
                disabled={!buttonState.F7}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F7 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF7Click}
              >
                Next_Cost <br />
                別原(F7)
              </button>
              <button
                disabled={!buttonState.F8}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F8 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF8Click}
              >
                Next_Parts
                <br />
                別部(F8)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F9}
                onClick={handleF9Click}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F9 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                Save
                <br />
                登録(F9)
              </button>

              <button
                disabled={!buttonState.F10}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F10 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                Delete
                <br />
                取消(F10)
              </button>
              <button
                disabled={!buttonState.F11}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F11 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF11Click}
              >
                Next_Input <br />
                次へ(F11)
              </button>
              <button
                disabled={!buttonState.F12}
                className={`bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center ${
                  !buttonState.F12 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                onClick={handleF12Click}
              >
                Exit <br />
                終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostInfo;
