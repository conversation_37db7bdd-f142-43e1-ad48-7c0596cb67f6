import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import "../../fonts/CODE39.ttf";

export default function RD_Process_Sheet() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const { orderNo } = useParams();
  const reportRefs = useRef([]);
  const [OrderData, setOrderData] = useState(null);
  const [CustomerData, setCustomerData] = useState([]);
  const [ProcessSheetData, setProcessSheetData] = useState([]);

  useEffect(() => {
    // ฟังการส่งข้อความจากหน้าต่างหลัก
    const handleMessage = (event) => {
      // ตรวจสอบว่าเป็นข้อมูลจากที่มาถูกต้องหรือไม่
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const { OrderData, CustomerData, ProcessSheetData } = event.data;

      // ตั้งค่าข้อมูลที่ได้รับ
      setOrderData(OrderData);
      setCustomerData(CustomerData);
      setProcessSheetData(ProcessSheetData);

      localStorage.removeItem("OdinfoisF6Clicked");

      localStorage.setItem("OdinfoisF6Clicked", "true");
    };

    // เพิ่ม event listener เมื่อหน้าต่างโหลด
    window.addEventListener("message", handleMessage);

    // ลบ event listener เมื่อ component ถูก unmount
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  useEffect(() => {
    const OdinfoisF6Clicked = localStorage.getItem("OdinfoisF6Clicked");

    if (
      OdinfoisF6Clicked === "true" &&
      OrderData &&
      CustomerData.length > 0 &&
      ProcessSheetData.length > 0
    ) {
      handleViewPDF();
    }
  }, [OrderData, CustomerData, ProcessSheetData]);

  const styles = {
    fontFamily: "CODE39",
  };

  const getCurrentDateTime = () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, "0");
    const month = String(now.getMonth() + 1).padStart(2, "0"); // เดือนเริ่มจาก 0
    const year = now.getFullYear();
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    // สร้างวันที่และเวลาในรูปแบบที่ต้องการ
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const handleViewPDF = async () => {
    const pdf = new jsPDF("p", "mm", "a4");

    for (let i = 0; i < reportRefs.current.length; i++) {
      const element = reportRefs.current[i];
      if (!element) continue;

      const canvas = await html2canvas(element, { scale: 2 });
      const imgData = canvas.toDataURL("image/png");
      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      if (i > 0) pdf.addPage(); // เพิ่มหน้าใหม่ถ้าไม่ใช่หน้าแรก
      pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
    }

    const pdfBlob = pdf.output("blob");
    const pdfURL = URL.createObjectURL(pdfBlob);
    window.open(pdfURL, "_blank");

    window.close();
    localStorage.removeItem("OdinfoisF6Clicked");
  };

  const filteredProcessSheetData = ProcessSheetData?.filter(
    (sheetData) =>
      sheetData.Order_No.trim().toLowerCase() === orderNo.trim().toLowerCase()
  );

  const filteredCustomerData = Array.isArray(CustomerData)
    ? CustomerData.find(
        (customer) =>
          customer.Customer_CD.trim().toLowerCase() ===
          OrderData?.Customer_CD.trim().toLowerCase()
      )
    : null;

  return (
    <div className="bg-slate-200">
      {/* <button
        onClick={handleViewPDF}
        className="absolute top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-md hover:bg-blue-600 focus:outline-none"
      >
        View as PDF
      </button> */}

      {filteredProcessSheetData &&
        filteredProcessSheetData.length > 0 &&
        filteredProcessSheetData.map((sheetData, index) => {
          const latestPPDPosition = sheetData.Latest_PPD_Position;

          const longPPVsCount = Object.keys(sheetData).filter(
            (key) =>
              key.startsWith("PPV") &&
              sheetData[key] &&
              sheetData[key].length > 100
          ).length;

          const fontSizeClassFor24 =
            longPPVsCount >= 3
              ? "text-[8.47pt]"
              : longPPVsCount === 2
              ? "text-[8.7pt]"
              : longPPVsCount === 1
              ? "text-[8.95pt]"
              : "text-[9.21pt]";

          const fontSizeClassFor30 =
            longPPVsCount >= 3
              ? "text-[7.9pt]"
              : longPPVsCount === 2
              ? "text-[8.07pt]"
              : longPPVsCount === 1
              ? "text-[8.25pt]"
              : "text-[8.45pt]";

          const fontSizeClassFor36 =
            longPPVsCount >= 3
              ? "text-[8.04pt]"
              : longPPVsCount === 2
              ? "text-[8.17pt]"
              : longPPVsCount === 1
              ? "text-[8.35pt]"
              : "text-[8.52pt]";

          return (
            <div
              key={index}
              ref={(el) => (reportRefs.current[index] = el)}
              className="px-5 border border-gray-300 w-[794px] h-[1113px] mx-auto bg-white overflow-hidden"
            >
              <div className="relative">
                {/* ทำให้ <h1> อยู่ตรงกลาง โดยไม่ขยับ */}
                <h1 className="font-bold text-[20pt] text-[#000080] text-center">
                  ＊＊ Process Sheet ＊＊
                </h1>

                <p className="text-[10pt]  font-bold absolute right-0 bottom-0">
                  <span className="text-[#000080]">Make_Time:</span>
                  <span className="text-[#000000]">{getCurrentDateTime()}</span>
                </p>
              </div>

              <p className="text-[10pt] font-bold text-right">
                <span className="text-[#000080] ">Reg_Person: </span>

                <span className="text-[12pt] text-[#000000] font-normal ">
                  {sheetData?.Pl_Reg_Person_CD || ""}
                </span>
              </p>

              <div className="grid grid-cols-3 gap-4">
                <p className="text-[12pt] font-bold ">
                  <span className="text-[#000080] ">Product Group: </span>
                  <span className="text-[14pt] text-[#000000] font-normal">
                    {sheetData?.Product_Grp_CD || ""}
                  </span>
                </p>

                <p className="text-[12pt] font-bold text-center">
                  <span className="text-[#000080] ">Parts Name: </span>
                  <span className="text-[14pt] text-[#000000] font-normal">
                    {sheetData?.Parts_Name || ""}
                  </span>
                </p>

                <p
                  className="text-[18pt] font-normal text-right"
                  style={styles}
                >
                  <span className="text-[#000000] ">
                    {sheetData?.Barcode || ""}
                  </span>
                </p>
              </div>

              <div className="grid grid-cols-3 pr-96 gap-5">
                <p className="text-[12pt] font-bold">
                  <span className="text-[#000080]">Order_No</span>
                </p>
                <p className="text-[12pt] font-bold ml-7">
                  <span className="text-[#000080]">Parts_No</span>
                </p>
                <p className="text-[12pt] font-bold">
                  <span className="text-[#000080]">Pt_Qty</span>
                </p>
              </div>

              <div className="grid grid-cols-6 pr-20">
                <p className="text-[18pt] font-bold w-[500px]">
                  <span className="text-[#000000]">
                    {sheetData?.Order_No || ""}
                    <br></br>
                  </span>

                  <span className="text-[12pt] text-[#000080]">Customer: </span>
                  {filteredCustomerData && (
                    <span
                      className={`text-[#000000] font-normal ${
                        filteredCustomerData?.Customer_Name?.trim().length > 40
                          ? "text-[10pt]"
                          : "text-[12pt]"
                      }`}
                    >
                      {filteredCustomerData?.Customer_Name || ""}
                    </span>
                  )}
                </p>

                <>
                  <p className="text-[18pt] font-bold pl-10">
                    <span className="text-[#000000]">
                      &nbsp;&nbsp;- {sheetData?.Parts_No || ""}
                    </span>
                  </p>
                  <p className="text-[18pt] font-bold pl-10">
                    <span className="text-[#000000]">
                      {sheetData?.Pt_Qty || ""}
                    </span>
                  </p>
                </>

                <div className="text-[16pt] font-bold pl-14 pt-1.5">
                  <span className="text-[#000000]">PCS</span>
                </div>

                <div className="text-[12pt] font-bold w-48">
                  <div className="flex gap-4">
                    <span className="text-[#000000] pl-1 block">Request</span>
                    <span>
                      {sheetData?.Request_Delivery
                        ? new Date(
                            sheetData.Request_Delivery
                          ).toLocaleDateString("en-GB")
                        : ""}
                    </span>
                  </div>

                  <div className="flex gap-4">
                    <span className="text-[#000000] pl-1 block">Confirm</span>
                    <span>
                      {sheetData?.Confirm_Delivery
                        ? new Date(
                            sheetData.Confirm_Delivery
                          ).toLocaleDateString("en-GB")
                        : ""}
                    </span>
                  </div>

                  <div className="flex gap-[39px]">
                    <span className="text-[#000000] pl-1 block">Parts</span>
                    <span>
                      {sheetData?.Pt_Delivery
                        ? new Date(sheetData.Pt_Delivery).toLocaleDateString(
                            "en-GB"
                          )
                        : ""}
                    </span>
                  </div>
                </div>

                <p className="font-bold pl-24 w-24">
                  <span className="text-[16pt] text-[#000000] block">
                    Product
                  </span>
                  <span className="text-[20pt]">
                    {sheetData?.Pt_Delivery
                      ? new Date(sheetData.Pt_Delivery).toLocaleDateString(
                          "en-GB",
                          {
                            day: "2-digit",
                            month: "2-digit",
                          }
                        )
                      : ""}
                  </span>
                </p>
              </div>

              <div className="grid grid-cols-3">
                <div className="font-bold w-80">
                  <div className="flex items-baseline">
                    <span className="text-[#000080] pl-7">Name: </span>
                    <span
                      className={`text-[#000000] font-normal ml-1 ${
                        sheetData?.Product_Name?.trim().length > 32
                          ? "text-[7pt]"
                          : "text-[12pt]"
                      }`}
                    >
                      {sheetData?.Product_Name || ""}
                    </span>
                  </div>
                  <div className="flex items-baseline mt-2 mb-4">
                    <span className="text-[#000080] pl-10">Size: </span>
                    <span
                      className={`text-[#000000] font-normal ml-1 ${
                        sheetData?.Product_Size?.trim().length > 32
                          ? "text-[7pt]"
                          : "text-[12pt]"
                      }`}
                    >
                      {sheetData?.Product_Size || ""}
                    </span>
                  </div>
                  <div className="flex items-baseline mt-2 mb-4">
                    <span className="font-normal text-[10pt]">Draw: </span>
                    <span
                      className={`text-[#000000] font-normal ml-1 ${
                        sheetData?.Product_Draw?.trim().length > 44
                          ? "text-[7pt]"
                          : "text-[12pt]"
                      }`}
                    >
                      {sheetData?.Product_Draw || ""}
                    </span>
                  </div>
                </div>

                <div className="text-[12pt] font-bold pl-20">
                  <div>
                    <span className="text-[#000080]">Specific: </span>
                    <span className="text-[#000000] font-normal">
                      {sheetData?.Specific_CD}
                    </span>
                  </div>

                  <div className="">
                    <span className="text-[#000000] font-normal"></span>
                  </div>
                  <div className="mt-8 w-96">
                    <span className="text-[#000080]">Pt_Material: </span>
                    <span className="text-[#000000] font-normal">
                      {sheetData?.Pt_Material}
                    </span>
                  </div>
                  <div>
                    <span className="text-[#000000] font-normal"></span>
                  </div>
                </div>
                <div className="text-[12pt] font-bold">
                  <div>
                    <span
                      className="text-[#000080]"
                      style={{ transform: "translateY(-5px)" }}
                    >
                      Pt_Remark
                    </span>
                  </div>
                  <div>
                    <textarea className="text-[10pt] font-normal text-[#000000] w-[250px] h-auto min-h-[95px] border border-black focus:outline-none focus:ring-2 focus:ring-blue-500 absolute resize-none z-50"></textarea>
                  </div>
                </div>
              </div>

              <div className="flex space-x-24 -mt-4">
                <div>
                  <span
                    className="text-[#000080] text-[10pt] font-bold"
                    style={{ transform: "translateY(-5px)" }}
                  >
                    Info
                  </span>
                </div>
                <div>
                  <span
                    className="text-[#000080] text-[10pt] font-bold mr-1"
                    style={{ transform: "translateY(-5px)" }}
                  >
                    Pl_Quote_OdPt_No:
                  </span>

                  <span
                    className="text-[#000000] text-[12pt] font-bold"
                    style={{ transform: "translateY(-5px)" }}
                  >
                    {sheetData?.Pl_Quote_OdPt_No || ""}
                  </span>
                </div>
              </div>

              {latestPPDPosition <= 24 && (
                <div className="grid grid-cols-[110px_auto] gap-2 h-[90vh]">
                  {/* Left Section */}
                  <div className="flex flex-col h-full overflow-hidden">
                    <div className="flex-grow flex flex-col">
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full"
                        style={{
                          transform: "scale(0.8)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          {Array.from({ length: 6 }).map((_, rowIndex) => {
                            const validInfoValues = Object.keys(sheetData)
                              .filter(
                                (key) =>
                                  key.startsWith("Info") && sheetData[key]
                              )
                              .map((key) => sheetData[key]);

                            const infoValue = validInfoValues[rowIndex] || "";

                            return (
                              <tr key={`info-${rowIndex}`}>
                                <td className="border-[1px] border-[#000080] p-1 w-[20px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {rowIndex + 1}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080] p-1 min-w-[115px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {infoValue}
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                      <span
                        className="text-[#000080] text-[10pt] font-bold -mt-8"
                        style={{ transform: "translateY(-7px)" }}
                      >
                        Connect_Pt_Info
                      </span>
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full -mt-1"
                        style={{
                          transform: "scale(0.7)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          <tr className="text-[8pt]">
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Od No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-[130px]">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Od_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pt No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pt_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pr No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pr_No}
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <div className="flex-grow -mt-[38px]">
                        <table
                          className="border-collapse border-[2px] border-[#000080] text-[8pt] w-full"
                          style={{
                            transform: "scale(0.65)",
                            transformOrigin: "top left",
                          }}
                        >
                          <thead>
                            <tr>
                              <th className="border-[1px] border-[#000080]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  No
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[72px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Process
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[72px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Pl_Date
                                </span>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {Array.from({ length: 36 }).map((_, rowIndex) => {
                              return (
                                <tr key={`row-${rowIndex}`}>
                                  <td className="border-[1px] border-[#000080] text-center">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    >
                                      {rowIndex + 1}
                                    </span>
                                  </td>
                                  <td
                                    className="border-[1px] border-[#000080]"
                                    style={{
                                      wordWrap: "break-word",
                                      wordBreak: "break-word",
                                      whiteSpace: "normal",
                                    }}
                                  >
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                  <td className="border-[1px] border-[#000080] ">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Right Section */}
                  <div className="flex flex-col -ml-2">
                    <div className="flex-grow overflow-y-auto">
                      <table
                        className={`border-collapse border-[3px] border-[#000080] w-full ${fontSizeClassFor24}`}
                      >
                        <thead className="text-[#000080]">
                          <tr>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                No
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[90px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Process
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[60px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Plan <br />
                                Date
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[60px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Plan <br />
                                Time
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[270px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Instructions
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Time
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Date
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Qty
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Person <br />
                                Sign
                              </span>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {Array.from({ length: 24 }).map((_, index) => {
                            const ppcKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPC")
                            );
                            const ppdKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPD")
                            );
                            const pplKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPL")
                            );

                            // ดึงค่าทั้งหมดตามลำดับ (รวมช่องว่าง)
                            const ppcValues = ppcKeys.map(
                              (key) => sheetData[key] || ""
                            );
                            const ppdValues = ppdKeys.map(
                              (key) => sheetData[key] || ""
                            );
                            const pplValues = pplKeys.map(
                              (key) => sheetData[key] || ""
                            );

                            // ดึงค่าตาม index ที่กำหนด
                            const processValue = ppcValues[index] || "";
                            const ppdValue = ppdValues[index] || "";

                            // ฟังก์ชันแปลงวันที่เป็น mm/yy
                            const formatToDDMM = (dateString) => {
                              if (!dateString) return "";
                              const date = new Date(dateString);
                              if (isNaN(date)) return "";
                              const day = date
                                .getDate()
                                .toString()
                                .padStart(2, "0");
                              const month = (date.getMonth() + 1)
                                .toString()
                                .padStart(2, "0");
                              return `${day}/${month}`;
                            };

                            const formattedPPDValue = formatToDDMM(ppdValue);

                            const pplValue = pplValues[index] || "";

                            const ppvKey = `PPV${index + 1}`;
                            const ppvValue = sheetData
                              ? sheetData[ppvKey] || ""
                              : "";

                            return (
                              <tr key={`process-${index}`}>
                                <td className="border-[1px] text-[#000080] border-[#000080] p-1 text-center font-bold">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {index + 1}
                                  </span>
                                </td>
                                <td
                                  align="center"
                                  className="border-[1px] border-[#000080]"
                                >
                                  <span
                                    style={{
                                      display: "inline-block",
                                      maxWidth: "90px",
                                      wordBreak: "break-word",
                                      transform: "translateY(-5px)",
                                      fontSize:
                                        processValue.length > 12
                                          ? "9px"
                                          : processValue.length > 8
                                          ? "12px"
                                          : "14px",
                                    }}
                                  >
                                    {processValue}
                                  </span>
                                </td>
                                <td
                                  align="center"
                                  className="border-[1px] border-[#000080] text-[12px]"
                                >
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {formattedPPDValue}
                                  </span>
                                </td>
                                <td
                                  align="right"
                                  className="border-[1px] border-[#000080] pr-1 text-[12px]"
                                >
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {pplValue}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080] p-0.5 text-left">
                                  <span
                                    style={{
                                      transform: "translateY(-5px)",
                                      fontSize:
                                        ppvValue.length > 80 ? "10px" : "14px",
                                    }}
                                  >
                                    {ppvValue}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                      {/* Start Footer Report */}
                      <div className="flex justify-between">
                        <div className="text-[16px] text-black font-bold pl-5">
                          <span style={{ transform: "translateY(-5px)" }}>
                            ตรวจสอบขนาดก่อนทำงาน
                          </span>
                        </div>
                        <div className="text-[16px] text-blue-800 font-bold">
                          <span style={{ transform: "translateY(-5px)" }}>
                            FTC-FR-PDS-03-10(1-11-2016)
                          </span>
                        </div>
                      </div>
                      {/* End Footer Report */}
                    </div>
                  </div>
                </div>
              )}

              {latestPPDPosition > 24 && latestPPDPosition < 31 && (
                <div className="grid grid-cols-[110px_auto] gap-2 h-[90vh]">
                  {/* Left Section */}
                  <div className="flex flex-col h-full overflow-hidden">
                    <div className="flex-grow flex flex-col">
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full"
                        style={{
                          transform: "scale(0.8)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          {Array.from({ length: 6 }).map((_, rowIndex) => {
                            const validInfoValues = Object.keys(sheetData)
                              .filter(
                                (key) =>
                                  key.startsWith("Info") && sheetData[key]
                              )
                              .map((key) => sheetData[key]);

                            const infoValue = validInfoValues[rowIndex] || "";

                            return (
                              <tr key={`info-${rowIndex}`}>
                                <td className="border-[1px] border-[#000080] p-1 w-[20px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {rowIndex + 1}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080] p-1 min-w-[115px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {infoValue}
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                      <span
                        className="text-[#000080] text-[9pt] font-bold -mt-8"
                        style={{ transform: "translateY(-7px)" }}
                      >
                        Connect_Pt_Info
                      </span>
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full -mt-1"
                        style={{
                          transform: "scale(0.7)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          <tr className="text-[8pt]">
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Od No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-[130px]">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Od_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pt No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pt_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pr No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pr_No}
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <div className="flex-grow -mt-[38px]">
                        <table
                          className="border-collapse border-[2px] border-[#000080] text-[8pt] w-full"
                          style={{
                            transform: "scale(0.66)",
                            transformOrigin: "top left",
                          }}
                        >
                          <thead>
                            <tr>
                              <th className="border-[1px] border-[#000080]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  No
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[72px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Process
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[70px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Pl_Date
                                </span>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {Array.from({ length: 36 }).map((_, rowIndex) => {
                              return (
                                <tr key={`row-${rowIndex}`}>
                                  <td className="border-[1px] border-[#000080] text-center">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    >
                                      {rowIndex + 1}
                                    </span>
                                  </td>
                                  <td
                                    className="border-[1px] border-[#000080]"
                                    style={{
                                      wordWrap: "break-word",
                                      wordBreak: "break-word",
                                      whiteSpace: "normal",
                                    }}
                                  >
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                  <td className="border-[1px] border-[#000080] ">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Right Section */}
                  <div className="flex flex-col h-full -ml-2">
                    <div className="flex-grow overflow-y-auto">
                      <table
                        className={`border-collapse border-[3px] border-[#000080] w-full ${fontSizeClassFor30}`}
                        style={{
                          transform: "scale(0.865)",
                          transformOrigin: "top left",
                        }}
                      >
                        <thead className="text-[#000080]">
                          <tr>
                            <th className="border-[2px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                No
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[90px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Process
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[60px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Plan <br />
                                Date
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[60px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Plan <br />
                                Time
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[335px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Instructions
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[40px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Time
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[40px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Date
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[40px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Rs <br />
                                Qty
                              </span>
                            </th>
                            <th className="border-[2px] border-[#000080] min-w-[50px]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Person <br />
                                Sign
                              </span>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {Array.from({ length: 30 }).map((_, index) => {
                            const ppcKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPC")
                            );
                            const ppdKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPD")
                            );
                            const pplKeys = Object.keys(sheetData).filter(
                              (key) => key.startsWith("PPL")
                            );

                            // ดึงค่าทั้งหมดตามลำดับ (รวมช่องว่าง)
                            const ppcValues = ppcKeys.map(
                              (key) => sheetData[key] || ""
                            );
                            const ppdValues = ppdKeys.map(
                              (key) => sheetData[key] || ""
                            );
                            const pplValues = pplKeys.map(
                              (key) => sheetData[key] || ""
                            );

                            // ดึงค่าตาม index ที่กำหนด
                            const processValue = ppcValues[index] || "";
                            const ppdValue = ppdValues[index] || "";

                            // ฟังก์ชันแปลงวันที่เป็น mm/yy
                            const formatToDDMM = (dateString) => {
                              if (!dateString) return "";
                              const date = new Date(dateString);
                              if (isNaN(date)) return "";
                              const day = date
                                .getDate()
                                .toString()
                                .padStart(2, "0");
                              const month = (date.getMonth() + 1)
                                .toString()
                                .padStart(2, "0");
                              return `${day}/${month}`;
                            };

                            const formattedPPDValue = formatToDDMM(ppdValue);

                            const pplValue = pplValues[index] || "";

                            const ppvKey = `PPV${index + 1}`;
                            const ppvValue = sheetData
                              ? sheetData[ppvKey] || ""
                              : "";

                            return (
                              <tr key={`process-${index}`}>
                                <td className="border-[1px] text-[#000080] border-[#000080] p-1 text-center font-bold">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {index + 1}
                                  </span>
                                </td>
                                <td
                                  align="center"
                                  className="border-[1px] border-[#000080]"
                                >
                                  <span
                                    style={{
                                      display: "inline-block",
                                      maxWidth: "90px",
                                      wordBreak: "break-word",
                                      transform: "translateY(-5px)",
                                      fontSize:
                                        processValue.length > 12
                                          ? "9px"
                                          : processValue.length > 8
                                          ? "12px"
                                          : "14px",
                                    }}
                                  >
                                    {processValue}
                                  </span>
                                </td>
                                <td
                                  align="center"
                                  className="border-[1px] border-[#000080] text-[12px]"
                                >
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {formattedPPDValue}
                                  </span>
                                </td>
                                <td
                                  align="right"
                                  className="border-[1px] border-[#000080] pr-1 text-[12px]"
                                >
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {pplValue}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080] p-0.5 text-left">
                                  <span
                                    style={{
                                      transform: "translateY(-5px)",
                                      fontSize:
                                        ppvValue.length > 80 ? "10px" : "14px",
                                    }}
                                  >
                                    {ppvValue}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                                <td className="border-[1px] border-[#000080]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  ></span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                      {/* Start Footer Report */}
                      <div
                        className="flex mt-1.5"
                        style={{ transform: "translateY(-120px)" }}
                      >
                        <div className="text-[16px] text-black font-bold pl-5">
                          <span style={{ transform: "translateY(-5px)" }}>
                            ตรวจสอบขนาดก่อนทำงาน
                          </span>
                        </div>
                        <div className="text-[16px] text-blue-800 font-bold pl-[175px]">
                          <span style={{ transform: "translateY(-5px)" }}>
                            FTC-FR-PDS-03-10(1-11-2016)
                          </span>
                        </div>
                      </div>
                      {/* End Footer Report */}
                    </div>
                  </div>
                </div>
              )}

              {latestPPDPosition >= 31 && (
                <div className="grid grid-cols-[110px_auto] gap-2 h-[90vh]">
                  {/* Left Section */}
                  <div className="flex flex-col h-full overflow-hidden">
                    <div className="flex-grow flex flex-col">
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full"
                        style={{
                          transform: "scale(0.8)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          {Array.from({ length: 6 }).map((_, rowIndex) => {
                            const validInfoValues = Object.keys(sheetData)
                              .filter(
                                (key) =>
                                  key.startsWith("Info") && sheetData[key]
                              )
                              .map((key) => sheetData[key]);

                            const infoValue = validInfoValues[rowIndex] || "";

                            return (
                              <tr key={`info-${rowIndex}`}>
                                <td className="border-[1px] border-[#000080] p-1 w-[20px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {rowIndex + 1}
                                  </span>
                                </td>
                                <td className="border-[1px] border-[#000080] p-1 min-w-[115px]">
                                  <span
                                    style={{ transform: "translateY(-5px)" }}
                                  >
                                    {infoValue}
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                      <span
                        className="text-[#000080] text-[9pt] font-bold -mt-8"
                        style={{ transform: "translateY(-7px)" }}
                      >
                        Connect_Pt_Info
                      </span>
                      <table
                        className="border-collapse border-[2px] border-[#000080] text-[10pt] w-full -mt-1"
                        style={{
                          transform: "scale(0.7)",
                          transformOrigin: "top left",
                        }}
                      >
                        <tbody>
                          <tr className="text-[8pt]">
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Od No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-[130px]">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Od_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pt No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pt_No}
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="border-[1px] border-[#000080]">
                              <span style={{ transform: "translateY(-5px)" }}>
                                Pr No
                              </span>
                            </td>
                            <td className="border-[1px] border-[#000080] min-w-32">
                              <span style={{ transform: "translateY(-7px)" }}>
                                {sheetData?.Connect_Pr_No}
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <div className="flex-grow -mt-[38px]">
                        <table
                          className="border-collapse border-[2px] border-[#000080] text-[8pt] w-full"
                          style={{
                            transform: "scale(0.655)",
                            transformOrigin: "top left",
                          }}
                        >
                          <thead>
                            <tr>
                              <th className="border-[1px] border-[#000080]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  No
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[72px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Process
                                </span>
                              </th>
                              <th className="border-[1px] border-[#000080] min-w-[70px]">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  Pl_Date
                                </span>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {Array.from({ length: 36 }).map((_, rowIndex) => {
                              return (
                                <tr key={`row-${rowIndex}`}>
                                  <td className="border-[1px] border-[#000080] text-center">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    >
                                      {rowIndex + 1}
                                    </span>
                                  </td>
                                  <td
                                    className="border-[1px] border-[#000080]"
                                    style={{
                                      wordWrap: "break-word",
                                      wordBreak: "break-word",
                                      whiteSpace: "normal",
                                    }}
                                  >
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                  <td className="border-[1px] border-[#000080] ">
                                    <span
                                      style={{ transform: "translateY(-5px)" }}
                                    ></span>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Right Section */}
                  <div className="flex flex-col h-full -ml-2">
                    <table
                      className={`border-collapse border-[3px] border-[#000080] w-full ${fontSizeClassFor36}`}
                      style={{
                        transform: "scale(0.72)",
                        transformOrigin: "top left",
                      }}
                    >
                      <thead className="text-[#000080]">
                        <tr>
                          <th className="border-[2px] border-[#000080]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              No
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[100px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Process
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Plan <br />
                              Date
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Plan <br />
                              Time
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] w-60 min-w-[405px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Instructions
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Rs <br />
                              Time
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Rs <br />
                              Date
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Rs <br />
                              Qty
                            </span>
                          </th>
                          <th className="border-[2px] border-[#000080] min-w-[60px]">
                            <span style={{ transform: "translateY(-5px)" }}>
                              Person <br />
                              Sign
                            </span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {Array.from({ length: 36 }).map((_, index) => {
                          const ppcKeys = Object.keys(sheetData).filter((key) =>
                            key.startsWith("PPC")
                          );
                          const ppdKeys = Object.keys(sheetData).filter((key) =>
                            key.startsWith("PPD")
                          );
                          const pplKeys = Object.keys(sheetData).filter((key) =>
                            key.startsWith("PPL")
                          );

                          // ดึงค่าทั้งหมดตามลำดับ (รวมช่องว่าง)
                          const ppcValues = ppcKeys.map(
                            (key) => sheetData[key] || ""
                          );
                          const ppdValues = ppdKeys.map(
                            (key) => sheetData[key] || ""
                          );
                          const pplValues = pplKeys.map(
                            (key) => sheetData[key] || ""
                          );

                          // ดึงค่าตาม index ที่กำหนด
                          const processValue = ppcValues[index] || "";
                          const ppdValue = ppdValues[index] || "";

                          // ฟังก์ชันแปลงวันที่เป็น mm/yy
                          const formatToDDMM = (dateString) => {
                            if (!dateString) return "";
                            const date = new Date(dateString);
                            if (isNaN(date)) return "";
                            const day = date
                              .getDate()
                              .toString()
                              .padStart(2, "0");
                            const month = (date.getMonth() + 1)
                              .toString()
                              .padStart(2, "0");
                            return `${day}/${month}`;
                          };

                          const formattedPPDValue = formatToDDMM(ppdValue);

                          const pplValue = pplValues[index] || "";

                          const ppvKey = `PPV${index + 1}`;
                          const ppvValue = sheetData
                            ? sheetData[ppvKey] || ""
                            : "";

                          return (
                            <tr key={`process-${index}`}>
                              <td className="border-[1px] text-[#000080] border-[#000080] p-1 text-center font-bold">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {index + 1}
                                </span>
                              </td>
                              <td
                                align="center"
                                className="border-[1px] border-[#000080]"
                              >
                                <span
                                  style={{
                                    display: "inline-block",
                                    maxWidth: "100px",
                                    wordBreak: "break-word",
                                    transform: "translateY(-5px)",
                                    fontSize:
                                      processValue.length > 12
                                        ? "10px"
                                        : processValue.length > 8
                                        ? "12px"
                                        : "14px",
                                  }}
                                >
                                  {processValue}
                                </span>
                              </td>
                              <td
                                align="center"
                                className="border-[1px] border-[#000080] text-[12px]"
                              >
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {formattedPPDValue}
                                </span>
                              </td>
                              <td
                                align="right"
                                className="border-[1px] border-[#000080] pr-1 text-[12px]"
                              >
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {pplValue}
                                </span>
                              </td>
                              <td className="border-[1px] border-[#000080] p-0.5 text-left">
                                <span
                                  style={{
                                    transform: "translateY(-5px)",
                                    fontSize:
                                      ppvValue.length > 80 ? "10px" : "14px",
                                  }}
                                >
                                  {ppvValue}
                                </span>
                              </td>
                              <td className="border-[1px] border-[#000080]">
                                <span
                                  style={{ transform: "translateY(-5px)" }}
                                ></span>
                              </td>
                              <td className="border-[1px] border-[#000080]">
                                <span
                                  style={{ transform: "translateY(-5px)" }}
                                ></span>
                              </td>
                              <td className="border-[1px] border-[#000080]">
                                <span
                                  style={{ transform: "translateY(-5px)" }}
                                ></span>
                              </td>
                              <td className="border-[1px] border-[#000080]">
                                <span
                                  style={{ transform: "translateY(-5px)" }}
                                ></span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                    {/* Start Footer Report */}
                    <div
                      className="flex pt-1.5"
                      style={{ transform: "translateY(-280px)" }}
                    >
                      <div className="text-[16px] text-black font-bold pl-5">
                        <span style={{ transform: "translateY(-5px)" }}>
                          ตรวจสอบขนาดก่อนทำงาน
                        </span>
                      </div>
                      <div className="text-[16px] text-blue-800 font-bold pl-[190px]">
                        <span style={{ transform: "translateY(-5px)" }}>
                          FTC-FR-PDS-03-10(1-11-2016)
                        </span>
                      </div>
                    </div>
                    {/* End Footer Report */}
                  </div>
                </div>
              )}
            </div>
          );
        })}
    </div>
  );
}
