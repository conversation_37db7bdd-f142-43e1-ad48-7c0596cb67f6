import React, { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import axios from "axios";

export function Plan_WI() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);

  // เรียกข้อมูลแบบเก็บ local
  // const fetchData = async () => {
  //   // ลบเฉพาะข้อมูล wiPlanTable ออกจาก LocalStorage
  //   localStorage.removeItem('wiPlanTable');

  //   // เช็คข้อมูลใน LocalStorage
  //   const cachedData = localStorage.getItem('wiPlanTable');
  //   if (cachedData) {
  //     console.log("Local Storage still has cached data");
  //     setData(JSON.parse(cachedData).QD_Plan_WI);  // ดึงจากโครงสร้างใหม่
  //     return;
  //   }

  //   // ถ้าไม่มีข้อมูลใน LocalStorage ให้เรียก API
  //   try {
  //     const response = await axios.post(`${apiUrl_4000}/wi-sum/wi-plan`);
  //     if (response.data.status === "success") {
  //       const formattedData = { QD_Plan_WI: response.data.data };  // ครอบข้อมูลด้วย QD_Plan_WI
  //       setData(formattedData.QD_Plan_WI);
  //       localStorage.setItem('wiPlanTable', JSON.stringify(formattedData));
  //     }
  //   } catch (error) {
  //     console.error("Error fetching data:", error);
  //     alert("Error fetching data");
  //   }
  // };

  // useEffect(() => {
  //   fetchData();
  // }, []);

  const fetchData = async () => {
    try {
      const response = await axios.post(`${apiUrl_4000}/wi-sum/wi-plan`);
      if (response.data.status === "success") {
        const formattedData = { QD_Plan_WI: response.data.data }; // ครอบข้อมูลด้วย QD_Plan_WI
        setData(formattedData.QD_Plan_WI);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      alert("Error fetching data");
    }
  };

  // เรียก fetchData เมื่อเข้าหน้านี้
  useEffect(() => {
    fetchData();
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return "-";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "-";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}/${month}/${day}`;
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const columns = [
    {
      name: "Product_Grp_CD",
      selector: (row) => row.Product_Grp_CD,
      width: "180px",
    },
    { name: "Item1_CD", selector: (row) => row.Item1_CD, width: "180px" },
    { name: "Item1", selector: (row) => row.Item1, width: "180px" },
    { name: "Item1_Grp", selector: (row) => row.Item1_Grp, width: "180px" },
    { name: "Customer_CD", selector: (row) => row.Customer_CD, width: "180px" },
    { name: "Customer", selector: (row) => row.Customer, width: "180px" },
    { name: "Coating_CD", selector: (row) => row.Coating_CD, width: "180px" },
    { name: "Coating", selector: (row) => row.Coating, width: "180px" },
    {
      name: "Od_Progress_CD",
      selector: (row) => row.Od_Progress_CD,
      width: "180px",
    },
    { name: "Od_Progress", selector: (row) => row.Od_Progress, width: "180px" },
    {
      name: "Od_Pending",
      selector: (row) => (
        <input type="checkbox" checked={row.Od_Pending} disabled />
      ),
      width: "180px",
    },

    {
      name: "Order_Date",
      selector: (row) => {
        const date = new Date(row.Order_Date);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}/${month}/${day}`;
      },
      width: "180px",
    },

    { name: "Order_Type", selector: (row) => row.Order_Type, width: "180px" },
    { name: "Order_No", selector: (row) => row.Order_No, width: "180px" },
    { name: "Parts_No", selector: (row) => row.Parts_No, width: "180px" },
    { name: "OdPt_No", selector: (row) => row.OdPt_No, width: "180px" },
    { name: "Parts_CD", selector: (row) => row.Parts_CD, width: "180px" },
    { name: "Pt_Material", selector: (row) => row.Pt_Material, width: "180px" },
    { name: "Pt_Qty", selector: (row) => row.Pt_Qty, width: "180px" },
    { name: "Pt_Unit_CD", selector: (row) => row.Pt_Unit_CD, width: "180px" },
    {
      name: "Pt_Split",
      selector: (row) => (
        <input type="checkbox" checked={row.Pt_Split} disabled />
      ),
      width: "180px",
    },

    {
      name: "Pt_Spare_Qty",
      selector: (row) => row.Pt_Spare_Qty,
      width: "180px",
    },
    { name: "Pt_NG_Qty", selector: (row) => row.Pt_NG_Qty, width: "180px" },
    {
      name: "Connect_Od_No",
      selector: (row) => row.Connect_Od_No,
      width: "180px",
    },
    {
      name: "Connect_Pt_No",
      selector: (row) => row.Connect_Pt_No,
      width: "180px",
    },
    {
      name: "Connect_Pr_No",
      selector: (row) => row.Connect_Pr_No,
      width: "180px",
    },
    {
      name: "Pl_St_Rev_Day",
      selector: (row) => row.Pl_St_Rev_Day,
      width: "180px",
    },
    {
      name: "Pl_Ed_Rev_Day",
      selector: (row) => row.Pl_Ed_Rev_Day,
      width: "180px",
    },
    {
      name: "Pl_Progress_CD",
      selector: (row) => row.Pl_Progress_CD,
      width: "180px",
    },
    { name: "Pl_Progress", selector: (row) => row.Pl_Progress, width: "180px" },
    {
      name: "Pl_Schedule_CD",
      selector: (row) => row.Pl_Schedule_CD,
      width: "180px",
    },
    {
      name: "Pt_Instructions",
      selector: (row) => row.Pt_Instructions,
      width: "180px",
    },
    { name: "Pt_Remark", selector: (row) => row.Pt_Remark, width: "180px" },
    {
      name: "Pt_Information",
      selector: (row) => row.Pt_Information,
      width: "180px",
    },
    {
      name: "Pl_Reg_Person_CD",
      selector: (row) => row.Pl_Reg_Person_CD,
      width: "180px",
    },
    {
      name: "Pl_Upd_Person_CD",
      selector: (row) => row.Pl_Upd_Person_CD,
      width: "180px",
    },
    {
      name: "Pt_Delivery",
      selector: (row) => {
        const date = new Date(row.Pt_Delivery);

        if (isNaN(date.getTime())) return "";

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");

        return `${year}/${month}/${day}`;
      },
      width: "180px",
    },

    {
      name: "Target_Delivery",
      selector: (row) => {
        const date = new Date(row.Target_Delivery);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}/${month}/${day}`;
      },
      width: "180px",
    },

    {
      name: "Pl_Reg_Date",
      selector: (row) => formatDate(row.Pl_Reg_Date),
      width: "180px",
    },
    {
      name: "Pt_Complete_Date",
      selector: (row) => formatDate(row.Pt_Complete_Date),
      width: "180px",
    },
    {
      name: "Pt_I_Date",
      selector: (row) => formatDate(row.Pt_I_Date),
      width: "180px",
    },
    {
      name: "Pt_Shipment_Date",
      selector: (row) => formatDate(row.Pt_Shipment_Date),
      width: "180px",
    },
    {
      name: "Pt_Calc_Date",
      selector: (row) => formatDate(row.Pt_Calc_Date),
      width: "180px",
    },
    {
      name: "Pt_Confirm_Date",
      selector: (row) => formatDate(row.Pt_Confirm_Date),
      width: "180px",
    },
    {
      name: "Pl_Upd_Date",
      selector: (row) => formatDate(row.Pl_Upd_Date),
      width: "180px",
    },
    {
      name: "Pl_Month_Plan",
      cell: (row) => (
        <input type="checkbox" checked={row.Pl_Month_Plan} disabled />
      ),
      width: "180px",
    },
    {
      name: "Pl_Wweek_Plan",
      cell: (row) => (
        <input type="checkbox" checked={row.Pl_Wweek_Plan} disabled />
      ),
      width: "180px",
    },
    {
      name: "Pl_Today_Plan",
      cell: (row) => (
        <input type="checkbox" checked={row.Pl_Today_Plan} disabled />
      ),
      width: "180px",
    },
    {
      name: "Pt_CAT1",
      cell: (row) => <input type="checkbox" checked={row.Pt_CAT1} disabled />,
      width: "180px",
    },
    {
      name: "Pt_CAT2",
      cell: (row) => <input type="checkbox" checked={row.Pt_CAT2} disabled />,
      width: "180px",
    },
    {
      name: "Pt_CAT3",
      cell: (row) => <input type="checkbox" checked={row.Pt_CAT3} disabled />,
      width: "180px",
    },
    {
      name: "Money_Object",
      cell: (row) => (
        <input type="checkbox" checked={row.Money_Object} disabled />
      ),
      width: "180px",
    },
    {
      name: "Outside",
      cell: (row) => <input type="checkbox" checked={row.Outside} disabled />,
      width: "180px",
    },

    {
      name: "Pt_Pending",
      selector: (row) => (
        <input type="checkbox" checked={row.Pt_Pending} disabled />
      ),
      width: "180px",
    },
    { name: "Info1", selector: (row) => row.Info1, width: "180px" },
    { name: "Info2", selector: (row) => row.Info2, width: "180px" },
    { name: "Info3", selector: (row) => row.Info3, width: "180px" },
    { name: "Info4", selector: (row) => row.Info4, width: "180px" },
    { name: "Info5", selector: (row) => row.Info5, width: "180px" },
    { name: "Info6", selector: (row) => row.Info6, width: "180px" },
    {
      name: "InfoChk1",
      selector: (row) => row.InfoChk1,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk1} disabled />,
    },
    {
      name: "InfoChk2",
      selector: (row) => row.InfoChk2,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk2} disabled />,
    },
    {
      name: "InfoChk3",
      selector: (row) => row.InfoChk3,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk3} disabled />,
    },
    {
      name: "InfoChk4",
      selector: (row) => row.InfoChk4,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk4} disabled />,
    },
    {
      name: "InfoChk5",
      selector: (row) => row.InfoChk5,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk5} disabled />,
    },
    {
      name: "InfoChk6",
      selector: (row) => row.InfoChk6,
      width: "180px",
      cell: (row) => <input type="checkbox" checked={row.InfoChk6} disabled />,
    },
    { name: "Max_No", selector: (row) => row.Max_No, width: "180px" },
    { name: "End_No", selector: (row) => row.End_No, width: "180px" },
    { name: "Now_No", selector: (row) => row.Now_No, width: "180px" },
    {
      name: "Total_M_Time",
      selector: (row) => row.Total_M_Time,
      width: "180px",
    },
    {
      name: "Total_P_Time",
      selector: (row) => row.Total_P_Time,
      width: "180px",
    },
    { name: "Re_Pr_Qty", selector: (row) => row.Re_Pr_Qty, width: "180px" },
    {
      name: "Re_Total_M_Time",
      selector: (row) => row.Re_Total_M_Time,
      width: "180px",
    },
    {
      name: "Re_Total_P_Time",
      selector: (row) => row.Re_Total_P_Time,
      width: "180px",
    },
    {
      name: "Re_Total_N_Time",
      selector: (row) => row.Re_Total_N_Time,
      width: "180px",
    },
    { name: "Re_Days", selector: (row) => row.Re_Days, width: "180px" },
    { name: "Re_Days_CAT", selector: (row) => row.Re_Days_CAT, width: "180px" },
    {
      name: "Re_Days_Work_R",
      selector: (row) => row.Re_Days_Work_R,
      width: "180px",
    },
    {
      name: "Re_Days_Work_P",
      selector: (row) => row.Re_Days_Work_P,
      width: "180px",
    },
    { name: "Re_PrQty_P", selector: (row) => row.Re_PrQty_P, width: "180px" },
    { name: "Priority_P", selector: (row) => row.Priority_P, width: "180px" },
    { name: "Join_P", selector: (row) => row.Join_P, width: "180px" },
    {
      name: "Priority_Rate",
      selector: (row) => row.Priority_Rate,
      width: "180px",
    },
    { name: "PPC1", selector: (row) => row.PPC1, width: "180px" },
    { name: "PPC2", selector: (row) => row.PPC2, width: "180px" },
    { name: "PPC3", selector: (row) => row.PPC3, width: "180px" },
    { name: "PPC4", selector: (row) => row.PPC4, width: "180px" },
    { name: "PPC5", selector: (row) => row.PPC5, width: "180px" },
    { name: "PPC6", selector: (row) => row.PPC6, width: "180px" },
    { name: "PPC7", selector: (row) => row.PPC7, width: "180px" },
    { name: "PPC8", selector: (row) => row.PPC8, width: "180px" },
    { name: "PPC9", selector: (row) => row.PPC9, width: "180px" },
    { name: "PPC10", selector: (row) => row.PPC10, width: "180px" },
    { name: "PPC11", selector: (row) => row.PPC11, width: "180px" },
    { name: "PPC12", selector: (row) => row.PPC12, width: "180px" },
    { name: "PPC13", selector: (row) => row.PPC13, width: "180px" },
    { name: "PPC14", selector: (row) => row.PPC14, width: "180px" },
    { name: "PPC15", selector: (row) => row.PPC15, width: "180px" },
    { name: "PPC16", selector: (row) => row.PPC16, width: "180px" },
    { name: "PPC17", selector: (row) => row.PPC17, width: "180px" },
    { name: "PPC18", selector: (row) => row.PPC18, width: "180px" },
    { name: "PPC19", selector: (row) => row.PPC19, width: "180px" },
    { name: "PPC20", selector: (row) => row.PPC20, width: "180px" },
    { name: "PPC21", selector: (row) => row.PPC21, width: "180px" },
    { name: "PPC22", selector: (row) => row.PPC22, width: "180px" },
    { name: "PPC23", selector: (row) => row.PPC23, width: "180px" },
    { name: "PPC24", selector: (row) => row.PPC24, width: "180px" },
    { name: "PPC25", selector: (row) => row.PPC25, width: "180px" },
    { name: "PPC26", selector: (row) => row.PPC26, width: "180px" },
    { name: "PPC27", selector: (row) => row.PPC27, width: "180px" },
    { name: "PPC28", selector: (row) => row.PPC28, width: "180px" },
    { name: "PPC29", selector: (row) => row.PPC29, width: "180px" },
    { name: "PPC30", selector: (row) => row.PPC30, width: "180px" },
    { name: "PPC31", selector: (row) => row.PPC31, width: "180px" },
    { name: "PPC32", selector: (row) => row.PPC32, width: "180px" },
    { name: "PPC33", selector: (row) => row.PPC33, width: "180px" },
    { name: "PPC34", selector: (row) => row.PPC34, width: "180px" },
    { name: "PPC35", selector: (row) => row.PPC35, width: "180px" },
    { name: "PPC36", selector: (row) => row.PPC36, width: "180px" },
    { name: "PMT1", selector: (row) => row.PMT1, width: "180px" },
    { name: "PMT2", selector: (row) => row.PMT2, width: "180px" },
    { name: "PMT3", selector: (row) => row.PMT3, width: "180px" },
    { name: "PMT4", selector: (row) => row.PMT4, width: "180px" },
    { name: "PMT5", selector: (row) => row.PMT5, width: "180px" },
    { name: "PMT6", selector: (row) => row.PMT6, width: "180px" },
    { name: "PMT7", selector: (row) => row.PMT7, width: "180px" },
    { name: "PMT8", selector: (row) => row.PMT8, width: "180px" },
    { name: "PMT9", selector: (row) => row.PMT9, width: "180px" },
    { name: "PMT10", selector: (row) => row.PMT10, width: "180px" },
    { name: "PMT11", selector: (row) => row.PMT11, width: "180px" },
    { name: "PMT12", selector: (row) => row.PMT12, width: "180px" },
    { name: "PMT13", selector: (row) => row.PMT13, width: "180px" },
    { name: "PMT14", selector: (row) => row.PMT14, width: "180px" },
    { name: "PMT15", selector: (row) => row.PMT15, width: "180px" },
    { name: "PMT16", selector: (row) => row.PMT16, width: "180px" },
    { name: "PMT17", selector: (row) => row.PMT17, width: "180px" },
    { name: "PMT18", selector: (row) => row.PMT18, width: "180px" },
    { name: "PMT19", selector: (row) => row.PMT19, width: "180px" },
    { name: "PMT20", selector: (row) => row.PMT20, width: "180px" },
    { name: "PMT21", selector: (row) => row.PMT21, width: "180px" },
    { name: "PMT22", selector: (row) => row.PMT22, width: "180px" },
    { name: "PMT23", selector: (row) => row.PMT23, width: "180px" },
    { name: "PMT24", selector: (row) => row.PMT24, width: "180px" },
    { name: "PMT25", selector: (row) => row.PMT25, width: "180px" },
    { name: "PMT26", selector: (row) => row.PMT26, width: "180px" },
    { name: "PMT27", selector: (row) => row.PMT27, width: "180px" },
    { name: "PMT28", selector: (row) => row.PMT28, width: "180px" },
    { name: "PMT29", selector: (row) => row.PMT29, width: "180px" },
    { name: "PMT30", selector: (row) => row.PMT30, width: "180px" },
    { name: "PMT31", selector: (row) => row.PMT31, width: "180px" },
    { name: "PMT32", selector: (row) => row.PMT32, width: "180px" },
    { name: "PMT33", selector: (row) => row.PMT33, width: "180px" },
    { name: "PMT34", selector: (row) => row.PMT34, width: "180px" },
    { name: "PMT35", selector: (row) => row.PMT35, width: "180px" },
    { name: "PMT36", selector: (row) => row.PMT36, width: "180px" },
    { name: "PPT1", selector: (row) => row.PPT1, width: "180px" },
    { name: "PPT2", selector: (row) => row.PPT2, width: "180px" },
    { name: "PPT3", selector: (row) => row.PPT3, width: "180px" },
    { name: "PPT4", selector: (row) => row.PPT4, width: "180px" },
    { name: "PPT5", selector: (row) => row.PPT5, width: "180px" },
    { name: "PPT6", selector: (row) => row.PPT6, width: "180px" },
    { name: "PPT7", selector: (row) => row.PPT7, width: "180px" },
    { name: "PPT8", selector: (row) => row.PPT8, width: "180px" },
    { name: "PPT9", selector: (row) => row.PPT9, width: "180px" },
    { name: "PPT10", selector: (row) => row.PPT10, width: "180px" },
    { name: "PPT11", selector: (row) => row.PPT11, width: "180px" },
    { name: "PPT12", selector: (row) => row.PPT12, width: "180px" },
    { name: "PPT13", selector: (row) => row.PPT13, width: "180px" },
    { name: "PPT14", selector: (row) => row.PPT14, width: "180px" },
    { name: "PPT15", selector: (row) => row.PPT15, width: "180px" },
    { name: "PPT16", selector: (row) => row.PPT16, width: "180px" },
    { name: "PPT17", selector: (row) => row.PPT17, width: "180px" },
    { name: "PPT18", selector: (row) => row.PPT18, width: "180px" },
    { name: "PPT19", selector: (row) => row.PPT19, width: "180px" },
    { name: "PPT20", selector: (row) => row.PPT20, width: "180px" },
    { name: "PPT21", selector: (row) => row.PPT21, width: "180px" },
    { name: "PPT22", selector: (row) => row.PPT22, width: "180px" },
    { name: "PPT23", selector: (row) => row.PPT23, width: "180px" },
    { name: "PPT24", selector: (row) => row.PPT24, width: "180px" },
    { name: "PPT25", selector: (row) => row.PPT25, width: "180px" },
    { name: "PPT26", selector: (row) => row.PPT26, width: "180px" },
    { name: "PPT27", selector: (row) => row.PPT27, width: "180px" },
    { name: "PPT28", selector: (row) => row.PPT28, width: "180px" },
    { name: "PPT29", selector: (row) => row.PPT29, width: "180px" },
    { name: "PPT30", selector: (row) => row.PPT30, width: "180px" },
    { name: "PPT31", selector: (row) => row.PPT31, width: "180px" },
    { name: "PPT32", selector: (row) => row.PPT32, width: "180px" },
    { name: "PPT33", selector: (row) => row.PPT33, width: "180px" },
    { name: "PPT34", selector: (row) => row.PPT34, width: "180px" },
    { name: "PPT35", selector: (row) => row.PPT35, width: "180px" },
    { name: "PPT36", selector: (row) => row.PPT36, width: "180px" },
    { name: "PPV1", selector: (row) => row.PPV1, width: "180px" },
    { name: "PPV2", selector: (row) => row.PPV2, width: "180px" },
    { name: "PPV3", selector: (row) => row.PPV3, width: "180px" },
    { name: "PPV4", selector: (row) => row.PPV4, width: "180px" },
    { name: "PPV5", selector: (row) => row.PPV5, width: "180px" },
    { name: "PPV6", selector: (row) => row.PPV6, width: "180px" },
    { name: "PPV7", selector: (row) => row.PPV7, width: "180px" },
    { name: "PPV8", selector: (row) => row.PPV8, width: "180px" },
    { name: "PPV9", selector: (row) => row.PPV9, width: "180px" },
    { name: "PPV10", selector: (row) => row.PPV10, width: "180px" },
    { name: "PPV11", selector: (row) => row.PPV11, width: "180px" },
    { name: "PPV12", selector: (row) => row.PPV12, width: "180px" },
    { name: "PPV13", selector: (row) => row.PPV13, width: "180px" },
    { name: "PPV14", selector: (row) => row.PPV14, width: "180px" },
    { name: "PPV15", selector: (row) => row.PPV15, width: "180px" },
    { name: "PPV16", selector: (row) => row.PPV16, width: "180px" },
    { name: "PPV17", selector: (row) => row.PPV17, width: "180px" },
    { name: "PPV18", selector: (row) => row.PPV18, width: "180px" },
    { name: "PPV19", selector: (row) => row.PPV19, width: "180px" },
    { name: "PPV20", selector: (row) => row.PPV20, width: "180px" },
    { name: "PPV21", selector: (row) => row.PPV21, width: "180px" },
    { name: "PPV22", selector: (row) => row.PPV22, width: "180px" },
    { name: "PPV23", selector: (row) => row.PPV23, width: "180px" },
    { name: "PPV24", selector: (row) => row.PPV24, width: "180px" },
    { name: "PPV25", selector: (row) => row.PPV25, width: "180px" },
    { name: "PPV26", selector: (row) => row.PPV26, width: "180px" },
    { name: "PPV27", selector: (row) => row.PPV27, width: "180px" },
    { name: "PPV28", selector: (row) => row.PPV28, width: "180px" },
    { name: "PPV29", selector: (row) => row.PPV29, width: "180px" },
    { name: "PPV30", selector: (row) => row.PPV30, width: "180px" },
    { name: "PPV31", selector: (row) => row.PPV31, width: "180px" },
    { name: "PPV32", selector: (row) => row.PPV32, width: "180px" },
    { name: "PPV33", selector: (row) => row.PPV33, width: "180px" },
    { name: "PPV34", selector: (row) => row.PPV34, width: "180px" },
    { name: "PPV35", selector: (row) => row.PPV35, width: "180px" },
    { name: "PPV36", selector: (row) => row.PPV36, width: "180px" },
    { name: "Ps_Input", selector: (row) => row.Ps_Input, width: "180px" },
    { name: "Into_I", selector: (row) => row.Into_I, width: "180px" },
    { name: "Calc_Chk", selector: (row) => row.Calc_Chk, width: "180px" },
    {
      name: "Fix_Unit_Price",
      selector: (row) => row.Fix_Unit_Price,
      width: "180px",
    },
    {
      name: "Target_Date",
      selector: (row) => {
        const date = new Date(row.Target_Delivery);

        if (isNaN(date.getTime())) return "";

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");

        return `${year}/${month}/${day}`;
      },
      width: "180px",
    },

    { name: "Week", selector: (row) => row.Week, width: "180px" },
    { name: "Week_No", selector: (row) => row.Week_No, width: "180px" },
    { name: "Amount", selector: (row) => row.Amount, width: "180px" },
  ];

  return (
    <>
      <div className="flex items-center justify-center py-5">
        <span className="text-xl font-semibold">Plan_WI</span>
      </div>

      <div className="flex pb-5 text-lg">
        <input
          className="border-2 border-gray-500 rounded-md w-52 h-9"
          type="text"
          placeholder=" Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <DataTable
        columns={columns}
        data={filteredData}
        pagination
        paginationPerPage={5}
        paginationRowsPerPageOptions={[5, 10, 15, 20]}
        customStyles={{
          rows: {
            style: {
              "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
              "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
              minHeight: "50px",
              textAlign: "center",
              justifyContent: "center",
              borderBottom: "1px solid #ccc",
              borderRight: "1px solid #ccc",
            },
          },
          headCells: {
            style: {
              backgroundColor: "#DCDCDC",
              fontSize: "14px",
              textAlign: "center",
              justifyContent: "center",
              border: "1px solid #ccc",
            },
          },
          cells: {
            style: {
              textAlign: "center",
              justifyContent: "center",
              border: "1px solid #ccc",
            },
          },
          table: {
            style: {
              borderCollapse: "collapse",
            },
          },
        }}
      />
    </>
  );
}
