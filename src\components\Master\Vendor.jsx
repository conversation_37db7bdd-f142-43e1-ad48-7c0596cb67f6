import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Vendor() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [isChanged, setIsChanged] = useState(false);
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const editedDataRef = useRef(editedData);

  const openModal = () => {
    if (selectedRowForCopy) {
      const { Vendor_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Vendor_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Vendor_CD: "",
        Vendor_Name: "",
        Vendor_Name2: "",
        Vendor_Abb: "",
        Vendor_Add: "",
        Vendor_Add2: "",
        Vendor_Add3: "",
        Vendor_Contact: "",
        Vendor_TEL: "",
        Posting_Group: "",
        Payment_CD: "",
        Blocked: false,
        VAT_Reg_No: "",
        Branch_No: "",
        Nationality: "",
        Vendor_Group: "",
        Vendor_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const [formData, setFormData] = useState({
    Vendor_CD: "",
    Vendor_Name: "",
    Vendor_Name2: "",
    Vendor_Abb: "",
    Vendor_Add: "",
    Vendor_Add2: "",
    Vendor_Add3: "",
    Vendor_Contact: "",
    Vendor_TEL: "",
    Posting_Group: "",
    Payment_CD: "",
    Blocked: false,
    VAT_Reg_No: "",
    Branch_No: "",
    Nationality: "",
    Vendor_Group: "",
    Vendor_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleSaveVendor = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem("authToken");
      const response = await axios.post(
        `${apiUrl_4000}/vendor/create-vendor`, // API URL
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Vendor created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        // อัปเดตข้อมูลในตารางหลังจากสร้างสำเร็จ
        fetchVendor();

        // ล้างค่าฟอร์ม
        setFormData({
          Vendor_CD: "",
          Vendor_Name: "",
          Vendor_Name2: "",
          Vendor_Abb: "",
          Vendor_Add: "",
          Vendor_Add2: "",
          Vendor_Add3: "",
          Vendor_Contact: "",
          Vendor_TEL: "",
          Posting_Group: "",
          Payment_CD: "",
          Blocked: false,
          VAT_Reg_No: "",
          Branch_No: "",
          Nationality: "",
          Vendor_Group: "",
          Vendor_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        // ปิด Modal
        closeModal();
      }
    } catch (error) {
      console.error("Error creating vendor:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create vendor.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const [selectedVendors, setSelectedVendors] = useState([]);
  // จัดการการเปลี่ยนแปลง checkbox
  const handleCheckboxChange = (e, vendorCd) => {
    if (e.target.checked) {
      setSelectedVendors((prev) => [...prev, vendorCd]); // เพิ่ม vendorCd ที่เลือก
    } else {
      setSelectedVendors((prev) => prev.filter((cd) => cd !== vendorCd)); // เอา vendorCd ที่ยกเลิกออก
    }
  };

  const handleDeleteClick = async () => {
    if (selectedVendors.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const vendorList = selectedVendors.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following vendors?<br>Vendor CDs: ${vendorList}`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/vendor/delete-vendor`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedVendors.map((vendorCd) => ({ Vendor_CD: vendorCd })),
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: "The selected vendors have been deleted.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // รีเฟรชข้อมูลจาก API โดยดึงข้อมูลใหม่
          await fetchVendor(); // ตรวจสอบว่า fetchVendor ทำงานได้

          // รีเซ็ตการเลือก checkbox ทั้งหมด
          setSelectedVendors([]); // ล้างรายการที่เลือก

          // ตัวเลือก checkbox ต้องถูกเคลียร์ใน UI
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ตสถานะของ checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteClick:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 1?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 1 หรือไม่?<br>データは編集されました。master 1 に戻りますか？"
          : "Do you want to go back to master 1?<br>คุณต้องการกลับไปที่หน้า master 1 หรือไม่?<br>master 1 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master1");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const fetchVendor = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/vendor/fetch-vendor`);
      // console.log("Fetched data:", response.data);
      setData(response.data.data.vendor || []);
    } catch (error) {
      // console.error("Error fetching vendor:", error);
    }
  };

  useEffect(() => {
    fetchVendor();
  }, []);

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const handleChange = (e, vendorCd, field) => {
    const newValue = e.target.value;

    if (editedDataRef.current[vendorCd]?.[field] !== newValue) {
      setIsChanged(true);

      const updatedData = { ...editedDataRef.current };

      updatedData[vendorCd] = updatedData[vendorCd] || {};
      updatedData[vendorCd][field] = newValue;

      setEditedData(updatedData);
      editedDataRef.current = updatedData;
    }
  };

  const handleSave = async (vendorCd, field) => {
    const newValue = editedData[vendorCd]?.[field];
    const oldValue = data.find((row) => row.Vendor_CD === vendorCd)?.[field];

    if (newValue !== oldValue) {
      try {
        const payload = {
          Vendor_CD: vendorCd,
          [field]: newValue === "" ? null : newValue,
        };

        const response = await axios.put(
          `${apiUrl_4000}/vendor/update-vendor`,
          payload
        );

        const updatedData = [...data];
        const rowIndex = updatedData.findIndex(
          (row) => row.Vendor_CD === vendorCd
        );
        if (rowIndex !== -1) {
          updatedData[rowIndex][field] = newValue;
          setData(updatedData);
        }

        // เปลี่ยนข้อความใน SweetAlert เป็น "Edit Successfully"
        Swal.fire({
          title: "Success",
          text: "Edit Successfully",
          icon: "success",
          confirmButtonText: "OK",
        });

        setIsChanged(false);
      } catch (error) {
        Swal.fire({
          title: "Error",
          text: "Something went wrong!",
          icon: "error",
          confirmButtonText: "OK",
        });
        console.error(error);
      }
    }
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Vendor_CD: row.Vendor_CD,
      Vendor_Name: row.Vendor_Name,
      Vendor_Name2: row.Vendor_Name2,
      Vendor_Abb: row.Vendor_Abb,
      Vendor_Add: row.Vendor_Add,
      Vendor_Add2: row.Vendor_Add2,
      Vendor_Add3: row.Vendor_Add3,
      Vendor_Contact: row.Vendor_Contact,
      Vendor_TEL: row.Vendor_TEL,
      Posting_Group: row.Posting_Group,
      Payment_CD: row.Payment_CD,
      Blocked: row.Blocked,
      VAT_Reg_No: row.VAT_Reg_No,
      Branch_No: row.Branch_No,
      Nationality: row.Nationality,
      Vendor_Group: row.Vendor_Group,
      Vendor_Remark: row.Vendor_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Vendor_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy && selectedRowForCopy.Vendor_CD === row.Vendor_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            checked={selectedVendors.includes(row.Vendor_CD)} // ตรวจสอบว่าถูกเลือกหรือไม่
            onChange={(e) => handleCheckboxChange(e, row.Vendor_CD)} // จัดการการเลือก
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Vendor_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Vendor_CD !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_CD
              : row.Vendor_CD || ""
          }
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_CD")}
          disabled
        />
      ),
      width: "190px",
    },
    {
      name: "Vendor_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "250px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Name !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Name
              : row.Vendor_Name || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Name")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Name")}
        />
      ),
      width: "auto",
    },
    {
      name: "Vendor_Name2",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "250px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Name2 !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Name2
              : row.Vendor_Name2 || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Name2")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Name2")}
        />
      ),
      width: "auto",
    },
    {
      name: "Vendor_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Vendor_Abb !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Abb
              : row.Vendor_Abb || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Abb")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Abb")}
        />
      ),
      width: "200px",
    },
    {
      name: "Vendor_Add",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "370px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Add !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Add
              : row.Vendor_Add || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Add")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Add")}
        />
      ),
      width: "410px",
    },
    {
      name: "Vendor_Add2",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "370px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Add2 !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Add2
              : row.Vendor_Add2 || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Add2")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Add2")}
        />
      ),
      width: "410px",
    },
    {
      name: "Vendor_Add3",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "300px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Add3 !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Add3
              : row.Vendor_Add3 || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Add3")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Add3")}
        />
      ),
      width: "350px",
    },
    {
      name: "Vendor_Contact",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "300px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Contact !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Contact
              : row.Vendor_Contact || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Contact")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Contact")}
        />
      ),
      width: "350px",
    },
    {
      name: "Vendor_TEL",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Vendor_TEL !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_TEL
              : row.Vendor_TEL || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_TEL")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_TEL")}
        />
      ),
      width: "220px",
    },
    {
      name: "Posting_Group",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Posting_Group !== undefined
              ? editedData[row.Vendor_CD]?.Posting_Group
              : row.Posting_Group || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Posting_Group")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Posting_Group")}
        />
      ),
      width: "220px",
    },
    {
      name: "Payment_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Payment_CD !== undefined
              ? editedData[row.Vendor_CD]?.Payment_CD
              : row.Payment_CD || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Payment_CD")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Payment_CD")}
        />
      ),
      width: "200px",
    },
    {
      name: "Blocked",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Blocked}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Blocked")}
          className="mx-auto"
        />
      ),
      width: "180px",
    },
    {
      name: "VAT_Reg_No",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.VAT_Reg_No !== undefined
              ? editedData[row.Vendor_CD]?.VAT_Reg_No
              : row.VAT_Reg_No || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "VAT_Reg_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "VAT_Reg_No")}
        />
      ),
      width: "200px",
    },
    {
      name: "Branch_No",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Branch_No !== undefined
              ? editedData[row.Vendor_CD]?.Branch_No
              : row.Branch_No || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Branch_No")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Branch_No")}
        />
      ),
      width: "190px",
    },
    {
      name: "Nationality",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Nationality !== undefined
              ? editedData[row.Vendor_CD]?.Nationality
              : row.Nationality || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Nationality")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Nationality")}
        />
      ),
      width: "190px",
    },
    {
      name: "Vendor_Group",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Vendor_CD]?.Vendor_Group !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Group
              : row.Vendor_Group || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Group")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Group")}
        />
      ),
      width: "190px",
    },
    {
      name: "Vendor_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "250px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Vendor_CD]?.Vendor_Remark !== undefined
              ? editedData[row.Vendor_CD]?.Vendor_Remark
              : row.Vendor_Remark || ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Vendor_CD, "Vendor_Remark")}
          onKeyDown={(e) => handleKeyDown(e, row.Vendor_CD, "Vendor_Remark")}
        />
      ),
      width: "300px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Vendor <br /> 仕入先マスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Vendor</h2>
                  <form onSubmit={handleSaveVendor}>
                    {/* Vendor_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_CD
                      </label>
                      <input
                        type="text"
                        name="Vendor_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Name
                      </label>
                      <input
                        type="text"
                        name="Vendor_Name"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Name}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Name2 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Name2
                      </label>
                      <input
                        type="text"
                        name="Vendor_Name2"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Name2}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Abb
                      </label>
                      <input
                        type="text"
                        name="Vendor_Abb"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Abb}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Add */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Add
                      </label>
                      <input
                        type="text"
                        name="Vendor_Add"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Add}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Add2 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Add2
                      </label>
                      <input
                        type="text"
                        name="Vendor_Add2"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Add2}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Add3 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Add3
                      </label>
                      <input
                        type="text"
                        name="Vendor_Add3"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Add3}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Contact */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Contact
                      </label>
                      <input
                        type="text"
                        name="Vendor_Contact"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Contact}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_TEL */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_TEL
                      </label>
                      <input
                        type="text"
                        name="Vendor_TEL"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_TEL}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Posting_Group */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Posting_Group
                      </label>
                      <input
                        type="text"
                        name="Posting_Group"
                        className="w-full p-2 border rounded-md"
                        value={formData.Posting_Group}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Payment_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Payment_CD
                      </label>
                      <input
                        type="text"
                        name="Payment_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.Payment_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Blocked */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Blocked
                      </label>
                      <input
                        type="checkbox"
                        name="Blocked"
                        className="w-4 h-4"
                        checked={formData.Blocked}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* VAT_Reg_No */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        VAT_Reg_No
                      </label>
                      <input
                        type="text"
                        name="VAT_Reg_No"
                        className="w-full p-2 border rounded-md"
                        value={formData.VAT_Reg_No}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Branch_No */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Branch_No
                      </label>
                      <input
                        type="text"
                        name="Branch_No"
                        className="w-full p-2 border rounded-md"
                        value={formData.Branch_No}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Nationality */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Nationality
                      </label>
                      <input
                        type="text"
                        name="Nationality"
                        className="w-full p-2 border rounded-md"
                        value={formData.Nationality}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Group */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Group
                      </label>
                      <input
                        type="text"
                        name="Vendor_Group"
                        className="w-full p-2 border rounded-md"
                        value={formData.Vendor_Group}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Vendor_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Vendor_Remark
                      </label>
                      <textarea
                        name="Vendor_Remark"
                        className="w-full p-2 border rounded-md h-24 overflow-y-auto"
                        value={formData.Vendor_Remark}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Vendor_CD]?.[field] !== undefined) {
                      handleSave(row.Vendor_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteClick}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
