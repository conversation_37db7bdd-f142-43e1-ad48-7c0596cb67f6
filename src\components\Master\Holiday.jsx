import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

// icons
import { FaPlus, FaTimes } from "react-icons/fa";

export function Holiday() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [editedData, setEditedData] = useState({});
  const editedDataRef = useRef(editedData);

  const fetchHolidays = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/holiday/fetch-holiday`);
      const formattedData = response.data.data.map((row) => ({
        ...row,
        Holiday: formatDateForInput(row.Holiday),
      }));
      // console.log("Fetched data:", response.data);
      setData(formattedData);
    } catch (error) {
      // console.error("Error fetching holiday:", error);
    }
  };

  useEffect(() => {
    fetchHolidays();
  }, []);

  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    if (isNaN(date)) return "";

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleChange = (e, holiday, field) => {
    setEditedData({
      ...editedData,
      [holiday]: {
        ...editedData[holiday],
        [field]: e.target.value,
      },
    });
  };

  const [holidays, setHolidays] = useState([{ Holiday: "" }]);

  const handleInputChangeForAddnew = (e, index) => {
    const { name, value } = e.target;
    const updatedHolidays = [...holidays];
    updatedHolidays[index] = { ...updatedHolidays[index], [name]: value };
    setHolidays(updatedHolidays);
  };

  const handleAddHoliday = () => {
    if (holidays.length < 5) {
      setHolidays([...holidays, { Holiday: "" }]);
    } else {
      Swal.fire({
        title: "Error",
        text: "You can only add up to 5 holidays.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleRemoveHoliday = (index) => {
    const updatedHolidays = holidays.filter((_, i) => i !== index);
    setHolidays(updatedHolidays);
  };

  // Check if there are any empty holiday inputs
  const checkEmptyHoliday = () => {
    return holidays.some((holiday) => !holiday.Holiday); // ถ้ามีค่า Holiday ว่างจะส่งกลับเป็น true
  };

  // Check if there are duplicate holidays in the form
  const checkDuplicateInForm = () => {
    const holidayValues = holidays.map((holiday) => holiday.Holiday);
    const uniqueHolidays = new Set(holidayValues);

    return holidayValues.length !== uniqueHolidays.size;
  };

  // Check if there are duplicate holidays in the database
  const checkDuplicateHoliday = async (newHoliday) => {
    try {
      const response = await axios.get(`${apiUrl_4000}/holiday/fetch-holiday`);
      const existingHolidays = response.data.data.map((row) => row.Holiday);

      // ตรวจสอบว่าในฐานข้อมูลมี Holiday นี้อยู่แล้วหรือไม่
      if (existingHolidays.includes(newHoliday)) {
        Swal.fire({
          title: "Duplicate Holiday",
          text: "This holiday already exists in the database.",
          icon: "error",
          confirmButtonText: "OK",
        });
        return true; // หากมีข้อมูลซ้ำ ให้คืนค่า true
      }
      return false; // หากไม่ซ้ำ
    } catch (error) {
      console.error("Error checking duplicates:", error);
      return false; // หากไม่สามารถตรวจสอบได้ ให้ไม่ทำการบันทึก
    }
  };

  const handleCreateHoliday = async (e) => {
    e.preventDefault();

    try {
      const token = localStorage.getItem("authToken");

      // ตรวจสอบข้อมูล holiday ที่จะส่งไปว่ามีข้อมูลซ้ำหรือไม่
      for (let holiday of holidays) {
        const isDuplicate = await checkDuplicateHoliday(holiday.Holiday);
        if (isDuplicate) {
          return; // หากพบข้อมูลซ้ำ ให้หยุดการทำงาน
        }
      }

      const dataToSend = holidays.map((holiday) => ({
        Holiday: holiday.Holiday,
      }));

      const response = await axios.post(
        `${apiUrl_4000}/holiday/create-holiday`,
        dataToSend,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Holidays created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });
        fetchHolidays();
        setHolidays([{ Holiday: "" }]);
        closeModal();
      }
    } catch (error) {
      console.error("Error creating Holiday:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create Holidays.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleUpdateHoliday = async (holiday) => {
    const payload = { Holiday: holiday };
    let isEdited = false;

    const editedFields = Object.keys(editedData[holiday] || {});

    for (const field of editedFields) {
      const newValue = editedData[holiday]?.[field];
      const oldValue = data.find((row) => row.Holiday === holiday)?.[field];

      if (newValue !== oldValue) {
        // ตรวจสอบฟิลด์ Coefficient
        if (field === "Coefficient" && (newValue > 100 || newValue < 1)) {
          Swal.fire({
            title: "Error",
            text: "Coefficient must be between 1 and 100.",
            icon: "error",
            confirmButtonText: "OK",
          });
          return; // หยุดการทำงานหากค่าไม่ถูกต้อง
        }

        payload[field] = newValue === "" ? null : newValue;
        isEdited = true;
      }
    }

    // ตัดการแจ้งเตือนออก
    if (!isEdited) {
      return; // ไม่ทำอะไรหากไม่มีการเปลี่ยนแปลง
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const response = await axios.put(
        `${apiUrl_4000}/holiday/update-holiday`,
        payload
      );

      const updatedData = [...data];
      const rowIndex = updatedData.findIndex((row) => row.Holiday === holiday);
      if (rowIndex !== -1) {
        Object.keys(payload).forEach((field) => {
          if (field !== "Holiday") {
            updatedData[rowIndex][field] = payload[field];
          }
        });
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "Holiday data has been updated.",
      });
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const [selectedHolidays, setSelectedHolidays] = useState([]);

  const handleCheckboxChange = (e, holiday) => {
    if (e.target.checked) {
      setSelectedHolidays([...selectedHolidays, holiday]);
    } else {
      setSelectedHolidays(selectedHolidays.filter((cd) => cd !== holiday));
    }
  };

  const handleDeleteClick = async () => {
    if (selectedHolidays.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const holidayList = selectedHolidays.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>Holidays: <b>${holidayList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/holiday/delete-holiday`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedHolidays.map((holiday) => ({ Holiday: holiday })), // ส่งข้อมูล Holiday
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: `The selected Holidays have been deleted.`,
            icon: "success",
            confirmButtonText: "OK",
          });

          // อัปเดตข้อมูลใน state
          setData(
            data.filter((row) => !selectedHolidays.includes(row.Holiday))
          );
          setSelectedHolidays([]); // เคลียร์การเลือก
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ต checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteClick:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row) => {
      if (!editedData[row.Holiday]) {
        acc[row.Holiday] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const filteredData = data
    .filter((row) =>
      Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
    .sort((a, b) => new Date(b.Holiday) - new Date(a.Holiday)); // เรียงจากใหม่ไปเก่า

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 3?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 3 หรือไม่?<br>データは編集されました。master 3 に戻りますか？"
          : "Do you want to go back to master 3?<br>คุณต้องการกลับไปที่หน้า master 3 หรือไม่?<br>master 3 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master3");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Holiday: row.Holiday,
      Holiday_Name: row.Holiday_Name,
      Coefficient: row.Coefficient,
      Holiday_Remark: row.Holiday_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Holiday_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "Select",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            onChange={(e) => handleCheckboxChange(e, row.Holiday)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "100px",
      omit: !showCheckbox,
    },
    {
      name: "Holiday",
      selector: (row) => {
        const date = row.Holiday ? new Date(row.Holiday) : null;
        if (!date || isNaN(date)) return "";

        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear() + 543;

        return `${day}/${month}/${year}`;
      },
      width: "190px",
      cell: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="date"
          value={
            editedData[row.Holiday]?.Holiday ??
            formatDateForInput(row.Holiday) ??
            ""
          }
          onChange={(e) => handleChange(e, row.Holiday, "Holiday")}
          disabled
        />
      ),
    },
    {
      name: "Holiday_Name",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Holiday]?.Holiday_Name ?? row.Holiday_Name ?? ""
          }
          onChange={(e) => handleChange(e, row.Holiday, "Holiday_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Coefficient",
      cell: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={editedData[row.Holiday]?.Coefficient ?? row.Coefficient ?? ""}
          onChange={(e) => handleChange(e, row.Holiday, "Coefficient")}
          disabled={!isF2Pressed}
        />
      ),
      width: "150px",
    },
    {
      name: "Holiday_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "240px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Holiday]?.Holiday_Remark ?? row.Holiday_Remark ?? ""
          }
          onChange={(e) => handleChange(e, row.Holiday, "Holiday_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "280px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <h1 className="text-2xl font-bold text-center mt-3">
              Holiday <br /> 休日設定
            </h1>
            <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

            <div className="ml-5 text-lg flex justify-between">
              <input
                className="border-2 border-gray-500 rounded-md w-52 h-9"
                type="text"
                placeholder=" Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button
                onClick={exportToCsv}
                className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
              >
                Export to CSV
              </button>
            </div>

            <div className="flex justify-left items-center mt-5 mb-3">
              <div className="w-full sm:w-auto text-center px-5">
                <DataTable
                  columns={columns}
                  data={filteredData}
                  pagination
                  paginationPerPage={10}
                  paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                  customStyles={{
                    rows: {
                      style: {
                        "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                        "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                        minHeight: "50px",
                        textAlign: "center",
                        justifyContent: "center",
                        borderBottom: "1px solid #ccc",
                        borderRight: "1px solid #ccc",
                      },
                    },
                    headCells: {
                      style: {
                        backgroundColor: "#DCDCDC",
                        fontSize: "14px",
                        textAlign: "center",
                        justifyContent: "center",
                        border: "1px solid #ccc",
                      },
                    },
                    cells: {
                      style: {
                        textAlign: "center",
                        justifyContent: "center",
                        border: "1px solid #ccc",
                      },
                    },
                    table: {
                      style: {
                        borderCollapse: "collapse",
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add Holiday</h2>
                  <form onSubmit={handleCreateHoliday}>
                    {holidays.map((holiday, index) => (
                      <div key={index} className="mb-3">
                        <div className="mb-3">
                          <div className="flex flex-row justify-between">
                            <label className="block text-sm font-medium text-gray-700">
                              Holiday
                            </label>
                            {holidays.length > 1 && (
                              <button
                                type="button"
                                onClick={() => handleRemoveHoliday(index)}
                                className="text-red-500 p-1 text-lg"
                              >
                                <FaTimes /> {/* ไอคอน X */}
                              </button>
                            )}
                          </div>
                          <input
                            type="date"
                            name="Holiday"
                            className="w-full p-2 border rounded-md"
                            value={holiday.Holiday}
                            onChange={(e) =>
                              handleInputChangeForAddnew(e, index)
                            }
                          />
                        </div>
                      </div>
                    ))}

                    <button
                      type="button"
                      onClick={handleAddHoliday}
                      className="text-green-500 border border-green-500 hover:bg-green-500 hover:text-white rounded px-2 py-1 text-lg flex items-center gap-1"
                    >
                      Add <FaPlus /> {/* ไอคอน + */}
                    </button>

                    <div className="flex justify-end gap-2 pt-4">
                      <button
                        type="submit"
                        className={`bg-blue-500 p-3 rounded-lg text-white ${
                          checkDuplicateInForm() || checkEmptyHoliday()
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                        disabled={checkDuplicateInForm()} // ปิดปุ่ม Save ถ้ามีข้อมูลซ้ำ
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-red-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Holiday]?.[field] !== undefined) {
                      handleUpdateHoliday(row.Holiday, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteClick}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
