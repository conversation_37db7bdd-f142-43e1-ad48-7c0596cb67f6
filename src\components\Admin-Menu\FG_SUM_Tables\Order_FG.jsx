import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import DataTable from "react-data-table-component";
import axios from "axios";

export function Order_FG() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);

  // ดึงค่าจาก URL
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const targetYear = queryParams.get("year");
  const targetMonth = queryParams.get("month");

  const fetchData = async () => {
    if (!targetYear || !targetMonth) {
      console.error("Missing targetYear or targetMonth");
      return;
    }
  
    // ลบเฉพาะข้อมูล fgSumTable ออกจาก LocalStorage
    localStorage.removeItem('fgSumTable');  
  
    // เช็คข้อมูลใน LocalStorage
    const cachedData = localStorage.getItem(`fgSumTable`);
    if (cachedData) {
      console.log("Local Storage still has cached data");
      setData(JSON.parse(cachedData).QD_Order_FG);  // ดึงจากโครงสร้างใหม่
      return;
    }
  
    // ถ้าไม่มีข้อมูลใน LocalStorage ให้เรียก API
    const payload = { targetMonth, targetYear };
  
    try {
      const response = await axios.post(`${apiUrl_4000}/fg-sum/order-fg`, payload);
      if (response.data.status === "success") {
        const formattedData = { QD_Order_FG: response.data.data };  // ครอบข้อมูลด้วย QD_Order_FG
        setData(formattedData.QD_Order_FG);
        localStorage.setItem(`fgSumTable`, JSON.stringify(formattedData));  
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      alert("Error fetching data");
    }
  };
  
  useEffect(() => {
    fetchData();
  }, [targetYear, targetMonth]);

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const columns = [
    { name: "Product_Grp_CD", selector: (row) => row.Product_Grp_CD, width: "180px" },
    { name: "Item1_CD", selector: (row) => row.Item1_CD, width: "180px" },
    { name: "Item1", selector: (row) => row.Item1, width: "180px" },
    { name: "Item1_Grp", selector: (row) => row.Item1_Grp, width: "180px" },
    { name: "Customer_CD", selector: (row) => row.Customer_CD, width: "180px" },
    { name: "Customer", selector: (row) => row.Customer, width: "180px" },
    { name: "Order_Type", selector: (row) => row.Order_Type, width: "180px" },
    { name: "Order_No", selector: (row) => row.Order_No, width: "180px" },
    { name: "Parts_No", selector: (row) => row.Parts_No, width: "180px" },
    { name: "Cost_No", selector: (row) => row.Cost_No, width: "180px" },
    { name: "Process_No", selector: (row) => row.Process_No, width: "180px" },
    { name: "OdPt_No", selector: (row) => row.OdPt_No, width: "180px" },
    { name: "OdPtCs_No", selector: (row) => row.OdPtCs_No, width: "180px" },
    { name: "OdPtPr_No", selector: (row) => row.OdPtPr_No, width: "180px" },
    { name: "CMC", selector: (row) => row.CMC, width: "180px" },
    { name: "CMT", selector: (row) => row.CMT, width: "180px" },
    { name: "CPC", selector: (row) => row.CPC, width: "180px" },
    { name: "CPT", selector: (row) => row.CPT, width: "180px" },
    { name: "CPD", selector: (row) => formatDate(row.CPD), width: "180px" },
    { name: "CPN", selector: (row) => row.CPN, width: "180px" },
    { name: "Cs_Progress_CD", selector: (row) => row.Cs_Progress_CD, width: "180px" },
    { name: "Cs_Complete_Date", selector: (row) => formatDate(row.Cs_Complete_Date), width: "180px" },
    { name: "Cs_Complete_Qty", selector: (row) => row.Cs_Complete_Qty, width: "180px" },
    {
      name: "Cs_Label_CSV",
      selector: (row) => row.Cs_Label_CSV,
      width: "180px",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Label_CSV === true}
          readOnly
        />
      ),
    },
    {
      name: "Cs_All_Complete",
      selector: (row) => row.Cs_All_Complete,
      width: "180px",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_All_Complete === true}
          readOnly
        />
      ),
    },
    {
      name: "Cs_Order_All_Complete",
      selector: (row) => row.Cs_Order_All_Complete,
      width: "180px",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Order_All_Complete === true}
          readOnly
        />
      ),
    },
    {
      name: "Cs_Parts_Complete",
      selector: (row) => row.Cs_Parts_Complete,
      width: "180px",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Parts_Complete === true}
          readOnly
        />
      ),
    },
    {
      name: "Cs_Final_Complete",
      selector: (row) => row.Cs_Final_Complete,
      width: "180px",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Cs_Final_Complete === true}
          readOnly
        />
      ),
    },
    { name: "Cs_Remark", selector: (row) => row.Cs_Remark, width: "180px" },
    { name: "Cs_Register_Date", selector: (row) => formatDate(row.Cs_Register_Date), width: "180px" },
    { name: "Cs_Modify_Date", selector: (row) => formatDate(row.Cs_Modify_Date), width: "180px" },
    { name: "Cs_Reg_Person_CD", selector: (row) => row.Cs_Reg_Person_CD, width: "180px" },
    { name: "Cs_Upd_Person_CD", selector: (row) => row.Cs_Upd_Person_CD, width: "180px" },
    { name: "Sequence_No", selector: (row) => row.Sequence_No, width: "180px" },
    { name: "ProcessCD", selector: (row) => row.ProcessCD, width: "180px" },
    { name: "Complete_Month", selector: (row) => row.Complete_Month, width: "180px" },
    { name: "Complete_Date", selector: (row) => row.Complete_Date, width: "180px" },
    { name: "Comp_Date", selector: (row) => row.Comp_Date, width: "180px" },
    { name: "Week", selector: (row) => row.Week, width: "180px" },
    { name: "Amount", selector: (row) => row.Amount, width: "180px" },
  ];

  return (
    <div className="p-4">
      <div className="flex items-center justify-center py-5">
        <span className="text-xl font-semibold">Order_FG</span>
      </div>

      <div className="flex pb-5 text-lg">
        <input
          className="border-2 border-gray-500 rounded-md w-52 h-9"
          type="text"
          placeholder=" Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <DataTable
        columns={columns}
        data={filteredData}
        pagination
        paginationPerPage={5}
        paginationRowsPerPageOptions={[5, 10, 15, 20]}
        customStyles={{
          rows: {
            style: {
              "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
              "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
              minHeight: "50px",
              textAlign: "center",
              justifyContent: "center",
              borderBottom: "1px solid #ccc",
              borderRight: "1px solid #ccc",
            },
          },
          headCells: {
            style: {
              backgroundColor: "#DCDCDC",
              fontSize: "14px",
              textAlign: "center",
              justifyContent: "center",
              border: "1px solid #ccc",
            },
          },
          cells: {
            style: {
              textAlign: "center",
              justifyContent: "center",
              border: "1px solid #ccc",
            },
          },
          table: {
            style: {
              borderCollapse: "collapse",
            },
          },
        }}
      />
    </div>
  );
}
