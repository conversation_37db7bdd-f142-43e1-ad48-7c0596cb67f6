import React, { useState, useEffect } from "react";

// Icons
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function QE_Od_List_Detail() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [orderListData, setOrderListData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }
      const { status, data } = event.data;
      if (status === "success") {
        setOrderListData(data);
      } else {
        console.error("Failed to receive data.");
      }
    };

    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  const totalPages = Math.ceil(orderListData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = orderListData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const exportToCSV = () => {
    if (orderListData.length === 0) return;
    const formatValue = (val) => (val === null || val === undefined ? "" : val);
    const headers = [
      "Order_No",
      "Product_Name",
      "Company_Draw",
      "Product_Draw",
      "Quantity",
      "Item1_CD",
      "I_Completed_Date",
      "RemarkG_Sync",
      "Customer_Abb",
    ];

    const rows = orderListData.map((order) => [
      order.Order_No,
      order.Product_Name || "",
      order.Company_Draw || "",
      order.Product_Draw || "",
      order.Quantity || "",
      order.Item1_CD || "",
      order.I_Completed_Date
        ? new Date(order.I_Completed_Date).toISOString().split("T")[0]
        : "",
      order.WorkG_Symbol || "",
      order.Customer_Abb || "",
    ]);

     const csvContent =
    headers.join(",") +
    "\n" +
    rows.map((row) => row.map((value) => `"${value}"`).join(",")).join("\n");

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);

  // สร้างชื่อไฟล์ตามวันที่และเวลา
  const now = new Date();
  const pad = (n) => String(n).padStart(2, "0");
  const fileName = `QE_Od_List_Detail_${now.getFullYear()}-${pad(
    now.getMonth() + 1
  )}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(
    now.getMinutes()
  )}-${pad(now.getSeconds())}.csv`;

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-center items-center my-3 font-semibold text-xl">
        <span>QE_Od_List_Detail</span>
      </div>

      {/* ปุ่ม Export CSV */}
      <div className="flex justify-end mb-4">
        <button
          onClick={exportToCSV}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Export CSV
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-300 rounded-lg shadow-lg">
          <thead className="bg-gray-200 text-gray-700">
            <tr>
              <th className="py-2 px-4 text-left border-b">Order_No</th>
              <th className="py-2 px-4 text-left border-b">Quantity</th>
              <th className="py-2 px-4 text-left border-b">Request_Delivery</th>
              <th className="py-2 px-4 text-left border-b">I_Completed_Date</th>
              <th className="py-2 px-4 text-left border-b">Customer_Abb</th>
              <th className="py-2 px-4 text-left border-b">NAV_Name</th>
              <th className="py-2 px-4 text-left border-b">NAV_Size</th>
            </tr>
          </thead>
          <tbody>
            {displayedData.length > 0 ? (
              displayedData.map((orders, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  <td className="py-2 px-4 border-b">{orders.Order_No}</td>
                  <td className="py-2 px-4 border-b">{orders.Quantity}</td>
                  <td className="py-2 px-4 border-b">
                    {(() => {
                      if (!orders.Request_Delivery) return "";
                      const date = new Date(orders.Request_Delivery);
                      const day = String(date.getDate()).padStart(2, "0");
                      const month = String(date.getMonth() + 1).padStart(
                        2,
                        "0"
                      );
                      const year = date.getFullYear();
                      return `${day}/${month}/${year}`;
                    })()}
                  </td>
                  <td className="py-2 px-4 border-b">
                    {(() => {
                      if (!orders.I_Completed_Date) return "";
                      const date = new Date(orders.I_Completed_Date);
                      const day = String(date.getDate()).padStart(2, "0");
                      const month = String(date.getMonth() + 1).padStart(
                        2,
                        "0"
                      );
                      const year = date.getFullYear();
                      return `${day}/${month}/${year}`;
                    })()}
                  </td>
                  <td className="py-2 px-4 border-b">{orders.Customer_Abb}</td>
                  <td className="py-2 px-4 border-b">{orders.NAV_Name}</td>
                  <td className="py-2 px-4 border-b">{orders.NAV_Size}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="7" className="py-2 px-4 text-center">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={goToPrevPage}
          disabled={currentPage === 1}
          className={`p-2 rounded-full ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronLeft size={20} />
        </button>

        <div className="flex items-center gap-4">
          <span>
            Page {currentPage} of {totalPages || 1}
          </span>
          <select
            className="border border-gray-400 rounded px-2 py-1"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10 Rows</option>
            <option value={15}>15 Rows</option>
            <option value={20}>20 Rows</option>
            <option value={25}>25 Rows</option>
          </select>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          className={`p-2 rounded-full ${
            currentPage === totalPages || totalPages === 0
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
