import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

export const toThaiDate = (date) => {
  const nowThai = dayjs().tz("Asia/Bangkok");

  return new Date(
    dayjs(date)
      .hour(nowThai.hour())
      .minute(nowThai.minute())
      .second(nowThai.second())
      .tz("Asia/Bangkok")
      .format("YYYY-MM-DDTHH:mm:ss")
  );
};
