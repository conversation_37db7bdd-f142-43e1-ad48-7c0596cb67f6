import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useOrder } from "../hooks/use-order";
import { usePlan } from "../hooks/use-plan";
import { FaArrowDownLong, FaArrowRightLong } from "react-icons/fa6";
import Swal from "sweetalert2";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { AiTwotoneCalendar } from "react-icons/ai";
import { format } from "date-fns";
import CustomSelect from "./CustomSelect/CustomSelect";

import { toThaiDate } from "../utils/dateUtils";

export default function OrderInfo() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const timeoutIdRef = useRef(null);
  const formatDate = (date) => {
    const d = new Date(date);

    // แปลงวันที่เป็น UTC ก่อนเพื่อไม่ให้มีการปรับเวลา
    const day = String(d.getUTCDate()).padStart(2, "0");
    const month = String(d.getUTCMonth() + 1).padStart(2, "0"); // เดือนเริ่มต้นที่ 0
    const year = d.getUTCFullYear();
    const hours = String(d.getUTCHours()).padStart(2, "0");
    const minutes = String(d.getUTCMinutes()).padStart(2, "0");
    const seconds = String(d.getUTCSeconds()).padStart(2, "0");

    // แสดงผลในรูปแบบ dd/mm/yyyy hh:mm:ss (แบบไม่แปลงเวลา)
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const generateSpaces = (count) => "\u00A0".repeat(count);
  const location = useLocation();
  const navigate = useNavigate();
  const [newWindow, setNewWindow] = useState(null);
  const [isDisabled, setIsDisabled] = useState(true);
  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    newAddButton: true,
    F3: false,
    F4: false,
    F5: false,
    F6: false,
    F7: false,
    F8: true,
    F9: false,
    F10: false,
    F11: false,
    F12: true,
  });
  const [filteredWorkgData, setFilteredWorkgData] = useState([]);
  const [selectedWorkGName, setSelectedWorkGName] = useState("");
  const { searchOrderNo: initialSearchOrderNo = "" } = location.state || {};
  const [searchOrderNo, setSearchOrderNo] = useState(initialSearchOrderNo);
  const [remainningQuantity, setRemainningQuantity] = useState("");
  const [selectedSalesGrpAbb, setSelectedSalesGrpAbb] = useState("");
  const [selectedSalesPersonAbb, setSelectedSalesPerson] = useState("");
  const [selectedCustomerAbb, setSelectedCustomerAbb] = useState("");
  const [selectedCustomerName, setSelectedCustomerName] = useState("");
  const [request1Name, setRequest1Name] = useState("");
  const [request2Name, setRequest2Name] = useState("");
  const [request3Name, setRequest3Name] = useState("");
  const [quoteName, setQuoteName] = useState("");
  const [unitName, setUnitName] = useState("");
  const [itemName, setItemName] = useState("");
  const [supplyName, setSupplyName] = useState("");
  const [coatingName, setCoatingName] = useState("");
  const [targetName, setTargetName] = useState("");
  const [personName, setPersonName] = useState("");
  const [PriceName, setPriceName] = useState("");
  const [regPersonName, setregPersonName] = useState("");
  const [updPersonName, setupdPersonName] = useState("");
  const [destinationName, setDestinationName] = useState("");
  const [OrderNo, setOrderNo] = useState("");
  const [autoYearChange, setAutoYearChange] = useState(true);
  const [customerDraw, setCustomerDraw] = useState("");
  const [companyDraw, setCompanyDraw] = useState("");
  const [DocuName, setDocuName] = useState("");
  const [SpecificName, setSpecificName] = useState("");
  const [OdProgressName, setOdProgressName] = useState("");
  const [DeliveryName, setDeliveryName] = useState("");
  const [Schedule_Name, setSchedule_Name] = useState("");
  const orderNoRef = useRef(null);
  const SearchorderNoRef = useRef(null);
  const [isSearchCompleted, setIsSearchCompleted] = useState(true);
  const [hasUserEditedOrderNo, setHasUserEditedOrderNo] = useState(false);

  const handleAutoYearChange = (event) => {
    setAutoYearChange(event.target.checked);
  };
  const { ScheduleData, StatusData } = usePlan();
  const {
    CustomerData,
    WorkerData,
    WorkergData,
    orderData,
    searchOrderData,
    editOrders,
    deleteOrder,
    setOrderData,
    createOrder,
    setWorkergData,
    setWorkerData,
    Request1Data,
    setRequest1Data,
    Request2Data,
    setRequest2Data,
    Request3Data,
    setRequest3Data,
    CoatingData,
    setCoatingData,
    TargetData,
    setTargetData,
    Item1Data,
    setItem1Data,
    SupplyData,
    setSupplyData,
    UnitData,
    setUnitData,
    WorkgData,
    setWorkgData,
    setCustomerData,
    QuoteData,
    setQuoteData,
    PriceData,
    setPriceData,
    ContractDocuData,
    setContractDocu,
    SpecificData,
    setSpecificData,
    OdProgressData,
    setOdProgressData,
    DeliveryData,
    setDeliveryData,
    CheckOrderData,
    ProcessSheetData,
    fetchProcessSheet24,
    resetOrderData,
  } = useOrder();
  const [dates, setDates] = useState({
    Confirm_Delivery: null,
    NAV_Delivery: null,
    Request_Delivery: null,
    Product_Delivery: null,

    Pd_Received_Date: null,
    Pd_Complete_Date: null,
    I_Completed_Date: null,
    Shipment_Date: null,
    Pd_Calc_Date: null,
    Calc_Process_Date: null,
    Od_Upd_Date: null,
  });

  const dateRefs = useRef({
    Confirm_Delivery: null,
    NAV_Delivery: null,
    Request_Delivery: null,
    Product_Delivery: null,
    Pd_Received_Date: null,
    Pd_Complete_Date: null,
    I_Completed_Date: null,
    Shipment_Date: null,
    Pd_Calc_Date: null,
    Calc_Process_Date: null,
    Od_Upd_Date: null,
  });
  const [showConfirm, setShowConfirm] = useState(false);

  const confirmWhenSaveNull = (fieldName, defaultValue) => {
    const value = document.getElementById(fieldName).value;
    return value !== defaultValue && value.trim() !== "";
  };
  const searchPermission = (status) => {
    document.getElementById("Search_Order_No").disabled = !status;
  };

  const editPermission = (status) => {
    const datePickerFields = Object.keys(dateRefs.current); // รายชื่อ field ที่ใช้ DatePicker
    const otherFields = [
      "Order_No",
      "Product_Grp_CD",
      "Od_Pending",
      "Temp_Shipment",
      "Unreceived",
      "Od_CAT1",
      "Od_CAT2",
      "Od_CAT3",
      "NAV_Name",
      "Product_Name",
      "NAV_Size",
      "Product_Size",
      "Customer_Draw",
      "Company_Draw",
      "Product_Draw",
      "Quantity",
      "Unit_CD",
      "Sl_Instructions",
      "Pd_Instructions",
      "Pd_Remark",
      "I_Remark",
      "Sales_Grp_CD",
      "Sales_Person_CD",
      "Request1_CD",
      "Request2_CD",
      "Request3_CD",
      "Material1",
      "H_Treatment1",
      "Material2",
      "H_Treatment2",
      "Material3",
      "H_Treatment3",
      "Material4",
      "H_Treatment4",
      "Material5",
      "H_Treatment5",
      "Tolerance",
      "Coating_CD",
      "Coating",
      "Quote_No",
      "Quote_CD",
      "Item1_CD",
      "Custom_Material",
      "Od_No_of_Custom",
      "Customer_CD",
      "Supply_CD",
      "Destination_CD",
      "Contract_Docu_CD",
      "Price_CD",
      "Od_No_of_Pd_Split",
      "Od_Ctl_Person_CD",
      "Od_Reg_Person_CD",
      "Od_Upd_Person_CD",
      "Specific_CD",
      "Od_Progress_CD",
      "Delivery_CD",
      "Schedule_CD",
      "Target_CD",
      "Pd_Target_Qty",
      "Pd_Complete_Qty",
      "I_Complete_Qty",
      "Shipment_Qty",
      "Pd_Split_Qty",
      "Pd_Calc_Qty",
      "NG_Qty",
    ];

    // ปรับค่าผ่าน useRef สำหรับ DatePicker
    datePickerFields.forEach((id) => {
      if (dateRefs.current[id]) {
        dateRefs.current[id].disabled = !status;
      }
    });

    // ใช้ document.getElementById สำหรับ input ปกติ
    otherFields.forEach((id) => {
      const element = document.getElementById(id);
      if (element) element.disabled = !status;
    });
  };

  const handleOrderNoCheck = async (value) => {
    if (!value) return;

    if (!orderData?.Order_No || searchOrderNo) return;

    const result = await CheckOrderData(value);

    if (result) {
      Swal.fire({
        title: "Confirm",
        html: `${orderData.Order_No} is already registered!<br>${orderData.Order_No} ได้ถูกป้อนข้อมูลการลงทะเบียนแล้ว !<br>${orderData.Order_No} はすでに登録されています!`,
        icon: "error",
        confirmButtonText: "Ok",
      }).then(() => {
        editPermission(false);
        setIsDisabled(true);
        document.getElementById("Order_No").disabled = false;
      });
    } else {
      editPermission(true);
      setIsDisabled(false);
      document.getElementById("Order_No").disabled = true;
    }
  };

  const handleF1Click = () => {
    if (searchOrderNo) {
      // ตรวจสอบว่ามีการกรอกหมายเลขคำสั่งซื้อ
      searchOrderData(searchOrderNo); // เรียกฟังก์ชันค้นหาข้อมูล
    } else {
      // ใช้ SweetAlert 2 แทน alert
      Swal.fire({
        title: "The information is incorrect.",
        text: "Please enter your order number first.",
        icon: "warning",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF2Click = () => {
    try {
      searchPermission(false);
      editPermission(true);

      const orderNoInput = document.getElementById("Order_No");
      orderNoInput.disabled = false;

      setIsDisabled(false);

      setButtonState((prevState) => ({
        ...prevState,
        F2: true,
        F3: false,
        newAddButton: false,
        F4: false,
        F5: false,
        F6: false,
        F9: true,
        F10: false,
        F11: true,
        F12: false,
      }));
      setIsSearchCompleted(false);
    } catch (error) {
      // จัดการข้อผิดพลาด
      alert("Error occurs when F2_Click\nPlease contact system administrator.");
    }
  };

  const handleF3Click = () => {
    try {
      // ปิดสิทธิ์การแก้ไข
      searchPermission(false);
      editPermission(false);

      // ตรวจสอบและเปิดการแก้ไข Order_No
      const orderNoElement = document.getElementById("Order_No");
      if (orderNoElement) {
        orderNoElement.disabled = false;
      } else {
        console.warn('Element with ID "Order_No" not found.');
      }

      // รีเซ็ตค่าต่าง ๆ
      setSearchOrderNo("");
      setOrderData("");
      setRemainningQuantity("");
      setSelectedSalesGrpAbb("");
      setSelectedSalesPerson("");
      setSelectedCustomerAbb("");
      setSelectedCustomerName("");
      setRequest1Name("");
      setRequest2Name("");
      setRequest3Name("");
      setQuoteName("");
      setUnitName("");
      setItemName("");
      setSupplyName("");
      setCoatingName("");
      setTargetName("");
      setPersonName("");
      setPriceName("");
      setregPersonName("");
      setupdPersonName("");
      setDestinationName("");
      setOrderNo("");
      setCustomerDraw("");
      setCompanyDraw("");
      setDocuName("");
      setSpecificName("");
      setOdProgressName("");
      setDeliveryName("");
      setSchedule_Name("");

      // ตรวจสอบ ref และโฟกัส input
      if (orderNoRef.current) {
        orderNoRef.current.focus();
      } else {
        console.warn("orderNoRef is not assigned to an input.");
      }

      // อัปเดตสถานะปุ่ม
      setButtonState((prevState) => ({
        F3: false,
        F8: true,
        F9: true,
        F11: true,
        F12: false,
      }));
      setIsSearchCompleted(false);
    } catch (error) {
      // จัดการข้อผิดพลาด
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
      console.error("Error in F3_Click:", error);
    }
  };
  const handleF4Click = async () => {
    try {
      const orderExists = await searchOrderData(searchOrderNo);
      if (orderExists) {
        navigate("/purchase-info", { state: { searchOrderNo } });
      } else {
        await Swal.fire({
          title: "The information is incorrect.",
          text: "No number found order",
          icon: "warning",
          confirmButtonText: "OK",
        });
      }
    } catch (error) {
      alert("Error occurs when F4_Click\nPlease contact system administrator.");
    }
  };
  const handleF5Click = () => {
    try {
      // ส่งค่า Search_Order_No ไปที่หน้า /plan-info
      navigate("/plan-info", { state: { OrderNo: orderData.Order_No } });
    } catch (error) {
      // จัดการข้อผิดพลาด
      alert("Error occurs when F5_Click\nPlease contact system administrator.");
    }
  };

  window.addEventListener("beforeunload", function () {
    localStorage.removeItem("OdinfoisF6Clicked");
  });

  const handleF6Click = async () => {
    try {
      if (!orderData.Order_No) {
        Swal.fire({
          icon: "warning",
          title: "Order_No is required",
        });
        return;
      }

      const success = await fetchProcessSheet24(orderData.Order_No);
      if (!success) {
        Swal.fire({
          icon: "error",
          title: "No Plan Data!",
        });
        return;
      }

      let reportUrl = `${apiUrl_5173}/reports/RD_Process_Sheet/${orderData.Order_No}`;

      const loadingPopup = Swal.fire({
        title: "Loading...",
        text: "Please wait while the report is being loaded.",
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const iframe = document.createElement("iframe");
      iframe.src = reportUrl;
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      iframe.style.position = "absolute";
      iframe.style.visibility = "hidden";
      document.body.appendChild(iframe);

      iframe.onload = () => {
        iframe.contentWindow.postMessage(
          {
            OrderNo: orderData.Order_No,
            OrderData: orderData,
            CustomerData,
            ProcessSheetData,
          },
          `${apiUrl_5173}`
        );

        setTimeout(() => {
          loadingPopup.close();
        }, 2000);
      };
    } catch (error) {
      console.error("Error in handleF6Click:", error);
      Swal.fire({
        icon: "error",
        title: "No Plan Data!",
      });
    }
  };

  const handleF8Click = async () => {
    try {
      Swal.fire({
        title: "Limit",
        html: "This feature is currently unavialable!<br>ปัจจุบันไม่สามารถใข้งานฟังก์ชั่นนี้ได้ !<br>現在この機能は使用出来7",
        icon: "error",
        confirmButtonText: "Ok",
      });
    } catch (error) {
      console.error("Error in handleF8Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please contact the administrator.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const validateFormData = () => {
    const requiredFields = [
      { id: "Order_No", name: "(Order_No)" },
      { id: "Product_Grp_CD", name: "(Product Group)" },
      { id: "Sales_Grp_CD", name: "(Sales Group)" },
      { id: "Sales_Person_CD", name: "(Sales Person)" },
      { id: "Sales_Person_CD", name: "(Sales Person)" },

      { id: "Item1_CD", name: "(Item)" },

      { id: "Od_Progress_CD", name: "(Order Progress CAT)" },
      { id: "Quantity", name: "(Quantity)" },
    ];

    const missingFields = requiredFields.filter((field) => {
      const value = document.getElementById(field.id)?.value?.trim();
      return !value;
    });

    return missingFields;
  };

  const handleF9Click = async () => {
    try {
      // 1) ตรวจฟอร์ม
      const missingFields = validateFormData();
      if (missingFields.length > 0) {
        const names = missingFields.map((f) => f.name).join(", ");
        await Swal.fire({
          title: "Incomplete information",
          text: `Please fill in the following information completely: ${names}`,
          icon: "warning",
        });
        return;
      }

      // 2) เตรียมข้อมูล
      const now = new Date().toISOString();
      const updatedDates = Object.fromEntries(
        Object.entries(dates).map(([k, v]) => [k, v ? v.toISOString() : null])
      );
      const newData = { ...orderData, ...updatedDates, Od_Upd_Date: now };

      setOrderData(newData);

      // 3) ถามยืนยันเลย
      const { isConfirmed } = await Swal.fire({
        title: "Do you want to Save the record?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
      });
      if (!isConfirmed) return;

      // 4) เช็คมีหรือยัง แล้วแก้หรือสร้าง
      const orderNo =
        newData.Order_No || document.getElementById("Order_No")?.value;
      const exists = await searchOrderData(orderNo);
      if (exists) {
        await editOrders(orderNo, newData);
      } else {
        await createOrder(newData);
      }
      // 5) แสดงสำเร็จ
      await Swal.fire({
        title: "Success",
        html: `
          ${orderNo} ดำเนินการสำเร็จ !<br>
          Operation completed successfully !<br>
          処理が正常に完了しました !
        `,
        icon: "success",
        confirmButtonText: "ตกลง",
      });
      await searchOrderData(orderNo);
    } catch (err) {
      console.error("Error in handleF9Click:", err);
      await Swal.fire("Error", "มีข้อผิดพลาดเกิดขึ้น", "error");
    }
  };

  const handleF10Click = async () => {
    try {
      // แสดงกล่องยืนยันการลบข้อมูล
      const result = await Swal.fire({
        title: "Confirm deletion?",
        text: "Do you want to delete this information?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes!",
        cancelButtonText: "Cancle",
      });

      // ตรวจสอบว่าผู้ใช้เลือก "ยืนยัน" หรือไม่
      if (result.isConfirmed) {
        const response = await deleteOrder(searchOrderNo); // เรียกใช้ฟังก์ชันลบคำสั่ง
        console.log("Delete result:", response);

        // แสดงข้อความว่าลบเรียบร้อยแล้ว
        Swal.fire(
          "Deleted successfully!",
          "Your information has been successfully deleted.",
          "success"
        );
      }
    } catch (error) {
      // จัดการข้อผิดพลาด
      alert(
        "Error occurs when F10_Click\nPlease contact system administrator."
      );
    }
  };

  const handleF11Click = async () => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        html: "Would you like to make the next input?<br>ป้อนข้อมูลต่อไปหรือไม่ ?<br>次入力しますか?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        if (searchOrderNo) {
          setSearchOrderNo("");
          setRemainningQuantity("");
          setSelectedSalesGrpAbb("");
          setSelectedSalesPerson("");
          setSelectedCustomerAbb("");
          setSelectedCustomerName("");
          setRequest1Name("");
          setRequest2Name("");
          setRequest3Name("");
          setQuoteName("");
          setUnitName("");
          setItemName("");
          setSupplyName("");
          setCoatingName("");
          setTargetName("");
          setPersonName("");
          setPriceName("");
          setregPersonName("");
          setupdPersonName("");
          setDestinationName("");
          setOrderNo("");
          setCustomerDraw("");
          setCompanyDraw("");
          setDocuName("");
          setSpecificName("");
          setOdProgressName("");
          setDeliveryName("");
          setSchedule_Name("");
          setIsSearchCompleted(true);
          setSelectedWorkGName("");
          setOrderData("");
          setDates((prevDates) => {
            const resetDates = {};
            Object.keys(prevDates).forEach((key) => {
              resetDates[key] = null; // หรือ "" ถ้าต้องการให้เป็นสตริงว่าง
            });
            return resetDates;
          });
          // const response = await fetchOrders();
          resetOrderData();
          setButtonState((prevState) => ({
            ...prevState,
            F2: false,
            F3: false,
            F4: false,
            F5: false,
            F9: false,
            F10: false,
            F11: false,
          }));
          editPermission(false);

          // if (!response || !response.data || response.data.length === 0) {
          //   Swal.fire({
          //     title: "No order information",
          //     icon: "warning",
          //     confirmButtonText: "OK",
          //   });
          // } else {
          //   searchPermission(true);
          //   if (SearchorderNoRef.current) {
          //     SearchorderNoRef.current.focus();
          //   }
          // }
          searchPermission(true);
        } else {
          const confirmResult = await Swal.fire({
            title: "Reconfirm",
            html: "Editing contents will be cancelled!<br>Really, are you sure?<br>เนื้อหาที่ทําการแก้ไขจะถูกยกเลิก! แน่ใจจริงๆแล้ว หรือไม่?<br>編集中の内容が取消されます!<br>本当に宜しいで",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes",
            cancelButtonText: "No",
          });
          if (confirmResult.isConfirmed) {
            setSearchOrderNo("");
            setRemainningQuantity("");
            setSelectedSalesGrpAbb("");
            setSelectedSalesPerson("");
            setSelectedCustomerAbb("");
            setSelectedCustomerName("");
            setRequest1Name("");
            setRequest2Name("");
            setRequest3Name("");
            setQuoteName("");
            setUnitName("");
            setItemName("");
            setSupplyName("");
            setCoatingName("");
            setTargetName("");
            setPersonName("");
            setPriceName("");
            setregPersonName("");
            setupdPersonName("");
            setDestinationName("");
            setOrderNo("");
            setCustomerDraw("");
            setCompanyDraw("");
            setDocuName("");
            setSpecificName("");
            setOdProgressName("");
            setDeliveryName("");
            setSchedule_Name("");
            setSelectedWorkGName("");
            setOrderData("");
            setDates((prevDates) => {
              const resetDates = {};
              Object.keys(prevDates).forEach((key) => {
                resetDates[key] = null; // หรือ "" ถ้าต้องการให้เป็นสตริงว่าง
              });
              return resetDates;
            });
            setIsSearchCompleted(true);
            // const response = await fetchOrders();
            setButtonState((prevState) => ({
              ...prevState,
              F2: false,
              newAddButton: true,
              F3: false,
              F4: false,
              F5: false,
              F9: false,
              F10: false,
              F11: false,
              F12: true,
            }));
            editPermission(false);

            // if (!response || !response.data || response.data.length === 0) {
            //   Swal.fire({
            //     title: "No order information",
            //     icon: "warning",
            //     confirmButtonText: "OK",
            //   });
            // } else {
            //   searchPermission(true);
            //   if (SearchorderNoRef.current) {
            //     SearchorderNoRef.current.focus();
            //   }
            // }
            searchPermission(true);
          }
        }
        localStorage.removeItem("OdinfoisF6Clicked");
      }
    } catch (error) {
      console.error("Error in handleF11Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      }); // แจ้งเตือนผู้ใช้เกี่ยวกับข้อผิดพลาด
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: "Do you want to close this window?<br>คุณต้องการปิดหน้าต่างนี้หรือไม่?<br>このウィンドウを閉じますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });
      if (confirmResult.isConfirmed) {
        setSearchOrderNo("");
        setOrderData({});
        navigate("/dashboard");

        localStorage.removeItem("OdinfoisF6Clicked");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      }); // แจ้งเตือนผู้ใช้เกี่ยวกับข้อผิดพลาด
    }
  };

  useEffect(() => {
    console.log("reset data");

    setSearchOrderNo(""); // รีเซ็ตค่า
    setOrderData({});
  }, []);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     if (searchOrderNo) {
  //       const result = await searchOrderData(searchOrderNo);
  //       if (result) {
  //         // อัปเดตสถานะปุ่ม
  //         setButtonState((prevState) => ({
  //           ...prevState,
  //           F2: true,
  //           F3: true,
  //           F4: true,
  //           F5: true,
  //           F10: true,
  //           F11: true,
  //         }));
  //       } else {
  //         // หากไม่มีผลลัพธ์ ปิดสถานะปุ่ม
  //         setButtonState((prevState) => ({
  //           ...prevState,
  //           F2: false,
  //           F3: false,
  //           F4: false,
  //           F5: false,
  //           F10: false,
  //           F11: false,
  //         }));
  //       }
  //     }
  //   };

  //   fetchData();

  //   // รีเซ็ตวันที่เมื่อ searchOrderNo เปลี่ยนแปลง
  //   setDates((prev) =>
  //     Object.keys(prev).reduce((acc, key) => {
  //       if (key !== "QC_Completed") {
  //         acc[key] = null;
  //       }
  //       return acc;
  //     }, {})
  //   );
  // }, [searchOrderNo]);

  // Custom onChange handler for CustomSelect
  const handleCustomSelectChange = ({ id, value }) => {
    const event = {
      target: {
        id,
        value,
        type: "select-one",
      },
    };
    handleInputChange(event);
  };

  const handleInputChange = async (event, isDebouce = false) => {
    const { id, value, type, checked } = event.target;
    const supplyCD = orderData?.Supply_CD;
    let formattedValue = value;

    if (id === "Search_Order_No") {
      setSearchOrderNo(value);
    }

    // ตรวจสอบว่าเป็น datetime-local และฟอร์แมตค่า
    if (type === "datetime-local" && value) {
      const dateWithCurrentTime = new Date(value);
      const year = dateWithCurrentTime.getFullYear();
      const month = String(dateWithCurrentTime.getMonth() + 1).padStart(2, "0");
      const day = String(dateWithCurrentTime.getDate()).padStart(2, "0");
      const hours = String(dateWithCurrentTime.getHours()).padStart(2, "0");
      const minutes = String(dateWithCurrentTime.getMinutes()).padStart(2, "0");
      const seconds = String(dateWithCurrentTime.getSeconds()).padStart(2, "0");

      formattedValue = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    // อัปเดตค่าของ orderData
    setOrderData((prevOrderData) => ({
      ...prevOrderData,
      [id]:
        type === "checkbox"
          ? checked
          : type === "datetime-local" && value
          ? formattedValue // ใช้ formattedValue
          : type === "date" && value !== ""
          ? new Date(`${value}T00:00:00.000Z`).toISOString()
          : value === ""
          ? null
          : value,
    }));

    switch (id) {
      case "Product_Grp_CD":
        setOrderData((prevOrderData) => ({
          ...prevOrderData,
          Product_Grp_CD: value,
        }));
        break;
      default:
        break;
    }

    // กรองข้อมูล WorkgData ตาม Supply_CD
    if (supplyCD === "0") {
      if (Array.isArray(WorkgData) && WorkgData.length > 0) {
        setFilteredWorkgData(WorkgData);
      }
    } else if (supplyCD === "1") {
      if (Array.isArray(CustomerData) && CustomerData.length > 0) {
        setFilteredWorkgData(CustomerData);
      }
    } else {
      setFilteredWorkgData(WorkgData);
    }
  };

  const handleSearchEnter = async (event) => {
    if (event.key === "Enter") {
      const value = event.target.value;
      setSearchOrderNo(value);
      setHasUserEditedOrderNo(true);

      if (value) {
        try {
          const result = await searchOrderData(value);

          setButtonState((prevState) => ({
            ...prevState,
            F2: !!result,
            F3: !!result,
            F4: !!result,
            F5: !!result,
            F10: !!result,
            F11: !!result,
          }));
        } catch (error) {
          console.error("Error fetching order data:", error);
          setButtonState((prevState) => ({
            ...prevState,
            F2: false,
            F3: false,
            F4: false,
            F5: false,
            F10: false,
            F11: false,
          }));
        }
      } else {
        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
          F3: false,
          F4: false,
          F5: false,
          F10: false,
          F11: false,
        }));
      }
    }
  };

  const handleRequestDeliveryChange = (newDeliveryDate) => {
    handleInputChange({
      target: { id: "Request_Delivery", value: newDeliveryDate },
    });
  };

  const handleProductDeliveryChange = (newProductDeliveryDate) => {
    handleInputChange({
      target: { id: "Product_Delivery", value: newProductDeliveryDate },
    });
  };

  const handleConfirmDeliveryChange = (newConfirmDeliveryDate) => {
    handleInputChange({
      target: { id: "Confirm_Delivery", value: newConfirmDeliveryDate },
    });
  };

  const handleNAVDeliveryChange = (newNAVDeliveryDate) => {
    handleInputChange({
      target: { id: "NAV_Delivery", value: newNAVDeliveryDate },
    });
  };

  const handleProduct_Grp_NameChange = (Product_Grp_Name) => {
    handleInputChange({
      target: { id: "Product_Grp_Name", value: Product_Grp_Name },
    });
  };

  const handleDeliveryDateUpdate = (deliveryDateStr, handleChange) => {
    // แปลงวันที่จากรูปแบบ DD/MM/YYYY เป็น Date object
    const parts = deliveryDateStr.split("/"); // แบ่งวันที่ออกเป็นส่วน ๆ
    const deliveryDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`); // สร้าง Date object โดยใช้รูปแบบ YYYY-MM-DD

    const now = new Date(); // วันที่ปัจจุบัน
    const differenceInDays = Math.floor(
      (now - deliveryDate) / (1000 * 60 * 60 * 24)
    ); // คำนวณความแตกต่างในวัน

    // ตรวจสอบว่าความแตกต่างมากกว่า 183 วันหรือไม่
    if (differenceInDays > 183) {
      const newDeliveryDate = new Date(
        deliveryDate.setFullYear(deliveryDate.getFullYear() + 1)
      ); // เพิ่มปี

      // แปลงวันที่ใหม่กลับไปเป็น DD/MM/YYYY
      const formattedNewDeliveryDate = `${newDeliveryDate.getFullYear()}-${String(
        newDeliveryDate.getMonth() + 1
      ).padStart(2, "0")}-${String(newDeliveryDate.getDate()).padStart(
        2,
        "0"
      )}`;
      // เรียกใช้ฟังก์ชันสำหรับการเปลี่ยนแปลงวันที่
      handleChange(formattedNewDeliveryDate);
    }
  };

  const handleRequestDeliveryAfterUpdate = () => {
    if (autoYearChange) {
      handleDeliveryDateUpdate(
        orderData.Request_Delivery,
        handleRequestDeliveryChange
      );
      handleDeliveryDateUpdate(
        orderData.Product_Delivery,
        handleProductDeliveryChange
      );
      handleDeliveryDateUpdate(
        orderData.Confirm_Delivery,
        handleConfirmDeliveryChange
      );
      handleDeliveryDateUpdate(orderData.NAV_Delivery, handleNAVDeliveryChange);
    }
  };

  useEffect(() => {
    if (
      autoYearChange &&
      orderData?.Request_Delivery &&
      orderData?.Product_Delivery &&
      orderData?.Confirm_Delivery &&
      orderData?.NAV_Delivery
    ) {
      handleRequestDeliveryAfterUpdate();
    }
  }, [
    autoYearChange,
    orderData?.Request_Delivery,
    orderData?.Product_Delivery,
    orderData?.Confirm_Delivery,
    orderData?.NAV_Delivery,
  ]);

  const handleProductName = (newProductName) => {
    handleInputChange({
      target: { id: "Product_Name", value: newProductName },
    });
  };

  const handleGoods_Name_Reflect = () => {
    handleProductName(orderData.NAV_Name); // เรียกใช้ฟังก์ชันเพื่ออัปเดต Product_Name ด้วยค่า navName
  };

  const handlenavSizeName = (newnavSizeName) => {
    handleInputChange({
      target: { id: "Product_Size", value: newnavSizeName },
    });
  };

  const handleGoods_Size_Reflect = () => {
    handlenavSizeName(orderData.NAV_Size);
  };

  const handProductDraw = (newProductDraw) => {
    handleInputChange({
      target: { id: "Product_Draw", value: newProductDraw },
    });
  };

  const handleConfirm = () => {
    const customer = orderData.Customer_Draw || customerDraw;
    const company = orderData.Company_Draw || companyDraw;

    if (customer) {
      if (company) {
        handProductDraw(`Com:${company}/Cus:${customer}`);
      } else {
        handProductDraw(`Cus:${customer}`);
      }
    } else {
      if (company) {
        handProductDraw(`Com:${company}`);
      } else {
        handProductDraw(null);
      }
    }
  };

  const handleDrawNoReflectClick = () => {
    const message = `
      Company Draw: ${orderData.Customer_Draw}
      Customer Draw: ${orderData.Company_Draw}
      Are you sure you want to proceed?
    `;

    Swal.fire({
      title: "Confirm Action",
      text: message,
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then((result) => {
      if (result.isConfirmed) {
        handleConfirm(); // เรียกใช้ฟังก์ชันยืนยันถ้าผู้ใช้กด Yes
      }
    });
  };

  const handPdTargetQty = (newPdTargetQty) => {
    handleInputChange({
      target: { id: "Pd_Target_Qty", value: newPdTargetQty },
    });
  };

  const confirmProductionTargetChange = async (value) => {
    try {
      const result = await Swal.fire({
        title: "Confirm",
        html: "Would you like to also change [Production_Target_Qty]?<br>ต้องการเปลี่ยนแปลง [Production_Target_Qty] ด้วยหรือไม่ ?<br>「Production_Target_Qty」 も変更しますか?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (result.isConfirmed) {
        // ถ้าผู้ใช้กดยืนยัน จะเปลี่ยนค่าใหม่
        handPdTargetQty(orderData.Quantity);
      } else {
        // ถ้าผู้ใช้กดไม่ คืนค่าเดิม
        handPdTargetQty(orderData.Pd_Target_Qty);
      }
    } catch (error) {
      console.error("Error during confirmation:", error);
      Swal.fire({
        title: "เกิดข้อผิดพลาด",
        text: "ไม่สามารถเปลี่ยนค่า Production_Target_Qty ได้",
        icon: "error",
        confirmButtonText: "ตกลง",
      });
    }
  };

  // Function for button setting in Production Docu
  const handleSettingClick = async () => {
    // ตรวจสอบว่า Order_No มีค่าหรือไม่
    if (!orderData?.Order_No) return;

    const payload = {
      orderNo: orderData.Order_No,
    };

    try {
      // ส่งคำขอไปยัง API เพื่อขอข้อมูล
      const response = await fetch(`${apiUrl_4000}/order/product_docu_set`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // อัปเดตค่า Product_Docu ของ orderData ด้วยค่า documentPath ที่ได้จาก API
          setOrderData((prevOrderData) => ({
            ...prevOrderData,
            Product_Docu: data.documentPath || "", // ค่าใหม่ที่ได้จาก API
          }));
        } else {
          console.error("Error:", data.message);
        }
      } else {
        console.error("Failed to fetch the data");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const handlePathClick = (path) => {
    try {
      // 1. Replace all backslashes with forward slashes
      let correctedPath = path.replace(/\\/g, "/");

      // 2. Ensure the protocol has a double slash after the colon
      correctedPath = correctedPath.replace("http:/", "http://");

      // 3. Check if the file exists before opening it
      fetch(correctedPath, { method: "HEAD" })
        .then((response) => {
          if (response.ok) {
            // File exists, open it in a new window
            console.log("Opening URL:", correctedPath);

            const windowWidth = 1024;
            const windowHeight = 768;
            const left = (screen.width - windowWidth) / 2;
            const top = (screen.height - windowHeight) / 2;

            window.open(
              correctedPath,
              "_blank",
              `width=${windowWidth},height=${windowHeight},top=${top},left=${left},status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes,scrollbars=yes`
            );
          } else {
            // File doesn't exist, show error popup
            console.error("File not found:", correctedPath);
            Swal.fire({
              icon: "error",
              title: "File Not Found",
              text: "The requested PDF file was not found or is inaccessible.",
            });
          }
        })
        .catch((error) => {
          console.error("Error checking file:", error);
          Swal.fire({
            icon: "error",
            title: "Error Occurred",
            text: "An error occurred while verifying the file.",
          });
        });
    } catch (error) {
      console.error("Error processing path:", error);
      Swal.fire({
        icon: "error",
        title: "Error Occurred",
        text: "An error occurred while processing the file path.",
      });
    }
  };

  useEffect(() => {
    if (orderData?.Product_Grp_CD && WorkergData.length > 0) {
      const selectedGroup = WorkergData.find(
        (item) => item.WorkG_CD === orderData.Product_Grp_CD
      );
      setSelectedWorkGName(selectedGroup ? selectedGroup.WorkG_Name : "");
    }

    if (orderData?.Sales_Grp_CD && WorkergData.length > 0) {
      const selectedGroup = WorkergData.find(
        (item) => item.WorkG_CD === orderData.Sales_Grp_CD
      );
      setSelectedSalesGrpAbb(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }

    if (orderData?.Destination_CD && WorkergData.length > 0) {
      const selectedGroup = WorkergData.find(
        (item) => item.WorkG_CD === orderData.Destination_CD
      );
      setDestinationName(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }
  }, [
    orderData?.Product_Grp_CD,
    orderData?.Sales_Grp_CD,
    orderData?.Destination_CD,
    WorkergData,
  ]);

  useEffect(() => {
    if (orderData?.Schedule_CD && ScheduleData.length > 0) {
      const selectedGroup = ScheduleData.find(
        (item) => item.Schedule_CD === orderData.Schedule_CD
      );

      setSchedule_Name(selectedGroup ? selectedGroup.Schedule_Symbol : "");
    }
  }, [orderData?.Schedule_CD, ScheduleData]);

  useEffect(() => {
    if (orderData?.Sales_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Sales_Person_CD
      );
      setSelectedSalesPerson(selectedGroup ? selectedGroup.Worker_Abb : "");
    }
  }, [orderData?.Sales_Person_CD, WorkerData]);

  useEffect(() => {
    if (orderData?.Od_Ctl_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Od_Ctl_Person_CD
      );
      setPersonName(selectedGroup ? selectedGroup.Worker_Name : "");
    }
  }, [orderData?.Od_Ctl_Person_CD, WorkerData]);

  useEffect(() => {
    if (orderData?.Od_Upd_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Od_Upd_Person_CD
      );
      setupdPersonName(selectedGroup ? selectedGroup.Worker_Name : "");
    }
  }, [orderData?.Od_Upd_Person_CD, WorkerData]);

  useEffect(() => {
    if (orderData?.Od_Reg_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === orderData.Od_Reg_Person_CD
      );
      setregPersonName(selectedGroup ? selectedGroup.Worker_Name : "");
    }
  }, [orderData?.Od_Reg_Person_CD, WorkerData]);

  useEffect(() => {
    if (orderData?.Customer_CD && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === orderData.Customer_CD
      );
      setSelectedCustomerName(selectedGroup ? selectedGroup.Customer_Name : "");
      setSelectedCustomerAbb(selectedGroup ? selectedGroup.Customer_Abb : "");
    }

    if (orderData?.Destination_CD && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === orderData.Destination_CD
      );
      setDestinationName(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
  }, [orderData?.Customer_CD, orderData?.Destination_CD, CustomerData]);

  useEffect(() => {
    if (orderData?.Request1_CD && Request1Data.length > 0) {
      const selectedGroup = Request1Data.find(
        (item) => item.Request1_CD === orderData.Request1_CD
      );

      setRequest1Name(selectedGroup ? selectedGroup.Request1_Name : "");
    }
  }, [orderData?.Request1_CD, Request1Data]);

  useEffect(() => {
    if (orderData?.Request2_CD && Request2Data.length > 0) {
      const selectedGroup = Request2Data.find(
        (item) => item.Request2_CD === orderData.Request2_CD
      );

      setRequest2Name(selectedGroup ? selectedGroup.Request2_Name : "");
    }
  }, [orderData?.Request2_CD, Request2Data]);

  useEffect(() => {
    if (orderData?.Request3_CD && Request3Data.length > 0) {
      const selectedGroup = Request3Data.find(
        (item) => item.Request3_CD === orderData.Request3_CD
      );

      setRequest3Name(selectedGroup ? selectedGroup.Request3_Name : "");
    }
  }, [orderData?.Request3_CD, Request3Data]);

  useEffect(() => {
    if (orderData?.Unit_CD && UnitData.length > 0) {
      const selectedGroup = UnitData.find(
        (item) => item.Unit_CD === orderData.Unit_CD
      );

      setUnitName(selectedGroup ? selectedGroup.Unit_Name : "");
    }
  }, [orderData?.Unit_CD, UnitData]);

  useEffect(() => {
    if (orderData?.Item1_CD && Item1Data.length > 0) {
      const selectedGroup = Item1Data.find(
        (item) => item.Item1_CD === orderData.Item1_CD
      );

      setItemName(selectedGroup ? selectedGroup.Item1_Name : "");
    }
  }, [orderData?.Item1_CD, Item1Data]);

  useEffect(() => {
    if (orderData?.Coating_CD && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === orderData.Coating_CD
      );

      setCoatingName(selectedGroup ? selectedGroup.Coating_Name : "");
    }
  }, [orderData?.Coating_CD, CoatingData]);

  useEffect(() => {
    if (orderData?.Supply_CD && SupplyData.length > 0) {
      const selectedGroup = SupplyData.find(
        (item) => item.Supply_CD === orderData.Supply_CD
      );

      setSupplyName(selectedGroup ? selectedGroup.Supply_Name : "");
    }
  }, [orderData?.Supply_CD, SupplyData]);

  useEffect(() => {
    if (orderData?.Target_CD && TargetData.length > 0) {
      const selectedGroup = TargetData.find(
        (item) => item.Target_CD === orderData.Target_CD
      );

      setTargetName(selectedGroup ? selectedGroup.Target_Name : "");
    }
  }, [orderData?.Target_CD, TargetData]);

  useEffect(() => {
    const quantity = parseFloat(orderData?.Quantity) || 0;
    const completeQty = parseFloat(orderData?.I_Complete_Qty) || 0;
    setRemainningQuantity(quantity - completeQty);
  }, [orderData?.Quantity, orderData?.I_Complete_Qty]);

  useEffect(() => {
    if (orderData?.Quote_CD && QuoteData.length > 0) {
      const selectedGroup = QuoteData.find(
        (item) => item.Od_Quote_CD === orderData.Quote_CD
      );

      setQuoteName(selectedGroup ? selectedGroup.Od_Quote_Name : "");
    }
  }, [orderData?.Quote_CD, QuoteData]);

  useEffect(() => {
    if (orderData?.Unit_Price && PriceData.length > 0) {
      const selectedGroup = PriceData.find(
        (item) => item.Price_CD === orderData.Unit_Price
      );

      setPriceName(selectedGroup ? selectedGroup.Price_Name : "");
    }
  }, [orderData?.Unit_Price, PriceData]);

  useEffect(() => {
    if (orderData?.Contract_Docu_CD && ContractDocuData.length > 0) {
      const selectedGroup = ContractDocuData.find(
        (item) => item.Contract_Docu_CD === orderData.Contract_Docu_CD
      );

      setDocuName(selectedGroup ? selectedGroup.Contract_Docu_Name : "");
    }
  }, [orderData?.Contract_Docu_CD, ContractDocuData]);

  useEffect(() => {
    if (orderData?.Specific_CD && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === orderData.Specific_CD
      );

      setSpecificName(selectedGroup ? selectedGroup.Specific_Name : "");
    }
  }, [orderData?.Specific_CD, SpecificData]);

  useEffect(() => {
    if (
      !orderData ||
      !orderData.Od_Progress_CD ||
      !OdProgressData ||
      OdProgressData.length === 0
    )
      return;

    const selectedGroup = OdProgressData.find(
      (item) => item.Od_Progress_CD === orderData.Od_Progress_CD
    );

    setOdProgressName(selectedGroup?.Od_Progress_Name || "");
  }, [orderData?.Od_Progress_CD, OdProgressData]);

  useEffect(() => {
    if (orderData?.Delivery_CD && DeliveryData.length > 0) {
      const selectedGroup = DeliveryData.find(
        (item) => item.Delivery_CD === orderData.Delivery_CD
      );

      setDeliveryName(selectedGroup ? selectedGroup.Delivery_Name : "");
    }
  }, [orderData?.Delivery_CD, DeliveryData]);

  useEffect(() => {
    if (!orderData?.Od_Progress_CD) {
      setOrderData((prev) => ({ ...prev, Od_Progress_CD: "0" }));
    }
  }, [orderData]);

  useEffect(() => {
    if (!hasUserEditedOrderNo && !searchOrderNo && StatusData?.Obj_Od_No) {
      setSearchOrderNo(StatusData.Obj_Od_No);
      searchOrderData(StatusData.Obj_Od_No);
      setButtonState((prevState) => ({
        ...prevState,
        F2: true,
        F3: true,
        F4: true,
        F5: true,
        F10: true,
        F11: true,
      }));
    }
  }, [StatusData, hasUserEditedOrderNo]);

  const CustomDateInput = React.forwardRef(
    (
      {
        value,
        onClick,
        onClear,
        dateValue,
        bgColor = "bg-[#ccffff]",
        width = "min-w-[150px]",
        showTime = false,
      },
      ref
    ) => {
      return (
        <div
          ref={ref} // เพิ่ม ref ที่นี่
          onClick={onClick}
          className={`flex items-center border-solid border-2 border-gray-500 rounded-md px-2 ${width} flex-grow ${bgColor} cursor-pointer`}
        >
          <span className="flex-grow">
            {dateValue
              ? format(
                  dateValue,
                  showTime ? "dd/MM/yyyy HH:mm:ss" : "dd/MM/yyyy"
                )
              : ""}
          </span>
          <AiTwotoneCalendar className="text-gray-600" />
          {dateValue && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation(); // หยุด event propagation เพื่อไม่ให้เปิด DatePicker ซ้ำ
                onClear();
              }}
              className="ml-2 text-gray-600"
            >
              X
            </button>
          )}
        </div>
      );
    }
  );
  const handleDateChange = (field, selectedDate) => {
    if (!selectedDate) {
      setDates((prev) => ({ ...prev, [field]: null }));
      setOrderData((prev) => ({ ...prev, [field]: null }));
      return;
    }

    const thaiDate = toThaiDate(selectedDate);

    setDates((prev) => ({ ...prev, [field]: thaiDate }));
    setOrderData((prev) => ({ ...prev, [field]: thaiDate }));

    console.log(
      "Final Thai datetime:",
      thaiDate.toLocaleString("th-TH", { timeZone: "Asia/Bangkok" })
    );
  };

  // ✅ ฟังก์ชันเคลียร์วันที่
  const handleClear = (field) => {
    setDates((prevDates) => ({
      ...prevDates,
      [field]: null,
    }));

    setOrderData((prev) => ({
      ...prev,
      [field]: null, // 🛑 เคลียร์ค่าใน orderData ด้วย
    }));
  };
  useEffect(() => {
    // รีเซ็ตค่า
    return () => {
      // Code to run on unmount
      setSearchOrderNo("");
      setOrderData();
    };
  }, []);
  ///reserve
  useEffect(() => {
    if (orderData) {
      setDates((prevDates) => {
        const updatedDates = { ...prevDates };
        Object.keys(updatedDates).forEach((key) => {
          if (orderData[key] === null) {
            updatedDates[key] = null; // 🛑 เคลียร์ค่าเป็น null ถ้า orderData เป็น null
          } else if (orderData[key]) {
            const parsedDate = new Date(orderData[key]);
            if (!isNaN(parsedDate.getTime())) {
              updatedDates[key] = parsedDate;
            }
          }
        });
        return updatedDates;
      });
    }
  }, [orderData]);

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-screen mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col overflow-x-hidden flex-grow p-2">
          <div className="bg-white grid grid-cols-1">
            <div className="overflow-x-auto">
              <div className="grid  gap-2 mx-5 py-4 min-w-[1200px]">
                <div className="flex justify-center items-center py-3">
                  <label className="text-xl font-bold">Order Info</label>
                </div>
                <div className="grid grid-cols-12 gap-2 py-4">
                  {/* Search Order No */}
                  <div className="flex flex-wrap gap-2 items-center col-span-3">
                    <label
                      htmlFor="Search_Order_No"
                      className="whitespace-nowrap"
                    >
                      Search Order No
                    </label>
                    <input
                      ref={SearchorderNoRef}
                      id="Search_Order_No"
                      value={searchOrderNo || ""}
                      onChange={handleInputChange}
                      onKeyDown={handleSearchEnter}
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md px-2 flex-1 min-w-[150px] h-10"
                      placeholder="Search Order Number"
                    />
                  </div>

                  {/* Order No */}
                  <div className="flex flex-wrap gap-2 pl-3 items-center col-span-3">
                    <label htmlFor="Order_No" className="whitespace-nowrap">
                      Order No.
                    </label>
                    <input
                      ref={orderNoRef}
                      disabled
                      id="Order_No"
                      value={orderData?.Order_No || ""}
                      onChange={handleInputChange}
                      type="text"
                      className="bg-[#ffff99] border-2 border-gray-500 rounded-md px-2 h-10"
                      onBlur={async (e) => {
                        await handleOrderNoCheck(e.target.value);
                      }}
                      onKeyDown={async (e) => {
                        if (e.key === "Enter") {
                          await handleOrderNoCheck(e.target.value);
                        }
                      }}
                    />
                  </div>

                  {/* Production Group */}
                  <div className="flex gap-2 items-center col-span-4">
                    <label
                      htmlFor="Product_Grp_CD"
                      className="whitespace-nowrap"
                    >
                      Production Group
                    </label>
                    <div className="w-28">
                      <CustomSelect
                        id="Product_Grp_CD"
                        data={WorkergData || []}
                        columns={["WorkG_CD", "WorkG_Name"]}
                        valueKey="WorkG_CD"
                        selectedValue={orderData?.Product_Grp_CD || ""}
                        onChange={handleCustomSelectChange}
                        bgColor="#cbfefe"
                        displayMode="table"
                      />
                    </div>
                    <input
                      disabled
                      id="Product_Grp_Name"
                      value={selectedWorkGName || ""}
                      onChange={(event) => setWorkergData(event)}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md px-1 w-40 h-10"
                      placeholder="Enter Group"
                    />
                  </div>

                  {/* Auto Year Change */}
                  <div className="flex gap-2 items-center">
                    <input
                      id="Auto_Year_Change"
                      checked={autoYearChange}
                      onChange={() => setAutoYearChange(!autoYearChange)}
                      type="checkbox"
                      className="w-6 h-6"
                    />
                    <label
                      htmlFor="Auto_Year_Change"
                      className="whitespace-nowrap"
                    >
                      Auto Year Change Group
                    </label>
                  </div>
                </div>

                <hr className="border-y-[1px] border-gray-300" />

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-1 mx-5 py-4">
                  <div className="grid grid-cols-1">
                    <div className="flex gap-1">
                      <div className="w-7/12 content-around">
                        {/* Request Delivery Date Group */}
                        <div className="flex items-center mb-8">
                          <label className="w-4/6 text-xs font-semibold">
                            Request Delivery Date
                          </label>
                          <div className="relative">
                            <DatePicker
                              ref={(el) =>
                                (dateRefs.current.Request_Delivery = el)
                              }
                              selected={dates.Request_Delivery}
                              onChange={(date) =>
                                handleDateChange("Request_Delivery", date)
                              }
                              dateFormat="dd/MM/yyyy"
                              isClearable
                              disabled={isDisabled}
                              // ตัวอย่างกำหนด disabled ตามเงื่อนไข (ถ้าคุณมี formState.stCalcDate)
                              // disabled
                              className="bg-white border-2 border-gray-500 rounded-md px-2 w-[150px]"
                              customInput={
                                <CustomDateInput
                                  dateValue={dates.Request_Delivery}
                                  onClear={() =>
                                    handleClear("Request_Delivery", null)
                                  }
                                  bgColor="bg-white"
                                />
                              }
                            />
                          </div>
                        </div>
                        <div className="flex items-center mb-8">
                          <label className="w-4/6 text-xs font-semibold">
                            Production Delivery Date
                          </label>
                          <div className="relative">
                            <DatePicker
                              ref={(el) =>
                                (dateRefs.current.Product_Delivery = el)
                              }
                              selected={dates.Product_Delivery}
                              onChange={(date) =>
                                handleDateChange("Product_Delivery", date)
                              }
                              dateFormat="dd/MM/yyyy"
                              isClearable
                              // ตัวอย่างกำหนด disabled ตามเงื่อนไข (ถ้าคุณมี formState.stCalcDate)
                              disabled={isDisabled}
                              className="bg-[#ffff99] border-2 border-gray-500 rounded-md px-2 w-[150px]"
                              customInput={
                                <CustomDateInput
                                  dateValue={dates.Product_Delivery}
                                  onClear={() =>
                                    handleClear("Product_Delivery", null)
                                  }
                                  bgColor="bg-[#ffff99]"
                                />
                              }
                            />
                          </div>
                        </div>
                        <div className="flex items-center mb-8  ">
                          <label className="w-4/6 text-xs font-semibold">
                            Comfirm Delivery Date
                          </label>
                          <div className="relative">
                            <DatePicker
                              ref={(el) =>
                                (dateRefs.current.Confirm_Delivery = el)
                              }
                              selected={dates.Confirm_Delivery}
                              onChange={(date) =>
                                handleDateChange("Confirm_Delivery", date)
                              }
                              dateFormat="dd/MM/yyyy"
                              isClearable
                              // ตัวอย่างกำหนด disabled ตามเงื่อนไข (ถ้าคุณมี formState.stCalcDate)
                              disabled={isDisabled}
                              className="bg-[#ffff99] border-2 border-gray-500 rounded-md px-2 w-[150px]"
                              customInput={
                                <CustomDateInput
                                  dateValue={dates.Confirm_Delivery}
                                  onClear={() =>
                                    handleClear("Confirm_Delivery", null)
                                  }
                                  bgColor="bg-[#ffff99]"
                                />
                              }
                            />
                          </div>
                        </div>
                        <div className="flex items-center">
                          <label className="w-4/6 text-xs font-semibold">
                            NAV Delivery Date
                          </label>
                          <div className="relative">
                            <DatePicker
                              ref={(el) => (dateRefs.current.NAV_Delivery = el)}
                              selected={dates.NAV_Delivery}
                              onChange={(date) =>
                                handleDateChange("NAV_Delivery", date)
                              }
                              dateFormat="dd/MM/yyyy"
                              isClearable
                              // ตัวอย่างกำหนด disabled ตามเงื่อนไข (ถ้าคุณมี formState.stCalcDate)
                              disabled={isDisabled}
                              className="bg-[#ffff99] border-2 border-gray-500 rounded-md px-2 w-[150px]"
                              customInput={
                                <CustomDateInput
                                  dateValue={dates.NAV_Delivery}
                                  onClear={() =>
                                    handleClear("NAV_Delivery", null)
                                  }
                                  bgColor="bg-[#ffff99]"
                                />
                              }
                            />
                          </div>
                        </div>
                      </div>

                      <div className="w-5/12 content-around">
                        <div className="flex items-center mb-3 gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_Pending"
                              checked={orderData?.Od_Pending === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_Pending"
                              checked={orderData?.Od_Pending === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Order Pending
                          </label>
                        </div>
                        <div className="flex items-center mb-3 gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Temp_Shipment"
                              checked={orderData?.Temp_Shipment === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Temp_Shipment"
                              checked={orderData?.Temp_Shipment === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Temporary Shipment
                          </label>
                        </div>
                        <div className="flex items-center mb-3 gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Unreceived"
                              checked={orderData?.Unreceived === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Unreceived"
                              checked={orderData?.Unreceived === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Unreceived
                          </label>
                        </div>
                        <div className="flex items-center mb-3 gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_CAT1"
                              checked={orderData?.Od_CAT1 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_CAT1"
                              checked={orderData?.Od_CAT1 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Order Identification1
                          </label>
                        </div>
                        <div className="flex items-center mb-3 gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_CAT2"
                              checked={orderData?.Od_CAT2 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_CAT2"
                              checked={orderData?.Od_CAT2 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Order Identification2
                          </label>
                        </div>
                        <div className="flex items-center gap-2">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_CAT3"
                              checked={orderData?.Od_CAT3 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_CAT3"
                              checked={orderData?.Od_CAT3 === true}
                              onChange={handleInputChange}
                              type="checkbox"
                              className="w-6 h-6"
                            />
                          )}
                          <label className="w-3/5 text-xs font-semibold">
                            Order Identification3
                          </label>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1">
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          NAV Goods Name
                        </label>
                        <div className="w-3/6">
                          <input
                            disabled
                            id="NAV_Name"
                            type="text"
                            value={orderData?.NAV_Name || ""}
                            onChange={handleInputChange}
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full" //ต้องการปิด input ให้ใส่ pointer-events-none ตัวนี้ถ้าไม่ต้องการปิด ให้ลบ pointer-events-none ตัวนี้ออก
                          />
                        </div>
                        <div className="w-1/6">
                          <button
                            onClick={handleGoods_Name_Reflect}
                            disabled={isDisabled}
                            className={`text-white text-lg w-full py-[5px] rounded-md text-[12px] flex justify-center items-center ${
                              isDisabled
                                ? "bg-gray-300 cursor-not-allowed text-gray-500"
                                : "bg-blue-500 hover:bg-blue-700"
                            }`}
                          >
                            <FaArrowDownLong />
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Goods Name
                        </label>
                        <div className="w-4/6">
                          <input
                            disabled
                            id="Product_Name"
                            type="text"
                            value={orderData?.Product_Name || ""}
                            onChange={(event) => handleInputChange(event)}
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          NAV Goods Size
                        </label>
                        <div className="w-3/6">
                          <input
                            disabled
                            // tabIndex="-1" //ต้องการปิด input ให้ใส่ตัวนี้ถ้าไม่ต้องการปิดให้ลบตัวนี้ออก
                            id="NAV_Size"
                            type="text"
                            value={orderData?.NAV_Size || ""}
                            onChange={handleInputChange}
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full " //ต้องการปิด input ให้ใส่ pointer-events-none ตัวนี้ถ้าไม่ต้องการปิด ให้ลบ pointer-events-none ตัวนี้ออก
                          />
                        </div>
                        <div className="w-1/6">
                          <button
                            onClick={handleGoods_Size_Reflect}
                            disabled={isDisabled}
                            className={`bg-blue-500 text-white text-lg w-full py-[5px] rounded-md text-[12px] flex justify-center items-center ${
                              isDisabled
                                ? "bg-gray-300 cursor-not-allowed text-gray-500"
                                : "hover:bg-blue-700"
                            }`}
                          >
                            <FaArrowDownLong />
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Goods Size
                        </label>
                        <div className="w-4/6">
                          <input
                            disabled
                            id="Product_Size"
                            type="text"
                            value={orderData?.Product_Size || ""}
                            onChange={(event) => handleInputChange(event)}
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <div className="w-5/6">
                          <div className="flex gap-2  mb-2">
                            <label className="text-xs font-semibold w-2/5">
                              Cutomer_Draw
                            </label>
                            <div className="w-3/5">
                              <input
                                disabled
                                // tabIndex="-1" //ต้องการปิด input ให้ใส่ตัวนี้ถ้าไม่ต้องการปิดให้ลบตัวนี้ออก
                                id="Customer_Draw"
                                value={orderData?.Customer_Draw || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full " //ต้องการปิด input ให้ใส่ pointer-events-none ตัวนี้ถ้าไม่ต้องการปิด ให้ลบ pointer-events-none ตัวนี้ออก
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="text-xs font-semibold w-2/5">
                              Company_Draw
                            </label>
                            <div className="w-3/5">
                              <input
                                disabled
                                // tabIndex="-1" //ต้องการปิด input ให้ใส่ตัวนี้ถ้าไม่ต้องการปิดให้ลบตัวนี้ออก
                                id="Company_Draw"
                                value={orderData?.Company_Draw || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full " //ต้องการปิด input ให้ใส่ pointer-events-none ตัวนี้ถ้าไม่ต้องการปิด ให้ลบ pointer-events-none ตัวนี้ออก
                              />
                            </div>
                          </div>
                        </div>
                        <div className="w-1/6">
                          <button
                            onClick={handleDrawNoReflectClick}
                            disabled={isDisabled}
                            className={`bg-blue-500 text-white text-lg w-full py-[22px] rounded-md text-[12px] flex justify-center items-center ${
                              isDisabled
                                ? "bg-gray-300 cursor-not-allowed text-gray-500"
                                : "hover:bg-blue-700"
                            }`}
                          >
                            <FaArrowDownLong />
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Draw
                        </label>
                        <div className="w-4/6">
                          <input
                            disabled
                            id="Product_Draw"
                            value={orderData?.Product_Draw || ""}
                            onChange={(event) => handleInputChange(event)}
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex justify-between w-full gap-2 mb-2">
                        <div className="w-7/12 flex gap-1 items-center">
                          <label className="text-xs font-semibold w-5/12">
                            Quantity
                          </label>
                          <div className="w-3/12">
                            <input
                              disabled
                              id="Quantity"
                              value={orderData?.Quantity || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                              onBlur={async (e) => {
                                const value = e.target.value;
                                if (value) {
                                  await confirmProductionTargetChange(value);
                                }
                              }}
                              onKeyDown={async (e) => {
                                if (e.key === "Enter") {
                                  const value = e.target.value;
                                  if (value) {
                                    await confirmProductionTargetChange(value);
                                  }
                                }
                              }}
                            />
                          </div>
                          <div className="w-20">
                            <CustomSelect
                              id="Unit_CD"
                              data={UnitData || []}
                              columns={["Unit_CD", "Unit_Name"]}
                              valueKey="Unit_CD"
                              selectedValue={orderData?.Unit_CD || ""}
                              onChange={handleCustomSelectChange}
                              bgColor="#cbfefe"
                              displayMode="table"
                            />
                          </div>

                          <div className="w-3/12">
                            <input
                              disabled
                              id="Unit_CD_Name"
                              value={unitName || ""}
                              onChange={(event) => setUnitData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          </div>
                        </div>
                        <div className="w-5/12 flex items-center">
                          <label className="text-xs font-semibold w-1/2">
                            Remaining Qty
                          </label>
                          <div className="w-1/2">
                            <input
                              disabled
                              id="Remainning_Quantity"
                              value={remainningQuantity}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Sale Instructions
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Sl_Instructions"
                              value={orderData.Sl_Instructions || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Sl_Instructions"
                              value={orderData?.Sl_Instructions || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Instructions
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Instructions"
                              value={orderData.Pd_Instructions || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Instructions"
                              value={orderData?.Pd_Instructions || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Remark
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Remark"
                              value={orderData.Pd_Remark || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Remark"
                              value={orderData?.Pd_Remark || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Inspection Remark
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="I_Remark"
                              value={orderData.I_Remark || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="I_Remark"
                              value={orderData?.I_Remark || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1">
                    <div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Sales Group
                        </label>
                        <div className="w-2/5">
                          <CustomSelect
                            id="Sales_Grp_CD"
                            data={WorkergData || []}
                            columns={["WorkG_CD", "WorkG_Abb"]}
                            valueKey="WorkG_CD"
                            selectedValue={orderData?.Sales_Grp_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#cbfefe"
                            displayMode="table"
                          />
                        </div>
                        <div className="w-2/5">
                          <input
                            disabled
                            id="Sales_Grp_CD_Name"
                            value={selectedSalesGrpAbb || ""}
                            onChange={(event) => setWorkergData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Sales Person
                        </label>
                        <div className="w-2/5">
                          <CustomSelect
                            id="Sales_Person_CD"
                            data={WorkerData || []}
                            columns={["Worker_CD", "Worker_Abb"]}
                            valueKey="Worker_CD"
                            selectedValue={orderData?.Sales_Person_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#cbfefe"
                            displayMode="table"
                          />
                        </div>
                        <div className="w-2/5">
                          <input
                            disabled
                            id="Sales_Person_CD_Name"
                            value={selectedSalesPersonAbb}
                            onChange={(event) => setWorkerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex items-center w-full gap-2 mb-2">
                        <label className="text-xs font-semibold">
                          Req Category
                        </label>
                        <div className="w-28">
                          <CustomSelect
                            id="Request1_CD"
                            data={Request1Data || []}
                            columns={["Request1_CD", "Request1_Name"]}
                            valueKey="Request1_CD"
                            selectedValue={orderData?.Request1_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/12">
                          <input
                            disabled
                            id="Request1_CD_Name"
                            value={request1Name}
                            onChange={(event) => setRequest1Data(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                        <div className="w-28">
                          <CustomSelect
                            id="Request2_CD"
                            data={Request2Data || []}
                            columns={["Request2_CD", "Request2_Name"]}
                            valueKey="Request2_CD"
                            selectedValue={orderData?.Request2_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ff99cc"
                            displayMode="table"
                          />
                        </div>
                        <div className="w-2/12">
                          <input
                            disabled
                            id="Request2_CD_Name"
                            value={request2Name}
                            onChange={(event) => setRequest2Data(event)}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                        <div className="w-28">
                          <CustomSelect
                            id="Request3_CD"
                            data={Request3Data || []}
                            columns={[
                              "Request3_CD",
                              "Request3_Abb",
                              "Request3_Name",
                            ]}
                            valueKey="Request3_CD"
                            selectedValue={orderData?.Request3_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>
                        <div className="w-2/12">
                          <input
                            disabled
                            id="Request3_CD_Name"
                            value={request3Name}
                            onChange={(event) => setRequest3Data(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-full gap-2">
                          <label className="text-xs font-semibold w-2/5">
                            Material1
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Material1"
                                value={orderData.Material1 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Material1"
                                value={orderData?.Material1 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-full gap-2 mb-2">
                          <label className="text-xs font-semibold w-1/5">
                            H/T
                          </label>
                          <div className="w-4/5">
                            {orderData ? (
                              <input
                                disabled
                                id="H_Treatment1"
                                value={orderData.H_Treatment1 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="H_Treatment1"
                                value={orderData?.H_Treatment1 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-2/5">
                            Material2
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Material2"
                                value={orderData.Material2 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Material2"
                                value={orderData?.Material2 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-1/5">
                            H/T
                          </label>
                          <div className="w-4/5">
                            {orderData ? (
                              <input
                                disabled
                                id="H_Treatment2"
                                value={orderData.H_Treatment2 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="H_Treatment2"
                                value={orderData?.H_Treatment2 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-2/5">
                            Material3
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Material3"
                                value={orderData.Material3 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Material3"
                                value={orderData?.Material3 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-1/5">
                            H/T
                          </label>
                          <div className="w-4/5">
                            {orderData ? (
                              <input
                                disabled
                                id="H_Treatment3"
                                value={orderData.H_Treatment3 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="H_Treatment3"
                                value={orderData?.H_Treatment3 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-2/5">
                            Material4
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Material4"
                                value={orderData.Material4 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Material4"
                                value={orderData?.Material4 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-1/5">
                            H/T
                          </label>
                          <div className="w-4/5">
                            {orderData ? (
                              <input
                                disabled
                                id="H_Treatment4"
                                value={orderData.H_Treatment4 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="H_Treatment4"
                                value={orderData?.H_Treatment4 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-2/5">
                            Material5
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Material5"
                                value={orderData.Material5 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Material5"
                                value={orderData?.Material5 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-1/2 gap-2">
                          <label className="text-xs font-semibold w-1/5">
                            H/T
                          </label>
                          <div className="w-4/5">
                            {orderData ? (
                              <input
                                disabled
                                id="H_Treatment5"
                                value={orderData.H_Treatment5 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="H_Treatment5"
                                value={orderData?.H_Treatment5 || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 mb-2">
                        <div className="flex gap-2">
                          <label className="text-xs font-semibold">
                            Coating
                          </label>
                          <div className="w-20">
                            <CustomSelect
                              id="Coating_CD"
                              data={CoatingData || []}
                              columns={["Coating_CD", "Coating_Name"]}
                              valueKey="Coating_CD"
                              selectedValue={orderData?.Coating_CD || ""}
                              onChange={handleCustomSelectChange}
                              bgColor="#cbfefe"
                              displayMode="table"
                            />
                          </div>

                          <div className="w-20">
                            <input
                              disabled
                              id="Coating_Name"
                              value={coatingName || ""}
                              onChange={(event) => setCoatingData(event)}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          </div>
                        </div>
                        <div className="flex w-3/5 gap-2">
                          <label className="text-xs font-semibold">
                            CT_Detail
                          </label>
                          <div className="w-4/6">
                            {orderData ? (
                              <input
                                disabled
                                id="Coating"
                                value={orderData.Coating || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                              />
                            ) : (
                              <input
                                disabled
                                id="Coating"
                                value={orderData?.Coating || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Tolerance
                        </label>
                        <div className="w-4/5">
                          {orderData ? (
                            <input
                              disabled
                              id="Tolerance"
                              value={orderData.Tolerance || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Tolerance"
                              value={orderData?.Tolerance || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full gap-2 mb-2">
                        <div className="flex w-6/12 gap-2 items-center">
                          <label className="text-xs font-semibold w-2/5">
                            Quotation No
                          </label>
                          <div className="w-3/5">
                            {orderData ? (
                              <input
                                disabled
                                id="Quote_No"
                                value={orderData.Quote_No || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-[#ffff00] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            ) : (
                              <input
                                disabled
                                id="Quote_No"
                                value={orderData?.Quote_No || ""}
                                onChange={handleInputChange}
                                type="text"
                                className="bg-[#ffff00] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex w-6/12 gap-2 items-center">
                          <label className="text-xs font-semibold w-1/5">
                            CAT
                          </label>
                          <div className="w-2/5">
                            <CustomSelect
                              id="Quote_CD"
                              data={QuoteData || []}
                              columns={["Od_Quote_CD", "Od_Quote_Name"]}
                              valueKey="Od_Quote_CD"
                              selectedValue={orderData?.Quote_CD || ""}
                              onChange={handleCustomSelectChange}
                              bgColor="#ff99cc"
                              displayMode="table"
                            />
                          </div>

                          <div className="w-2/5">
                            <input
                              disabled
                              id="Quote_CD_Name"
                              value={quoteName || ""}
                              onChange={(event) => setQuoteData(event)}
                              type="text"
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Item
                        </label>
                        <div className="w-2/5">
                          <CustomSelect
                            id="Item1_CD"
                            data={Item1Data || []}
                            columns={["Item1_CD", "Item1_Name"]}
                            valueKey="Item1_CD"
                            selectedValue={orderData?.Item1_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#cbfefe"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/5">
                          <input
                            disabled
                            id="Item1_CD_Name"
                            value={itemName || ""}
                            onChange={(event) => setItem1Data(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center mb-2">
                        <label className="text-xs font-semibold w-3/12">
                          Customer Materail
                        </label>
                        <div className="w-9/12">
                          {orderData ? (
                            <input
                              disabled
                              id="Custom_Material"
                              value={orderData.Custom_Material || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Custom_Material"
                              value={orderData?.Custom_Material || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full items-center mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          PO No
                        </label>
                        <div className="w-4/5">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_No_of_Custom"
                              value={orderData.Od_No_of_Custom || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_No_of_Custom"
                              value={orderData?.Od_No_of_Custom || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Production Docu
                        </label>
                        <div className="w-3/5">
                          <input
                            readOnly
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full cursor-pointer"
                            value={orderData?.Product_Docu || ""}
                            onClick={() =>
                              handlePathClick(orderData?.Product_Docu)
                            }
                          />
                        </div>
                        <div className="w-1/5">
                          <button
                            className={`bg-blue-500 text-white w-full  rounded-md text-sm py-1 flex justify-center items-center ${
                              isSearchCompleted
                                ? "bg-gray-300 cursor-not-allowed text-gray-500"
                                : "hover:bg-blue-700"
                            }`}
                            onClick={handleSettingClick}
                            disabled={isSearchCompleted}
                          >
                            Setting
                          </button>
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-1/5">
                          Supplement <br />
                          Docu
                        </label>
                        <div className="w-3/5">
                          <input
                            disabled
                            id=""
                            type="text"
                            className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                        <div className="w-1/5">
                          <button
                            className={`bg-blue-500 text-white w-full  rounded-md text-sm py-1 flex justify-center items-center ${
                              isSearchCompleted
                                ? "bg-gray-300 cursor-not-allowed text-gray-500"
                                : "hover:bg-blue-700"
                            }`}
                            onClick={handleSettingClick}
                            disabled={isSearchCompleted} // ใช้ disabled กับปุ่ม
                          >
                            Setting
                          </button>
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-1 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production_Received
                        </label>
                        <div className="w-[250px]">
                          <DatePicker
                            ref={(el) =>
                              (dateRefs.current.Pd_Received_Date = el)
                            }
                            selected={dates.Pd_Received_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Pd_Received_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Pd_Received_Date}
                                onClear={() =>
                                  handleClear("Pd_Received_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production_Completed
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) =>
                              (dateRefs.current.Pd_Complete_Date = el)
                            }
                            selected={dates.Pd_Complete_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Pd_Complete_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Pd_Complete_Date}
                                onClear={() =>
                                  handleClear("Pd_Complete_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          QC_Completed
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) =>
                              (dateRefs.current.I_Completed_Date = el)
                            }
                            selected={dates.I_Completed_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("I_Completed_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.I_Completed_Date}
                                onClear={() =>
                                  handleClear("I_Completed_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Shipment_Date
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) => (dateRefs.current.Shipment_Date = el)}
                            selected={dates.Shipment_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Shipment_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Shipment_Date}
                                onClear={() =>
                                  handleClear("Shipment_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production_Calc_Date
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) => (dateRefs.current.Pd_Calc_Date = el)}
                            selected={dates.Pd_Calc_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Pd_Calc_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Pd_Calc_Date}
                                onClear={() =>
                                  handleClear("Pd_Calc_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Calc_processing_Data
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) =>
                              (dateRefs.current.Calc_Process_Date = el)
                            }
                            selected={dates.Calc_Process_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Calc_Process_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Calc_Process_Date}
                                onClear={() =>
                                  handleClear("Calc_Process_Date", null)
                                }
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                      <div className="flex w-full items-center gap-2 mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order_Modify_Date
                        </label>
                        <div className="w-4/6">
                          <DatePicker
                            ref={(el) => (dateRefs.current.Od_Upd_Date = el)}
                            selected={dates.Od_Upd_Date}
                            timeFormat="HH:mm:ss"
                            onChange={(date) =>
                              handleDateChange("Od_Upd_Date", date)
                            }
                            dateFormat="dd/MM/yyyy HH:mm:ss"
                            isClearable
                            disabled={isDisabled}
                            className="border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            customInput={
                              <CustomDateInput
                                dateValue={dates.Od_Upd_Date}
                                onClear={() => handleClear("Od_Upd_Date", null)}
                                bgColor="bg-white"
                                width="min-w-[250px]" // ให้ความกว้างเต็ม
                                showTime
                              />
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1">
                    <div className="grid justify-between">
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Customer
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Customer_CD"
                            data={CustomerData || []}
                            columns={["Customer_CD", "Customer_Abb"]}
                            valueKey="Customer_CD"
                            selectedValue={orderData?.Customer_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="white"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Customer_CD_Name"
                            value={selectedCustomerAbb || ""}
                            onChange={(event) => setCustomerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Long Name
                        </label>
                        <div className="w-4/6">
                          <input
                            disabled
                            id="Customer_Name"
                            value={selectedCustomerName || ""}
                            onChange={(event) => setCustomerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Delivery Category
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Supply_CD"
                            data={SupplyData || []}
                            columns={["Supply_CD", "Supply_Name"]}
                            valueKey="Supply_CD"
                            selectedValue={orderData?.Supply_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ff99cc"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Supply_CD_Name"
                            value={supplyName || ""}
                            onChange={(event) => setSupplyData(event)}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Delivery Destination
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Destination_CD"
                            data={filteredWorkgData || []}
                            columns={["WorkG_CD", "WorkG_Name"]}
                            valueKey="WorkG_CD"
                            selectedValue={orderData?.Destination_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ff99cc"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Destination_CD_Name"
                            value={destinationName}
                            onChange={(event) => setWorkgData(event)}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Contract Document
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Contract_Docu_CD"
                            data={ContractDocuData || []}
                            columns={["Contract_Docu_CD", "Contract_Docu_Name"]}
                            valueKey="Contract_Docu_CD"
                            selectedValue={orderData?.Contract_Docu_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ff99cc"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Contract_Docu_CD_Name"
                            value={DocuName || ""}
                            onChange={(event) => setContractDocu(event)}
                            type="text"
                            className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Unit Price
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Price_CD"
                            data={PriceData || []}
                            columns={["Price_CD", "Price_Name"]}
                            valueKey="Price_CD"
                            selectedValue={orderData?.Price_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ff99cc"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6 flex gap-1">
                          <div className="w-2/5">
                            <input
                              disabled
                              id="Price_Name"
                              value={PriceName || ""}
                              onChange={(event) => setPriceData(event)}
                              type="text"
                              className="bg-[#ff99cc] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          </div>
                          <div className="w-3/5">
                            <input
                              disabled
                              id="Unit_Price"
                              value={orderData?.Unit_Price || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order No of Production Split
                        </label>
                        <div className="w-3/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Od_No_of_Pd_Split"
                              value={orderData.Od_No_of_Pd_Split || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff00] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          ) : (
                            <input
                              disabled
                              id="Od_No_of_Pd_Split"
                              value={orderData?.Od_No_of_Pd_Split || ""}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-[#ffff00] border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                            />
                          )}
                        </div>
                        <div className="w-1/6">
                          <button className="bg-blue-500 text-white w-full py-[5px] rounded-md hover:bg-blue-700 text-[12px] flex justify-center items-center gap-2">
                            Quot <FaArrowRightLong />
                          </button>
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order Controller Person
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Od_Ctl_Person_CD"
                            data={WorkerData || []}
                            columns={[
                              "Worker_CD",
                              "Worker_Name",
                              "Worker_Remark",
                            ]}
                            valueKey="Worker_CD"
                            selectedValue={orderData?.Od_Ctl_Person_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Od_Ctl_Person_CD_Name"
                            value={personName || ""}
                            onChange={(event) => setWorkerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order Register Person
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Od_Reg_Person_CD"
                            data={WorkerData || []}
                            columns={[
                              "Worker_CD",
                              "Worker_Name",
                              "Worker_Remark",
                            ]}
                            valueKey="Worker_CD"
                            selectedValue={orderData?.Od_Reg_Person_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Od_Reg_Person_CD_Name"
                            value={regPersonName || ""}
                            onChange={(event) => setWorkerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order Update Person
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Od_Upd_Person_CD"
                            data={WorkerData || []}
                            columns={[
                              "Worker_CD",
                              "Worker_Name",
                              "Worker_Remark",
                            ]}
                            valueKey="Worker_CD"
                            selectedValue={orderData?.Od_Upd_Person_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Od_Upd_Person_Name"
                            value={updPersonName || ""}
                            onChange={(event) => setWorkerData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Specific Item
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Specific_CD"
                            data={SpecificData || []}
                            columns={[
                              "Specific_CD",
                              "Specific_Name",
                              "Specific_Remark",
                            ]}
                            valueKey="Specific_CD"
                            selectedValue={orderData?.Specific_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Specific_Name"
                            value={SpecificName || ""}
                            onChange={(event) => setSpecificData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Order Progress CAT
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Od_Progress_CD"
                            data={OdProgressData || []}
                            columns={[
                              "Od_Progress_CD",
                              "Od_Progress_Name",
                              "Od_Progress_Remark",
                            ]}
                            valueKey="Od_Progress_CD"
                            selectedValue={orderData?.Od_Progress_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Od_Progress_Name"
                            value={OdProgressName || ""}
                            onChange={(event) => setOdProgressData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Delivery Date CAT
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Delivery_CD"
                            data={DeliveryData || []}
                            columns={[
                              "Delivery_CD",
                              "Delivery_Name",
                              "Delivery_Remark",
                            ]}
                            valueKey="Delivery_CD"
                            selectedValue={orderData?.Delivery_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Delivery_Name"
                            value={DeliveryName || ""}
                            onChange={(event) => setDeliveryData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Schedule CAT
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Schedule_CD"
                            data={ScheduleData || []}
                            columns={[
                              "Schedule_CD",
                              "Schedule_Name",
                              "Schedule_Remark",
                            ]}
                            valueKey="Schedule_CD"
                            selectedValue={orderData?.Schedule_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>
                        <div className="w-2/6">
                          <input
                            disabled
                            id="Pl_Schedule_Name"
                            value={Schedule_Name || ""}
                            onChange={(event) => setScheduleData(event)}
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Target CAT
                        </label>
                        <div className="w-2/6">
                          <CustomSelect
                            id="Target_CD"
                            data={TargetData || []}
                            columns={[
                              "Target_CD",
                              "Target_Name",
                              "Target_Remark",
                            ]}
                            valueKey="Target_CD"
                            selectedValue={orderData?.Target_CD || ""}
                            onChange={handleCustomSelectChange}
                            bgColor="#ffff99"
                            displayMode="table"
                          />
                        </div>

                        <div className="w-2/6">
                          <input
                            disabled
                            id="Target_Name"
                            value={targetName || ""}
                            onChange={(event) => setTargetData(event)}
                            type="text"
                            className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full h-10"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Target Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Target_Qty"
                              value={orderData?.Pd_Target_Qty ?? 0}
                              onChange={(event) => handleInputChange(event)}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Target_Qty"
                              value={orderData?.Pd_Target_Qty ?? 0}
                              onChange={(event) => handleInputChange(event)}
                              type="text"
                              className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Pruduction Completed Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Complete_Qty"
                              value={orderData?.Pd_Complete_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Complete_Qty"
                              value={orderData?.Pd_Complete_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Inspection Completed Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="I_Complete_Qty"
                              value={orderData?.I_Complete_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="I_Complete_Qty"
                              value={orderData?.I_Complete_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Delivery Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Shipment_Qty"
                              value={orderData?.Shipment_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Shipment_Qty"
                              value={orderData?.Shipment_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Split Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Split_Qty"
                              value={orderData?.Pd_Split_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Split_Qty"
                              value={orderData?.Pd_Split_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          Production Calculation Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="Pd_Calc_Qty"
                              value={orderData?.Pd_Calc_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="Pd_Calc_Qty"
                              value={orderData?.Pd_Calc_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center mb-2">
                        <label className="text-xs font-semibold w-2/6">
                          NG Qty
                        </label>
                        <div className="w-4/6">
                          {orderData ? (
                            <input
                              disabled
                              id="NG_Qty"
                              value={orderData?.NG_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          ) : (
                            <input
                              disabled
                              id="NG_Qty"
                              value={orderData?.NG_Qty ?? 0}
                              onChange={handleInputChange}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-2 mt-3">
          <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-2">
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F1}
                id="F1"
                onClick={handleF1Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Search <br />
                検索 (F1)
              </button>
              <button
                disabled={!buttonState.F2}
                id="F2"
                onClick={handleF2Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Edit <br />
                編集 (F2)
              </button>
              <button
                disabled={!buttonState.newAddButton}
                id="newAddButton"
                onClick={handleF3Click}
                className="bg-blue-500 px-2 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                New Add <br />
                追加 (F3)
              </button>
              <button
                id="F3"
                disabled={!buttonState.F3}
                onClick={handleF4Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Sub-Con <br />
                手配(F3)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F4"
                disabled={!buttonState.F4}
                onClick={handleF5Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Plan <br />
                計画 (F4)
              </button>
              <button
                id="F5"
                disabled={!buttonState.F5}
                onClick={handleF6Click}
                className="bg-blue-500 px-1 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                All P-Sheet <br />
                全指示書(F5)
              </button>
              <button
                id="F7"
                disabled={!buttonState.F7}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                (F7)
              </button>
              <button
                id="F8"
                disabled={!buttonState.F8}
                onClick={handleF8Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Master <br />
                マスタ (F8)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                disabled={!buttonState.F9}
                id="F9"
                onClick={handleF9Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Save <br />
                登録 (F9)
              </button>
              <button
                id="F10"
                disabled={!buttonState.F10}
                onClick={handleF10Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Delete <br />
                削除 (F10)
              </button>
              <button
                disabled={!buttonState.F11}
                id="F11"
                onClick={handleF11Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Next <br />
                Input <br />
                次へ (F11)
              </button>
              <button
                disabled={!buttonState.F12}
                id="F12"
                onClick={handleF12Click}
                className="bg-blue-500 py-2 rounded-lg hover:bg-blue-700 font-medium text-xs lg:text-sm text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Exit <br />
                終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// const searchPermission = (status) => {
//   document.getElementById("Search_Order_No").disabled = !status;
// };

// const editPermission = (status) => {
//   document.getElementById("Order_No").disabled = !status;
//   document.getElementById("Product_Grp_CD").disabled = !status;
//   document.getElementById("Request_Delivery").disabled = !status;
//   document.getElementById("Product_Delivery").disabled = !status;
//   document.getElementById("Confirm_Delivery").disabled = !status;
//   document.getElementById("NAV_Delivery").disabled = !status;
//   document.getElementById("Od_Pending").disabled = !status;
//   document.getElementById("Temp_Shipment").disabled = !status;
//   document.getElementById("Unreceived").disabled = !status;
//   document.getElementById("Od_CAT1").disabled = !status;
//   document.getElementById("Od_CAT2").disabled = !status;
//   document.getElementById("Od_CAT3").disabled = !status;
//   document.getElementById("NAV_Name").disabled = !status;
//   document.getElementById("Product_Name").disabled = !status;
//   document.getElementById("NAV_Size").disabled = !status;
//   document.getElementById("Product_Size").disabled = !status;
//   document.getElementById("Customer_Draw").disabled = !status;
//   document.getElementById("Company_Draw").disabled = !status;
//   document.getElementById("Product_Draw").disabled = !status;
//   document.getElementById("Quantity").disabled = !status;
//   document.getElementById("Unit_CD").disabled = !status;
//   document.getElementById("Sl_Instructions").disabled = !status;
//   document.getElementById("Pd_Instructions").disabled = !status;
//   document.getElementById("Pd_Remark").disabled = !status;
//   document.getElementById("I_Remark").disabled = !status;
//   document.getElementById("Sales_Grp_CD").disabled = !status;
//   document.getElementById("Sales_Person_CD").disabled = !status;
//   document.getElementById("Request1_CD").disabled = !status;
//   document.getElementById("Request2_CD").disabled = !status;
//   document.getElementById("Request3_CD").disabled = !status;
//   document.getElementById("Material1").disabled = !status;
//   document.getElementById("H_Treatment1").disabled = !status;
//   document.getElementById("Material2").disabled = !status;
//   document.getElementById("H_Treatment2").disabled = !status;
//   document.getElementById("Material3").disabled = !status;
//   document.getElementById("H_Treatment3").disabled = !status;
//   document.getElementById("Material4").disabled = !status;
//   document.getElementById("H_Treatment4").disabled = !status;
//   document.getElementById("Material5").disabled = !status;
//   document.getElementById("H_Treatment5").disabled = !status;
//   document.getElementById("Tolerance").disabled = !status;
//   document.getElementById("Coating_CD").disabled = !status;
//   document.getElementById("Coating").disabled = !status;
//   document.getElementById("Quote_No").disabled = !status;
//   document.getElementById("Quote_CD").disabled = !status;
//   document.getElementById("Item1_CD").disabled = !status;
//   document.getElementById("Custom_Material").disabled = !status;
//   document.getElementById("Od_No_of_Custom").disabled = !status;
//   document.getElementById("Pd_Received_Date").disabled = !status;
//   document.getElementById("I_Completed_Date").disabled = !status;
//   document.getElementById("Pd_Complete_Date").disabled = !status;
//   document.getElementById("Shipment_Date").disabled = !status;
//   document.getElementById("Pd_Calc_Date").disabled = !status;
//   document.getElementById("Calc_Process_Date").disabled = !status;
//   document.getElementById("Od_Upd_Date").disabled = !status;
//   document.getElementById("Customer_CD").disabled = !status;
//   document.getElementById("Supply_CD").disabled = !status;
//   document.getElementById("Destination_CD").disabled = !status;
//   document.getElementById("Contract_Docu_CD").disabled = !status;
//   document.getElementById("Price_CD").disabled = !status;
//   document.getElementById("Od_No_of_Pd_Split").disabled = !status;
//   document.getElementById("Od_Ctl_Person_CD").disabled = !status;
//   document.getElementById("Od_Reg_Person_CD").disabled = !status;
//   document.getElementById("Od_Upd_Person_CD").disabled = !status;
//   document.getElementById("Specific_CD").disabled = !status;
//   document.getElementById("Od_Progress_CD").disabled = !status;
//   document.getElementById("Delivery_CD").disabled = !status;
//   document.getElementById("Schedule_CD").disabled = !status;
//   document.getElementById("Target_CD").disabled = !status;
//   document.getElementById("Pd_Target_Qty").disabled = !status;
//   document.getElementById("Pd_Complete_Qty").disabled = !status;
//   document.getElementById("I_Complete_Qty").disabled = !status;
//   document.getElementById("Shipment_Qty").disabled = !status;
//   document.getElementById("Pd_Split_Qty").disabled = !status;
//   document.getElementById("Pd_Calc_Qty").disabled = !status;
//   document.getElementById("NG_Qty").disabled = !status;
// };
