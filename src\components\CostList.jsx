import React, { useState, useEffect } from "react";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import { useLocation, useNavigate } from "react-router-dom";
import { useCostList } from "../hooks/use-costlist";
import { usePlanList } from "../hooks/use-planlist";
import { useOrder } from "../hooks/use-order";
import Swal from "sweetalert2";
import axios from "axios";
//import Select from 'react-select';
import Papa from "papaparse";
// Icons
import { IoIosArrowRoundForward } from "react-icons/io";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function CostList() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const navigate = useNavigate();
  const location = useLocation();
  const [filteredCostlistData, setFilteredCostlistData] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(filteredCostlistData.length / rowsPerPage);

  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = filteredCostlistData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const [showDialog, setShowDialog] = useState(false);
  const [columnsVisibility, setColumnsVisibility] = useState({
    Product_Delivery: true,
    Order_No: true,
    Parts_No: true,
    Product_Grp: true,
    Customer_CD: true,
    Customer_Abb: true,
    Product_Name: true,
    Product_Size: true,
    Product_Draw: true,
    Quantity: true,
    Pd_Calc_Qty: true,
    Unit: true,
    Target: true,
    Product_Docu: true,
    Sales_Grp: true,
    Sales_Person: true,
    Request1: true,
    Request2: true,
    Request3: true,
    Material1: true,
    Material2: true,
    Coating_CD: true,
    Item1: true,
    Item2: true,
    Item3: true,
    Item4: true,
    Price: true,
    Unit_Price: true,
    Pd_Received_Date: true,
    Request_Delivery: true,
    NAV_Delivery: true,
    I_Completed_Date: true,
    Pd_Calc_Date: true,
    Shipment_Date: true,
    Specific: true,
    Confirm_Delivery: true,
    Delivery: true,
    Schedule: true,
    Od_Progress: true,
    Sl_Instructions: true,
    Pd_Instructions: true,
    Pd_Remark: true,
    I_Remark: true,
    Pd_Complete_Date: true,
    Supple_Docu: true,
    Process1: true,
    Process2: true,
    Process3: true,
    Process4: true,
    Process5: true,
    Process6: true,
    Process7: true,
    Process8: true,
    Process9: true,
    Process10: true,
    Process11: true,
    Process12: true,
    Process13: true,
    Process14: true,
    Process15: true,
    Process16: true,
    Process17: true,
    Process18: true,
    Process19: true,
    Process20: true,
    Process21: true,
    Process22: true,
    Process23: true,
    Process24: true,
    Process25: true,
    Process26: true,
    Process27: true,
    Process28: true,
    Process29: true,
    Process30: true,
    Process31: true,
    Process32: true,
    Process33: true,
    Process34: true,
    Process35: true,
    Process36: true,
  });

  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    F3: true,
    F4: false,
    F5: false,
    F6: true,
    F7: true,
    F8: true,
    F9: true,
    F10: false,
    F11: true,
    F12: true,
  });
  const {
    initialFormState,
    qrCostListData,
    setQrCostListData,
    WorkerData,
    setWorkerData,
    costListData,
    setCostListData,
    scheduleData,
    plprogressData,
    fetchCostList,
  } = useCostList();

  const {
    fetchOrders,
    OdProgressData,
    setWorkgData,
    WorkgData,
    CustomerData,
    setCustomerData,
    SpecificData,
    setSpecificData,
    PriceData,
    setPriceData,
    Request1Data,
    setRequest1Data,
    Request2Data,
    setRequest2Data,
    Request3Data,
    setRequest3Data,
    CoatingData,
    setCoatingData,
    Item1Data,
    setItem1Data,
    DeliveryData,
    TargetData,
  } = useOrder();
  const { PlProgressData } = usePlanList();
  const { searchOrderNo: initialSearchOrderNo = "" } = location.state || {};
  const [formState, setFormState] = useState(initialFormState);
  const [selectedSalesGrpAbb, setSelectedSalesGrpAbb] = useState("");
  const [selectedSalesGrpAbb2, setSelectedSalesGrpAbb2] = useState("");
  const [destinationName, setDestinationName] = useState("");
  const [destinationName2, setDestinationName2] = useState("");
  const [destinationName3, setDestinationName3] = useState("");
  const [destinationName4, setDestinationName4] = useState("");
  const [destinationName5, setDestinationName5] = useState("");
  const [selectedCustomerAbb, setSelectedCustomerAbb] = useState("");
  const [selectedCustomerAbb2, setSelectedCustomerAbb2] = useState("");
  const [selectedCustomerAbb3, setSelectedCustomerAbb3] = useState("");
  const [selectedCustomerAbb4, setSelectedCustomerAbb4] = useState("");
  const [SpecificName, setSpecificName] = useState("");
  const [SpecificName2, setSpecificName2] = useState("");
  const [SpecificName3, setSpecificName3] = useState("");
  const [SpecificName4, setSpecificName4] = useState("");
  const [PriceName, setPriceName] = useState("");
  const [request1Name, setRequest1Name] = useState("");
  const [request2Name, setRequest2Name] = useState("");
  const [request3Name, setRequest3Name] = useState("");
  const [itemName, setItemName] = useState("");
  const [coatingName, setCoatingName] = useState("");
  const [coatingName2, setCoatingName2] = useState("");
  const [coatingName3, setCoatingName3] = useState("");
  const [coatingName4, setCoatingName4] = useState("");
  const [selectedWorker, setSelectedWorker] = useState("");
  const [selectedWorkerDisplay, setSelectedWorkerDisplay] = useState("");
  const [isTableVisible, setIsTableVisible] = useState(false);
  const [delivery1, setDelivery1] = useState("Product");
  const [delivery2, setDelivery2] = useState("Comfirm");
  const [delivery3, setDelivery3] = useState("Request");
  const [viewSchedule, setViewSchedule] = useState("Manual");
  const [planTarget, setPlanTarget] = useState("");
  const [format, setFormat] = useState("Progress");
  const [changePage, setChangePage] = useState("No_Change_Page");
  const [target, setTarget] = useState("Production");
  const [markDays, setMarkDays] = useState("");

  const handleD1TypeChange = (event) => {
    const delivery1 = event.target.value;
    setDelivery1(delivery1);
  };
  const handleD2TypeChange = (event) => {
    setDelivery2(event.target.value);
  };
  const handleD3TypeChange = (event) => {
    setDelivery3(event.target.value);
  };
  const handleViewSchedule = (event) => {
    setViewSchedule(event.target.value);
  };
  const handlePanTarget = (event) => {
    setPlanTarget(event.target.value);
  };
  const handleChangePage = (event) => {
    setChangePage(event.target.value);
  };
  const handleTarget = (event) => {
    const value = event.target.value;
    setTarget(value);

    let odProgress = "";
    let plProgress = "";

    if (value === "Production") {
      odProgress = 3;
      plProgress = 3;
    } else if (value === "QC") {
      odProgress = 4;
      plProgress = 4;
    } else if (value === "Administrator") {
      odProgress = 6;
      plProgress = 9;
    }

    setCostListData((prev) => ({
      ...prev,
      S_Ed_Od_Progress_CD: odProgress,
      S_Ed_Pl_Progress_CD: plProgress,
    }));
  };
  const handleMarkDays = (e) => {
    const dateValue = e.target.value;
    setMarkDays(dateValue); // Save the selected date to state
  };

  useEffect(() => {
    if (costListData?.S_Od_Ctl_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === costListData?.S_Od_Ctl_Person_CD
      );

      setSelectedSalesGrpAbb(selectedGroup ? selectedGroup.Worker_Abb : "");
    }

    if (costListData?.S_Sl_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === costListData?.S_Sl_Person_CD
      );

      setSelectedSalesGrpAbb2(selectedGroup ? selectedGroup.Worker_Abb : "");
    }
    if (costListData?.S_Pl_Reg_Person_CD && WorkerData.length > 0) {
      const selectedGroup = WorkerData.find(
        (item) => item.Worker_CD === costListData?.S_Pl_Reg_Person_CD
      );

      setSelectedWorker(selectedGroup ? selectedGroup.Worker_Abb : "");
    }
  }, [
    costListData?.S_Od_Ctl_Person_CD,
    costListData?.S_Sl_Person_CD,
    costListData?.S_Pl_Reg_Person_CD,
    WorkerData,
  ]);
  useEffect(() => {
    if (costListData?.S_St_Pd_Grp_CD && WorkgData.length > 0) {
      const selectedGroup = WorkgData.find(
        (item) => item.WorkG_CD === costListData?.S_St_Pd_Grp_CD
      );
      setDestinationName(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }
    if (costListData?.S_Ed_Pd_Grp_CD && WorkgData.length > 0) {
      const selectedGroup = WorkgData.find(
        (item) => item.WorkG_CD === costListData?.S_Ed_Pd_Grp_CD
      );
      setDestinationName2(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }
    if (costListData?.S_No_Pd_Grp_CD1 && WorkgData.length > 0) {
      const selectedGroup = WorkgData.find(
        (item) => item.WorkG_CD === costListData?.S_No_Pd_Grp_CD1
      );
      setDestinationName3(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }
    if (costListData?.S_No_Pd_Grp_CD2 && WorkgData.length > 0) {
      const selectedGroup = WorkgData.find(
        (item) => item.WorkG_CD === costListData?.S_No_Pd_Grp_CD2
      );
      setDestinationName4(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }

    if (costListData?.S_Sl_Grp_CD && WorkgData.length > 0) {
      const selectedGroup = WorkgData.find(
        (item) => item.WorkG_CD === costListData?.S_Sl_Grp_CD
      );
      setDestinationName5(selectedGroup ? selectedGroup.WorkG_Abb : "");
    }
  }, [
    costListData?.S_St_Pd_Grp_CD,
    costListData?.S_Ed_Pd_Grp_CD,
    costListData?.S_No_Pd_Grp_CD1,
    costListData?.S_No_Pd_Grp_CD2,
    costListData?.S_Sl_Grp_CD,
    WorkgData,
  ]);
  useEffect(() => {
    if (costListData?.S_Customer_CD1 && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === costListData?.S_Customer_CD1
      );
      setSelectedCustomerAbb(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
    if (costListData?.S_Customer_CD2 && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === costListData?.S_Customer_CD2
      );
      setSelectedCustomerAbb2(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
    if (costListData?.S_Customer_CD3 && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === costListData?.S_Customer_CD3
      );
      setSelectedCustomerAbb3(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
    if (costListData?.S_No_Customer_CD && CustomerData.length > 0) {
      const selectedGroup = CustomerData.find(
        (item) => item.Customer_CD === costListData?.S_No_Customer_CD
      );
      setSelectedCustomerAbb4(selectedGroup ? selectedGroup.Customer_Abb : "");
    }
  }, [
    costListData?.S_Customer_CD1,
    costListData?.S_Customer_CD2,
    costListData?.S_Customer_CD3,
    costListData?.S_No_Customer_CD,
    CustomerData,
  ]);

  useEffect(() => {
    if (costListData?.S_Specific_CD1 && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === costListData?.S_Specific_CD1
      );
      setSpecificName(selectedGroup ? selectedGroup.Specific_Abb : "");
    }

    if (costListData?.S_Specific_CD2 && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === costListData?.S_Specific_CD2
      );
      setSpecificName2(selectedGroup ? selectedGroup.Specific_Abb : "");
    }

    if (costListData?.S_No_Specific_CD1 && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === costListData?.S_No_Specific_CD1
      );
      setSpecificName3(selectedGroup ? selectedGroup.Specific_Abb : "");
    }

    if (costListData?.S_No_Specific_CD2 && SpecificData.length > 0) {
      const selectedGroup = SpecificData.find(
        (item) => item.Specific_CD === costListData?.S_No_Specific_CD2
      );
      setSpecificName4(selectedGroup ? selectedGroup.Specific_Abb : "");
    }
  }, [
    costListData?.S_Specific_CD1,
    costListData?.S_Specific_CD2,
    costListData?.S_No_Specific_CD1,
    costListData?.S_No_Specific_CD2,
    SpecificData,
  ]);

  useEffect(() => {
    if (costListData?.S_Price_CD && PriceData.length > 0) {
      const selectedGroup = PriceData.find(
        (item) => item.Price_CD === costListData?.S_Price_CD
      );
      setPriceName(selectedGroup ? selectedGroup.Price_Symbol : "");
    }
  }, [costListData?.S_Price_CD, PriceData]);

  useEffect(() => {
    if (costListData?.S_Request1_CD && Request1Data.length > 0) {
      const selectedGroup = Request1Data.find(
        (item) => item.Request1_CD === costListData?.S_Request1_CD
      );

      setRequest1Name(selectedGroup ? selectedGroup.Request1_Abb : "");
    }
    if (costListData?.S_Request2_CD && Request2Data.length > 0) {
      const selectedGroup = Request2Data.find(
        (item) => item.Request2_CD === costListData?.S_Request2_CD
      );

      setRequest2Name(selectedGroup ? selectedGroup.Request2_Abb : "");
    }
    if (costListData?.S_Request3_CD && Request3Data.length > 0) {
      const selectedGroup = Request3Data.find(
        (item) => item.Request3_CD === costListData?.S_Request3_CD
      );

      setRequest3Name(selectedGroup ? selectedGroup.Request3_Abb : "");
    }
  }, [
    costListData?.S_Request1_CD,
    costListData?.S_Request2_CD,
    costListData?.S_Request3_CD,
    Request1Data,
    Request2Data,
    Request3Data,
  ]);
  useEffect(() => {
    if (costListData?.S_Item1_CD && Item1Data.length > 0) {
      const selectedGroup = Item1Data.find(
        (item) => item.Item1_CD === costListData?.S_Item1_CD
      );

      setItemName(selectedGroup ? selectedGroup.Item1_Abb : "");
    }
  }, [costListData?.S_Item1_CD, Item1Data]);

  useEffect(() => {
    if (costListData?.S_Coating_CD1 && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === costListData?.S_Coating_CD1
      );

      setCoatingName(selectedGroup ? selectedGroup.Coating_Symbol : "");
    }
    if (costListData?.S_Coating_CD2 && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === costListData?.S_Coating_CD2
      );

      setCoatingName2(selectedGroup ? selectedGroup.Coating_Symbol : "");
    }
    if (costListData?.S_Coating_CD3 && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === costListData?.S_Coating_CD3
      );

      setCoatingName3(selectedGroup ? selectedGroup.Coating_Symbol : "");
    }
    if (costListData?.S_No_Coating_CD && CoatingData.length > 0) {
      const selectedGroup = CoatingData.find(
        (item) => item.Coating_CD === costListData?.S_No_Coating_CD
      );

      setCoatingName4(selectedGroup ? selectedGroup.Coating_Symbol : "");
    }
  }, [
    costListData?.S_Coating_CD1,
    costListData?.S_Coating_CD2,
    costListData?.S_Coating_CD3,
    costListData?.S_No_Coating_CD,
    CoatingData,
  ]);

  // useEffect(() => {
  //   if (costListData) {
  //      // ใช้ข้อมูลจาก API ที่ได้รับconsole.log("ข้อมูล Cost List:", costListData);
  //   }
  // }, [costListData]);

  const Initial_Item = (flag) => {
    if (flag) {
      setFormState(initialFormState);
    }
  };

  const [checkboxGroupState, setCheckboxGroupState] = useState({
    Info_View: true,
    Pl_Color_Separate: true,
  });

  const handleCheckboxGroupChange = (event) => {
    const { id, checked } = event.target;
    setCheckboxGroupState((prevState) => ({
      ...prevState,
      [id]: checked,
    }));
  };

  const enableFields = (fieldNames) => {
    setFormState((prevState) => {
      const updatedFields = { ...prevState };
      fieldNames.forEach((field) => {
        if (updatedFields[field]) {
          updatedFields[field].enabled = true;
        }
      });
      return updatedFields;
    });
  };

  const disableFields = (fieldNames) => {
    setFormState((prevState) => {
      const updatedFields = { ...prevState };
      fieldNames.forEach((field) => {
        if (updatedFields[field]) {
          updatedFields[field].enabled = false;
        }
      });
      return updatedFields;
    });
  };

  // ฟังก์ชันสำหรับจัดการประเภทการค้นหา
  const Search_Type_AfterUpdate = (searchType) => {
    switch (searchType) {
      case "Simple":
        Initial_Item(true);
        disableFields([
          "S_Order_No",
          "S_NAV_Name",
          "S_NAV_Size",
          "S_Product_Size",
          "S_Customer_Draw",
          "S_Company_Draw",
          "S_Product_Draw",
          "S_Sl_Instructions",
          "S_Pd_Instructions",
          "S_Pd_Remark",
          "S_I_Remark",
          "S_Price_CD",
          "S_Price_Name",
          "S_Customer_Name1",
          "S_Customer_Name2",
          "S_Customer_Name3",
          "S_Od_No_of_Custom",
          "S_Request1_CD",
          "S_Request1_Name",
          "S_Request2_CD",
          "S_Request2_Name",
          "S_Request3_CD",
          "S_Request3_Name",
          "S_Material1",
          "S_Material2",
          "S_Item2_CD",
          "S_Item2_Name",
          "S_Item3_CD",
          "S_Item3_Name",
          "S_Item4_CD",
          "S_Item4_Name",
          "S_Od_Pending",
          "S_Temp_Shipment",
          "S_Unreceived",
          "S_Od_CAT1",
          "S_Od_CAT2",
          "S_Od_CAT3",
          "S_St_Delivery_CD",
          "S_Ed_Delivery_CD",
          "S_St_Schedule_CD",
          "S_Ed_Schedule_CD",
          "S_St_Target_CD",
          "S_Ed_Target_CD",
          "S_St_Request_Delivery",
          "S_Ed_Request_Delivery",
          "S_St_NAV_Delivery",
          "S_Ed_NAV_Delivery",
          "S_St_Confirm_Delivery",
          "S_Ed_Confirm_Delivery",
          "S_St_Pd_Received_Date",
          "S_Ed_Pd_Received_Date",
          "S_St_Pd_Complete_Date",
          "S_Ed_Pd_Complete_Date",
          "S_St_I_Complete_Date",
          "S_Ed_I_Complete_Date",
          "S_St_Shipment_Date",
          "S_Ed_Shipment_Date",
          "S_St_Calc_Date",
          "S_Ed_Calc_Date",
          "S_Parts_No",
          "S_Parts_Pending",
          "S_Parts_CAT1",
          "S_Parts_CAT2",
          "S_Parts_CAT3",
          "S_St_Parts_Delivery",
          "S_Ed_Parts_Delivery",
          "S_Parts_Material",
          "S_Parts_Instructions",
          "S_Parts_Remark",
        ]);
        break;

      case "Normal":
        Initial_Item(true);
        enableFields([
          "S_Order_No",
          "S_Pd_Instructions",
          "S_Pd_Remark",
          "S_Customer_Name1",
          "S_Customer_Name2",
          "S_Customer_Name3",
          "S_Request3_CD",
          "S_Request3_Name",
          "S_Od_Pending",
          "S_Temp_Shipment",
          "S_Unreceived",
          "S_Od_CAT1",
          "S_Od_CAT2",
          "S_Od_CAT3",
          "S_St_Target_CD",
          "S_Ed_Target_CD",
          "S_St_Request_Delivery",
          "S_Ed_Request_Delivery",
          "S_St_NAV_Delivery",
          "S_Ed_NAV_Delivery",
          "S_St_Confirm_Delivery",
          "S_Ed_Confirm_Delivery",
          "S_Parts_No",
          "S_Parts_Pending",
          "S_Parts_CAT1",
          "S_Parts_CAT2",
          "S_Parts_CAT3",
          "S_St_Parts_Delivery",
          "S_Ed_Parts_Delivery",
          "S_Parts_Instructions",
          "S_Parts_Remark",
        ]);
        disableFields([
          "S_NAV_Name",
          "S_NAV_Size",
          "S_Product_Size",
          "S_Customer_Draw",
          "S_Company_Draw",
          "S_Product_Draw",
          "S_Sl_Instructions",

          "S_I_Remark",
          "S_Price_CD",
          "S_Price_Name",

          "S_Od_No_of_Custom",
          "S_Request1_CD",
          "S_Request1_Name",
          "S_Request2_CD",
          "S_Request2_Name",

          "S_Material1",
          "S_Material2",
          "S_Item2_CD",
          "S_Item2_Name",
          "S_Item3_CD",
          "S_Item3_Name",
          "S_Item4_CD",
          "S_Item4_Name",

          "S_Unreceived",
          "S_Od_CAT1",
          "S_Od_CAT2",
          "S_Od_CAT3",
          "S_St_Delivery_CD",
          "S_Ed_Delivery_CD",
          "S_St_Schedule_CD",
          "S_Ed_Schedule_CD",

          "S_St_Pd_Received_Date",
          "S_Ed_Pd_Received_Date",
          "S_St_Pd_Complete_Date",
          "S_Ed_Pd_Complete_Date",
          "S_St_I_Complete_Date",
          "S_Ed_I_Complete_Date",
          "S_St_Shipment_Date",
          "S_Ed_Shipment_Date",
          "S_St_Calc_Date",
          "S_Ed_Calc_Date",

          "S_Parts_Material",
        ]);
        break;

      case "Detail":
        Initial_Item(true);
        enableFields([
          "S_Order_No",
          "S_NAV_Name",
          "S_NAV_Size",
          "S_Product_Size",
          "S_Customer_Draw",
          "S_Company_Draw",
          "S_Product_Draw",
          "S_Sl_Instructions",
          "S_Pd_Instructions",
          "S_Pd_Remark",
          "S_I_Remark",
          "S_Price_CD",
          "S_Price_Name",
          "S_Customer_Name1",
          "S_Customer_Name2",
          "S_Customer_Name3",
          "S_Od_No_of_Custom",
          "S_Request1_CD",
          "S_Request1_Name",
          "S_Request2_CD",
          "S_Request2_Name",
          "S_Request3_CD",
          "S_Request3_Name",
          "S_Material1",
          "S_Material2",
          "S_Item2_CD",
          "S_Item2_Name",
          "S_Item3_CD",
          "S_Item3_Name",
          "S_Item4_CD",
          "S_Item4_Name",
          "S_Od_Pending",
          "S_Temp_Shipment",
          "S_Unreceived",
          "S_Od_CAT1",
          "S_Od_CAT2",
          "S_Od_CAT3",
          "S_St_Delivery_CD",
          "S_Ed_Delivery_CD",
          "S_St_Schedule_CD",
          "S_Ed_Schedule_CD",
          "S_St_Target_CD",
          "S_Ed_Target_CD",
          "S_St_Request_Delivery",
          "S_Ed_Request_Delivery",
          "S_St_NAV_Delivery",
          "S_Ed_NAV_Delivery",
          "S_St_Confirm_Delivery",
          "S_Ed_Confirm_Delivery",
          "S_St_Pd_Received_Date",
          "S_Ed_Pd_Received_Date",
          "S_St_Pd_Complete_Date",
          "S_Ed_Pd_Complete_Date",
          "S_St_I_Complete_Date",
          "S_Ed_I_Complete_Date",
          "S_St_Shipment_Date",
          "S_Ed_Shipment_Date",
          "S_St_Calc_Date",
          "S_Ed_Calc_Date",
          "S_Parts_No",
          "S_Parts_Pending",
          "S_Parts_CAT1",
          "S_Parts_CAT2",
          "S_Parts_CAT3",
          "S_St_Parts_Delivery",
          "S_Ed_Parts_Delivery",
          "S_Parts_Material",
          "S_Parts_Instructions",
          "S_Parts_Remark",
        ]);
        break;

      default:
        break;
    }
  };

  const [selectedSearchType, setSelectedSearchType] = useState("Simple");
  const handleSearchTypeChange = (event) => {
    const selectedType = event.target.value;
    Search_Type_AfterUpdate(selectedType);
    setSelectedSearchType(selectedType);
  };

  const handleCostListInputChange = async (event) => {
    const { id, value, type, checked } = event.target;

    setCostListData((prevCostListData) => {
      let updatedData = {
        ...prevCostListData,
        [id]: type === "checkbox" ? checked : value === "" ? null : value,
        [id]: value === "" ? null : value,
      };

      // ถ้าเลือก S_St_Pd_Grp_CD ให้ตั้ง S_Ed_Pd_Grp_CD เท่ากับค่าเดียวกัน
      if (id === "S_St_Pd_Grp_CD") {
        updatedData.S_Ed_Pd_Grp_CD = value;

        // ถ้าค่า S_St_Pd_Grp_CD เป็น null ให้ตั้ง S_No_Pd_Grp_Abb เป็น null
        if (value === "" || value === null) {
          setDestinationName(null);
        }
      }

      // ถ้าเลือก S_Ed_Pd_Grp_CD เป็น null ให้ตั้ง S_Ed_Pd_Grp_Abb เป็น null
      if (id === "S_Ed_Pd_Grp_CD" && (value === "" || value === null)) {
        setDestinationName(null);
      }

      // ถ้าเลือก S_Od_Ctl_Person_CD ให้ตั้งค่า selectedSalesGrpAbb
      if (id === "S_Pl_Reg_Person_CD") {
        const selectedWorker = WorkerData.find(
          (item) => item.Worker_CD === value
        );

        // กำหนดข้อความที่จะแสดงใน select (แค่ Worker_CD ที่เลือก)
        setSelectedWorkerDisplay(
          selectedWorker ? selectedWorker.Worker_CD : ""
        );

        // อัปเดต Worker_Remark ใน costListData
        updatedData.Worker_Remark = selectedWorker
          ? selectedWorker.Worker_Remark
          : null;
      }

      return updatedData;
    });
  };

  const handleF2Click = () => {
    setShowDialog(true);
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setColumnsVisibility((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
  };
  const handleCheckAll = (event) => {
    const isChecked = event.target.checked;
    const updatedVisibility = Object.keys(columnsVisibility).reduce(
      (acc, column) => {
        acc[column] = isChecked;
        return acc;
      },
      {}
    );
    setColumnsVisibility(updatedVisibility);
  };

  const handleF3Click = async () => {
    try {
      const postData = {
        S_Order_No: costListData?.S_Order_No || null,
        S_NAV_Name: costListData?.S_NAV_Name || null,
        S_NAV_Size: costListData?.S_NAV_Size || null,
        S_Product_Size: costListData?.S_Product_Size || null,
        S_Customer_Draw: costListData?.S_Customer_Draw || null,
        S_Company_Draw: costListData?.S_Company_Draw || null,
        S_Product_Draw: costListData?.S_Product_Draw || null,
        S_Sl_Instructions: costListData?.S_Sl_Instructions || null,
        S_Pd_Instructions: costListData?.S_Pd_Instructions || null,
        S_Pd_Remark: costListData?.S_Pd_Remark || null,
        S_I_Remark: costListData?.S_I_Remark || null,
        S_Price_CD: costListData?.S_Price_CD || null,
        S_Customer_Name1: costListData?.S_Customer_Name1 || null,
        S_Customer_Name2: costListData?.S_Customer_Name2 || null,
        S_Customer_Name3: costListData?.S_Customer_Name3 || null,
        S_Od_No_of_Custom: costListData?.S_Od_No_of_Custom || null,
        S_Request1_CD: costListData?.S_Request1_CD || null,
        S_Request2_CD: costListData?.S_Request2_CD || null,
        S_Request3_CD: costListData?.S_Request3_CD || null,
        S_Material1: costListData?.S_Material1 || null,
        S_Material2: costListData?.S_Material2 || null,
        S_Item1_CD: costListData?.S_Item1_CD || null,
        S_Item2_CD: costListData?.S_Item2_CD || null,
        S_Item3_CD: costListData?.S_Item3_CD || null,
        S_Item4_CD: costListData?.S_Item4_CD || null,
        S_Od_Pending: costListData?.S_Od_Pending || null,
        S_Temp_Shipment: costListData?.S_Temp_Shipment || null,
        S_Unreceived: costListData?.S_Unreceived || null,
        S_Od_CAT1: costListData?.S_Od_CAT1 || null,
        S_Od_CAT2: costListData?.S_Od_CAT2 || null,
        S_Od_CAT3: costListData?.S_Od_CAT3 || null,
        S_St_Delivery_CD: costListData?.S_St_Delivery_CD || null,
        S_Ed_Delivery_CD: costListData?.S_Ed_Delivery_CD || null,
        S_St_Schedule_CD: costListData?.S_St_Schedule_CD || null,
        S_Ed_Schedule_CD: costListData?.S_Ed_Schedule_CD || null,
        S_St_Target_CD: costListData?.S_St_Target_CD || null,
        S_Ed_Target_CD: costListData?.S_Ed_Target_CD || null,
        S_St_Request_Delivery: costListData?.S_St_Request_Delivery || null,
        S_Ed_Request_Delivery: costListData?.S_Ed_Request_Delivery || null,
        S_St_NAV_Delivery: costListData?.S_St_NAV_Delivery || null,
        S_Ed_NAV_Delivery: costListData?.S_Ed_NAV_Delivery || null,
        S_St_Confirm_Delivery: costListData?.S_St_Confirm_Delivery || null,
        S_Ed_Confirm_Delivery: costListData?.S_Ed_Confirm_Delivery || null,
        S_St_Pd_Received_Date: costListData?.S_St_Pd_Received_Date || null,
        S_Ed_Pd_Received_Date: costListData?.S_Ed_Pd_Received_Date || null,
        S_St_Pd_Complete_Date: costListData?.S_St_Pd_Complete_Date || null,
        S_Ed_Pd_Complete_Date: costListData?.S_Ed_Pd_Complete_Date || null,
        S_St_I_Complete_Date: costListData?.S_St_I_Complete_Date || null,
        S_Ed_I_Complete_Date: costListData?.S_Ed_I_Complete_Date || null,
        S_St_Shipment_Date: costListData?.S_St_Shipment_Date || null,
        S_Ed_Shipment_Date: costListData?.S_Ed_Shipment_Date || null,
        S_St_Calc_Date: costListData?.S_St_Calc_Date || null,
        S_Ed_Calc_Date: costListData?.S_Ed_Calc_Date || null,
        S_Parts_No: costListData?.S_Parts_No || null,
        S_Parts_Pending: costListData?.S_Parts_Pending || null,
        S_Parts_CAT1: costListData?.S_Parts_CAT1 || null,
        S_Parts_CAT2: costListData?.S_Parts_CAT2 || null,
        S_Parts_CAT3: costListData?.S_Parts_CAT3 || null,
        S_St_Parts_Delivery: costListData?.S_St_Parts_Delivery || null,
        S_Ed_Parts_Delivery: costListData?.S_Ed_Parts_Delivery || null,
        S_Parts_Material: costListData?.S_Parts_Material || null,
        S_Parts_Instructions: costListData?.S_Parts_Instructions || null,
        S_Parts_Remark: costListData?.S_Parts_Remark || null,
        S_Parts_Information: costListData?.S_Parts_Information || null,
        S_St_Pl_Progress_CD: costListData?.S_St_Pl_Progress_CD || null,
        S_Ed_Pl_Progress_CD: costListData?.S_Ed_Pl_Progress_CD || null,
        S_Pl_Reg_Person_CD: costListData?.S_Pl_Reg_Person_CD || null,
        Plan_Target: costListData?.Plan_Target || null,
        S_St_Pd_Grp_CD: costListData?.S_St_Pd_Grp_CD || null,
        S_Ed_Pd_Grp_CD: costListData?.S_Ed_Pd_Grp_CD || null,
        S_No_Pd_Grp_CD1: costListData?.S_No_Pd_Grp_CD1 || null,
        S_No_Pd_Grp_CD2: costListData?.S_No_Pd_Grp_CD2 || null,
        S_Customer_CD1: costListData?.S_Customer_CD1 || null,
        S_Customer_CD2: costListData?.S_Customer_CD2 || null,
        S_Customer_CD3: costListData?.S_Customer_CD3 || null,
        S_No_Customer_CD: costListData?.S_No_Customer_CD || null,
        S_Product_Name: costListData?.S_Product_Name || null,
        S_Sl_Grp_CD: costListData?.S_Sl_Grp_CD || null,
        S_Sl_Person_CD: costListData?.S_Sl_Person_CD || null,
        S_Coating_CD1: costListData?.S_Coating_CD1 || null,
        S_Coating_CD2: costListData?.S_Coating_CD2 || null,
        S_Coating_CD3: costListData?.S_Coating_CD3 || null,
        S_No_Coating_CD: costListData?.S_No_Coating_CD || null,
        S_Specific_CD1: costListData?.S_Specific_CD1 || null,
        S_Specific_CD2: costListData?.S_Specific_CD2 || null,
        S_No_Specific_CD1: costListData?.S_No_Specific_CD1 || null,
        S_No_Specific_CD2: costListData?.S_No_Specific_CD2 || null,
        S_St_Od_Progress_CD: costListData?.S_St_Od_Progress_CD || null,
        S_Ed_Od_Progress_CD: costListData?.S_Ed_Od_Progress_CD || null,
        S_Od_Ctl_Person_CD: costListData?.S_Od_Ctl_Person_CD || null,
        S_St_Product_Delivery: costListData?.S_St_Product_Delivery || null,
        S_Ed_Product_Delivery: costListData?.S_Ed_Product_Delivery || null,
        S_St_Complete_Date: costListData?.S_St_Complete_Date || null,
        S_Ed_Complete_Date: costListData?.S_Ed_Complete_Date || null,
        S_St_Process_Date: costListData?.S_St_Process_Date || null,
        S_Ed_Process_Date: costListData?.S_Ed_Process_Date || null,
      };
      // เรียกใช้งาน API และส่งข้อมูลไปที่ Backend
      const apiResponse = await axios.post(
        `${apiUrl_4000}/costlist/costlist-detail`,
        postData
      );

      if (apiResponse.status === 200 && apiResponse.data.status === "success") {
        const costData = apiResponse.data.data.cost;

        // console.log("Filtered Cost Data:", costData);

        if (Array.isArray(costData) && costData.length === 0) {
          Swal.fire({
            title: "No Data Found",
            text: "There is no data to generate the report.",
            icon: "warning",
            confirmButtonText: "OK",
          });
          setFilteredCostlistData([]);
          setIsTableVisible(false);
          setButtonState((prev) => ({ ...prev, F2: false }));
          return;
        }

        if (Array.isArray(costData)) {
          setFilteredCostlistData(costData);
          setIsTableVisible(costData.length > 0);
          setButtonState((prevState) => ({
            ...prevState,
            F2: costData.length > 0, // เปิด F2 ถ้ามีข้อมูล
          }));
        } else {
          Swal.fire({
            icon: "warning",
            title: "ไม่พบข้อมูล",
            text: "ไม่พบข้อมูลตามเงื่อนไขที่ระบุ",
          });
          setFilteredCostlistData([]);
          setIsTableVisible(false);
          setButtonState((prevState) => ({
            ...prevState,
            F2: false,
          }));
        }
      } else {
        Swal.fire({
          icon: "error",
          title: "เกิดข้อผิดพลาด",
          text: "ไม่สามารถโหลดข้อมูลได้",
        });
        setFilteredCostlistData([]);
        setIsTableVisible(false);
        setButtonState((prevState) => ({
          ...prevState,
          F2: false,
        }));
      }
    } catch (error) {
      console.error("Error handling F3 click:", error);
      setFilteredCostlistData([]);
      setIsTableVisible(false);
      setButtonState((prevState) => ({
        ...prevState,
        F2: false,
      }));
    }
  };

  const handleF7Click = async () => {
    const S_Order_No = costListData?.S_Order_No?.toLowerCase() || null;
    try {
      const postData = {
        selectedSearchType: selectedSearchType || null,
        delivery1: delivery1 || null,
        delivery2: delivery2 || null,
        delivery3: delivery3 || null,
        viewSchedule: viewSchedule || null,
        format: format || null,
        changePage: changePage || null,
        target: target || null,
        markDays: markDays || new Date().toISOString().split("T")[0],
        checkboxGroupState: checkboxGroupState || null,
        S_Order_No: S_Order_No,
        S_NAV_Name: costListData?.S_NAV_Name || null,
        S_NAV_Size: costListData?.S_NAV_Size || null,
        S_Product_Size: costListData?.S_Product_Size || null,
        S_Customer_Draw: costListData?.S_Customer_Draw || null,
        S_Company_Draw: costListData?.S_Company_Draw || null,
        S_Product_Draw: costListData?.S_Product_Draw || null,
        S_Sl_Instructions: costListData?.S_Sl_Instructions || null,
        S_Pd_Instructions: costListData?.S_Pd_Instructions || null,
        S_Pd_Remark: costListData?.S_Pd_Remark || null,
        S_I_Remark: costListData?.S_I_Remark || null,
        S_Price_CD: costListData?.S_Price_CD || null,
        S_Customer_Name1: costListData?.S_Customer_Name1 || null,
        S_Customer_Name2: costListData?.S_Customer_Name2 || null,
        S_Customer_Name3: costListData?.S_Customer_Name3 || null,
        S_Od_No_of_Custom: costListData?.S_Od_No_of_Custom || null,
        S_Request1_CD: costListData?.S_Request1_CD || null,
        S_Request2_CD: costListData?.S_Request2_CD || null,
        S_Request3_CD: costListData?.S_Request3_CD || null,
        S_Material1: costListData?.S_Material1 || null,
        S_Material2: costListData?.S_Material2 || null,
        S_Item1_CD: costListData?.S_Item1_CD || null,
        S_Item2_CD: costListData?.S_Item2_CD || null,
        S_Item3_CD: costListData?.S_Item3_CD || null,
        S_Item4_CD: costListData?.S_Item4_CD || null,
        S_Od_Pending: costListData?.S_Od_Pending || null,
        S_Temp_Shipment: costListData?.S_Temp_Shipment || null,
        S_Unreceived: costListData?.S_Unreceived || null,
        S_Od_CAT1: costListData?.S_Od_CAT1 || null,
        S_Od_CAT2: costListData?.S_Od_CAT2 || null,
        S_Od_CAT3: costListData?.S_Od_CAT3 || null,
        S_St_Delivery_CD: costListData?.S_St_Delivery_CD || null,
        S_Ed_Delivery_CD: costListData?.S_Ed_Delivery_CD || null,
        S_St_Schedule_CD: costListData?.S_St_Schedule_CD || null,
        S_Ed_Schedule_CD: costListData?.S_Ed_Schedule_CD || null,
        S_St_Target_CD: costListData?.S_St_Target_CD || null,
        S_Ed_Target_CD: costListData?.S_Ed_Target_CD || null,
        S_St_Request_Delivery: costListData?.S_St_Request_Delivery || null,
        S_Ed_Request_Delivery: costListData?.S_Ed_Request_Delivery || null,
        S_St_NAV_Delivery: costListData?.S_St_NAV_Delivery || null,
        S_Ed_NAV_Delivery: costListData?.S_Ed_NAV_Delivery || null,
        S_St_Confirm_Delivery: costListData?.S_St_Confirm_Delivery || null,
        S_Ed_Confirm_Delivery: costListData?.S_Ed_Confirm_Delivery || null,
        S_St_Pd_Received_Date: costListData?.S_St_Pd_Received_Date || null,
        S_Ed_Pd_Received_Date: costListData?.S_Ed_Pd_Received_Date || null,
        S_St_Pd_Complete_Date: costListData?.S_St_Pd_Complete_Date || null,
        S_Ed_Pd_Complete_Date: costListData?.S_Ed_Pd_Complete_Date || null,
        S_St_I_Complete_Date: costListData?.S_St_I_Complete_Date || null,
        S_Ed_I_Complete_Date: costListData?.S_Ed_I_Complete_Date || null,
        S_St_Shipment_Date: costListData?.S_St_Shipment_Date || null,
        S_Ed_Shipment_Date: costListData?.S_Ed_Shipment_Date || null,
        S_St_Calc_Date: costListData?.S_St_Calc_Date || null,
        S_Ed_Calc_Date: costListData?.S_Ed_Calc_Date || null,
        S_Parts_No: costListData?.S_Parts_No || null,
        S_Parts_Pending: costListData?.S_Parts_Pending || null,
        S_Parts_CAT1: costListData?.S_Parts_CAT1 || null,
        S_Parts_CAT2: costListData?.S_Parts_CAT2 || null,
        S_Parts_CAT3: costListData?.S_Parts_CAT3 || null,
        S_St_Parts_Delivery: costListData?.S_St_Parts_Delivery || null,
        S_Ed_Parts_Delivery: costListData?.S_Ed_Parts_Delivery || null,
        S_Parts_Material: costListData?.S_Parts_Material || null,
        S_Parts_Instructions: costListData?.S_Parts_Instructions || null,
        S_Parts_Remark: costListData?.S_Parts_Remark || null,
        S_Parts_Information: costListData?.S_Parts_Information || null,
        S_St_Pl_Progress_CD:
          costListData?.S_St_Pl_Progress_CD != null
            ? String(costListData.S_St_Pl_Progress_CD)
            : null,
        S_Ed_Pl_Progress_CD:
          costListData?.S_Ed_Pl_Progress_CD != null
            ? String(costListData.S_Ed_Pl_Progress_CD)
            : null,
        S_Pl_Reg_Person_CD: costListData?.S_Pl_Reg_Person_CD || null,
        Plan_Target: costListData?.Plan_Target || null,
        S_St_Pd_Grp_CD: costListData?.S_St_Pd_Grp_CD || null,
        S_Ed_Pd_Grp_CD: costListData?.S_Ed_Pd_Grp_CD || null,
        S_No_Pd_Grp_CD1: costListData?.S_No_Pd_Grp_CD1 || null,
        S_No_Pd_Grp_CD2: costListData?.S_No_Pd_Grp_CD2 || null,
        S_Customer_CD1: costListData?.S_Customer_CD1 || null,
        S_Customer_CD2: costListData?.S_Customer_CD2 || null,
        S_Customer_CD3: costListData?.S_Customer_CD3 || null,
        S_No_Customer_CD: costListData?.S_No_Customer_CD || null,
        S_Product_Name: costListData?.S_Product_Name || null,
        S_Sl_Grp_CD: costListData?.S_Sl_Grp_CD || null,
        S_Sl_Person_CD: costListData?.S_Sl_Person_CD || null,
        S_Coating_CD1: costListData?.S_Coating_CD1 || null,
        S_Coating_CD2: costListData?.S_Coating_CD2 || null,
        S_Coating_CD3: costListData?.S_Coating_CD3 || null,
        S_No_Coating_CD: costListData?.S_No_Coating_CD || null,
        S_Specific_CD1: costListData?.S_Specific_CD1 || null,
        S_Specific_CD2: costListData?.S_Specific_CD2 || null,
        S_No_Specific_CD1: costListData?.S_No_Specific_CD1 || null,
        S_No_Specific_CD2: costListData?.S_No_Specific_CD2 || null,
        S_St_Od_Progress_CD:
          costListData?.S_St_Od_Progress_CD != null
            ? String(costListData.S_St_Od_Progress_CD)
            : null,
        S_Ed_Od_Progress_CD:
          costListData?.S_Ed_Od_Progress_CD != null
            ? String(costListData.S_Ed_Od_Progress_CD)
            : null,
        S_Od_Ctl_Person_CD: costListData?.S_Od_Ctl_Person_CD || null,
        S_St_Product_Delivery: costListData?.S_St_Product_Delivery || null,
        S_Ed_Product_Delivery: costListData?.S_Ed_Product_Delivery || null,
        S_St_Complete_Date: costListData?.S_St_Complete_Date || null,
        S_Ed_Complete_Date: costListData?.S_Ed_Complete_Date || null,
        S_St_Process_Date: costListData?.S_St_Process_Date || null,
        S_Ed_Process_Date: costListData?.S_Ed_Process_Date || null,
      };

      // console.log("Payload to be sent to the server:", postData);

      // ส่งข้อมูลไปที่ API
      const apiResponse = await axios.post(
        `${apiUrl_4000}/planlist/report-planlist`,
        postData
      );

      if (apiResponse.status === 200 && apiResponse.data.status === "success") {
        // console.log("Information received from the server:", (apiResponse.data));
      } else {
        console.error("Failed to get valid data:", apiResponse.status);
      }

      const responseData = apiResponse.data.data;

      if (Array.isArray(responseData) && responseData.length === 0) {
        Swal.fire({
          title: "No Data Found",
          text: "There is no data to generate the report.",
          icon: "warning",
          confirmButtonText: "OK",
        });
        return;
      }

      const win = window.open(
        `${apiUrl_5173}/reports/RD_Pl_Pg_None`,
        "_blank",
        `width=800,height=600`
      );

      win.onload = () => {
        win.postMessage(
          {
            status: apiResponse.status,
            data: apiResponse.data,
            selectedSearchType: selectedSearchType || null,
            delivery1: delivery1 || null,
            delivery2: delivery2 || null,
            delivery3: delivery3 || null,
            viewSchedule: viewSchedule || null,
            Plan_Target: costListData?.Plan_Target || null,
            format: format || null,
            changePage: changePage || null,
            target: target || null,
            markDays: markDays || new Date().toISOString().split("T")[0],
            checkboxGroupState: checkboxGroupState || null,
            S_St_Pd_Grp_CD: costListData?.S_St_Pd_Grp_CD || null,
            S_St_Pd_Grp_Abb: destinationName || null,
            S_Ed_Pd_Grp_CD: costListData?.S_Ed_Pd_Grp_CD || null,
            S_Ed_Pd_Grp_Abb: destinationName2 || null,
            S_No_Pd_Grp_CD1: costListData?.S_No_Pd_Grp_CD1 || null,
            S_No_Pd_Grp_CD2: costListData?.S_No_Pd_Grp_CD2 || null,
            S_No_Pd_Grp_Abb1: destinationName3 || null,
            S_No_Pd_Grp_Abb2: destinationName4 || null,
            S_Coating_CD1: costListData?.S_Coating_CD1 || null,
            S_Coating_CD2: costListData?.S_Coating_CD2 || null,
            S_Coating_CD3: costListData?.S_Coating_CD3 || null,
            Coating_Name1: coatingName || null,
            Coating_Name2: coatingName2 || null,
            Coating_Name3: coatingName3 || null,
            S_No_Coating_CD: costListData?.S_No_Coating_CD || null,
            S_No_Coating_Name: coatingName4 || null,
            S_Customer_CD1: costListData?.S_Customer_CD1 || null,
            S_Customer_CD2: costListData?.S_Customer_CD2 || null,
            S_Customer_CD3: costListData?.S_Customer_CD3 || null,
            S_No_Customer_CD: costListData?.S_No_Customer_CD || null,
            S_Customer_Name1: costListData?.S_Customer_Name1 || null,
            S_Customer_Name2: costListData?.S_Customer_Name2 || null,
            S_Customer_Name3: costListData?.S_Customer_Name3 || null,
            S_Customer_Abb1: selectedCustomerAbb || null,
            S_Customer_Abb2: selectedCustomerAbb2 || null,
            S_Customer_Abb3: selectedCustomerAbb3 || null,
            S_No_Customer_Abb: selectedCustomerAbb4 || null,
            S_Item1_CD: costListData?.S_Item1_CD || null,
            S_Item1_Name: itemName || null,
            S_Product_Name: costListData?.S_Product_Name || null,
            S_Sl_Person_CD: costListData?.S_Sl_Person_CD || null,
            S_Sl_Person_Name: selectedSalesGrpAbb2 || null,
            S_Od_Ctl_Person_CD: costListData?.S_Od_Ctl_Person_CD || null,
            S_Od_Ctl_Person_Name: selectedSalesGrpAbb || null,
            S_Pl_Reg_Person_CD: costListData?.S_Pl_Reg_Person_CD || null,
          },
          "*"
        );
      };
    } catch (error) {
      console.error("Error posting data:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again later.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  //****************F8 POP UP WINDOW VERSION****************//
  const handleF8Click = async () => {
    // เปิดหน้าต่างใหม่
    // ดึงข้อมูลจาก API
    try {
      const postData = {
        S_Order_No: costListData?.S_Order_No || null,
        S_NAV_Name: costListData?.S_NAV_Name || null,
        S_NAV_Size: costListData?.S_NAV_Size || null,
        S_Product_Size: costListData?.S_Product_Size || null,
        S_Customer_Draw: costListData?.S_Customer_Draw || null,
        S_Company_Draw: costListData?.S_Company_Draw || null,
        S_Product_Draw: costListData?.S_Product_Draw || null,
        S_Sl_Instructions: costListData?.S_Sl_Instructions || null,
        S_Pd_Instructions: costListData?.S_Pd_Instructions || null,
        S_Pd_Remark: costListData?.S_Pd_Remark || null,
        S_I_Remark: costListData?.S_I_Remark || null,
        S_Price_CD: costListData?.S_Price_CD || null,
        S_Customer_Name1: costListData?.S_Customer_Name1 || null,
        S_Customer_Name2: costListData?.S_Customer_Name2 || null,
        S_Customer_Name3: costListData?.S_Customer_Name3 || null,
        S_Od_No_of_Custom: costListData?.S_Od_No_of_Custom || null,
        S_Request1_CD: costListData?.S_Request1_CD || null,
        S_Request2_CD: costListData?.S_Request2_CD || null,
        S_Request3_CD: costListData?.S_Request3_CD || null,
        S_Material1: costListData?.S_Material1 || null,
        S_Material2: costListData?.S_Material2 || null,
        S_Item1_CD: costListData?.S_Item1_CD || null,
        S_Item2_CD: costListData?.S_Item2_CD || null,
        S_Item3_CD: costListData?.S_Item3_CD || null,
        S_Item4_CD: costListData?.S_Item4_CD || null,
        S_Od_Pending: costListData?.S_Od_Pending || null,
        S_Temp_Shipment: costListData?.S_Temp_Shipment || null,
        S_Unreceived: costListData?.S_Unreceived || null,
        S_Od_CAT1: costListData?.S_Od_CAT1 || null,
        S_Od_CAT2: costListData?.S_Od_CAT2 || null,
        S_Od_CAT3: costListData?.S_Od_CAT3 || null,
        S_St_Delivery_CD: costListData?.S_St_Delivery_CD || null,
        S_Ed_Delivery_CD: costListData?.S_Ed_Delivery_CD || null,
        S_St_Schedule_CD: costListData?.S_St_Schedule_CD || null,
        S_Ed_Schedule_CD: costListData?.S_Ed_Schedule_CD || null,
        S_St_Target_CD: costListData?.S_St_Target_CD || null,
        S_Ed_Target_CD: costListData?.S_Ed_Target_CD || null,
        S_St_Request_Delivery: costListData?.S_St_Request_Delivery || null,
        S_Ed_Request_Delivery: costListData?.S_Ed_Request_Delivery || null,
        S_St_NAV_Delivery: costListData?.S_St_NAV_Delivery || null,
        S_Ed_NAV_Delivery: costListData?.S_Ed_NAV_Delivery || null,
        S_St_Confirm_Delivery: costListData?.S_St_Confirm_Delivery || null,
        S_Ed_Confirm_Delivery: costListData?.S_Ed_Confirm_Delivery || null,
        S_St_Pd_Received_Date: costListData?.S_St_Pd_Received_Date || null,
        S_Ed_Pd_Received_Date: costListData?.S_Ed_Pd_Received_Date || null,
        S_St_Pd_Complete_Date: costListData?.S_St_Pd_Complete_Date || null,
        S_Ed_Pd_Complete_Date: costListData?.S_Ed_Pd_Complete_Date || null,
        S_St_I_Complete_Date: costListData?.S_St_I_Complete_Date || null,
        S_Ed_I_Complete_Date: costListData?.S_Ed_I_Complete_Date || null,
        S_St_Shipment_Date: costListData?.S_St_Shipment_Date || null,
        S_Ed_Shipment_Date: costListData?.S_Ed_Shipment_Date || null,
        S_St_Calc_Date: costListData?.S_St_Calc_Date || null,
        S_Ed_Calc_Date: costListData?.S_Ed_Calc_Date || null,
        S_Parts_No: costListData?.S_Parts_No || null,
        S_Parts_Pending: costListData?.S_Parts_Pending || null,
        S_Parts_CAT1: costListData?.S_Parts_CAT1 || null,
        S_Parts_CAT2: costListData?.S_Parts_CAT2 || null,
        S_Parts_CAT3: costListData?.S_Parts_CAT3 || null,
        S_St_Parts_Delivery: costListData?.S_St_Parts_Delivery || null,
        S_Ed_Parts_Delivery: costListData?.S_Ed_Parts_Delivery || null,
        S_Parts_Material: costListData?.S_Parts_Material || null,
        S_Parts_Instructions: costListData?.S_Parts_Instructions || null,
        S_Parts_Remark: costListData?.S_Parts_Remark || null,
        S_Parts_Information: costListData?.S_Parts_Information || null,
        S_St_Pl_Progress_CD: costListData?.S_St_Pl_Progress_CD || null,
        S_Ed_Pl_Progress_CD: costListData?.S_Ed_Pl_Progress_CD || null,
        S_Pl_Reg_Person_CD: costListData?.S_Pl_Reg_Person_CD || null,
        Plan_Target: costListData?.Plan_Target || null,
        S_St_Pd_Grp_CD: costListData?.S_St_Pd_Grp_CD || null,
        S_Ed_Pd_Grp_CD: costListData?.S_Ed_Pd_Grp_CD || null,
        S_No_Pd_Grp_CD1: costListData?.S_No_Pd_Grp_CD1 || null,
        S_No_Pd_Grp_CD2: costListData?.S_No_Pd_Grp_CD2 || null,
        S_Customer_CD1: costListData?.S_Customer_CD1 || null,
        S_Customer_CD2: costListData?.S_Customer_CD2 || null,
        S_Customer_CD3: costListData?.S_Customer_CD3 || null,
        S_No_Customer_CD: costListData?.S_No_Customer_CD || null,
        S_Product_Name: costListData?.S_Product_Name || null,
        S_Sl_Grp_CD: costListData?.S_Sl_Grp_CD || null,
        S_Sl_Person_CD: costListData?.S_Sl_Person_CD || null,
        S_Coating_CD1: costListData?.S_Coating_CD1 || null,
        S_Coating_CD2: costListData?.S_Coating_CD2 || null,
        S_Coating_CD3: costListData?.S_Coating_CD3 || null,
        S_No_Coating_CD: costListData?.S_No_Coating_CD || null,
        S_Specific_CD1: costListData?.S_Specific_CD1 || null,
        S_Specific_CD2: costListData?.S_Specific_CD2 || null,
        S_No_Specific_CD1: costListData?.S_No_Specific_CD1 || null,
        S_No_Specific_CD2: costListData?.S_No_Specific_CD2 || null,
        S_St_Od_Progress_CD: costListData?.S_St_Od_Progress_CD || null,
        S_Ed_Od_Progress_CD: costListData?.S_Ed_Od_Progress_CD || null,
        S_Od_Ctl_Person_CD: costListData?.S_Od_Ctl_Person_CD || null,
        S_St_Product_Delivery: costListData?.S_St_Product_Delivery || null,
        S_Ed_Product_Delivery: costListData?.S_Ed_Product_Delivery || null,
        S_St_Complete_Date: costListData?.S_St_Complete_Date || null,
        S_Ed_Complete_Date: costListData?.S_Ed_Complete_Date || null,
        S_St_Process_Date: costListData?.S_St_Process_Date || null,
        S_Ed_Process_Date: costListData?.S_Ed_Process_Date || null,
      };
      const apiResponse = await axios.post(
        `${apiUrl_4000}/costlist/costlist-detail`,
        postData
      );

      if (apiResponse.status === 200 && apiResponse.data.status === "success") {
        const costData = apiResponse.data.data.cost;

        if (Array.isArray(costData)) {
          if (costData.length === 0) {
            Swal.fire({
              title: "No Data Found",
              text: "There is no data to generate the report.",
              icon: "warning",
              confirmButtonText: "OK",
            });
            return;
          }

          // ✅ เปิดหน้ารายงาน
          const win = window.open(
            `${apiUrl_5173}/qr_cs_list`,
            "_blank",
            `width=800,height=600`
          );
          win.onload = () => {
            win.postMessage(
              { status: apiResponse.status, data: costData },
              "*"
            );
          };
        } else {
          console.error("Received data.cost is not an array.");
          Swal.fire({
            title: "ผิดพลาด",
            text: "ข้อมูลต้นทุนไม่อยู่ในรูปแบบที่คาดไว้",
            icon: "error",
            confirmButtonText: "ตกลง",
          });
        }
      } else {
        Swal.fire({
          title: "ไม่สามารถดึงข้อมูลได้",
          text: "กรุณาลองใหม่อีกครั้ง",
          icon: "error",
          confirmButtonText: "ตกลง",
        });
      }
    } catch (error) {
      console.error("Error posting data:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again later.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF9Click = async () => {
    try {
      const postData = {
        S_Order_No: costListData?.S_Order_No || null,
        S_NAV_Name: costListData?.S_NAV_Name || null,
        S_NAV_Size: costListData?.S_NAV_Size || null,
        S_Product_Size: costListData?.S_Product_Size || null,
        S_Customer_Draw: costListData?.S_Customer_Draw || null,
        S_Company_Draw: costListData?.S_Company_Draw || null,
        S_Product_Draw: costListData?.S_Product_Draw || null,
        S_Sl_Instructions: costListData?.S_Sl_Instructions || null,
        S_Pd_Instructions: costListData?.S_Pd_Instructions || null,
        S_Pd_Remark: costListData?.S_Pd_Remark || null,
        S_I_Remark: costListData?.S_I_Remark || null,
        S_Price_CD: costListData?.S_Price_CD || null,
        S_Customer_Name1: costListData?.S_Customer_Name1 || null,
        S_Customer_Name2: costListData?.S_Customer_Name2 || null,
        S_Customer_Name3: costListData?.S_Customer_Name3 || null,
        S_Od_No_of_Custom: costListData?.S_Od_No_of_Custom || null,
        S_Request1_CD: costListData?.S_Request1_CD || null,
        S_Request2_CD: costListData?.S_Request2_CD || null,
        S_Request3_CD: costListData?.S_Request3_CD || null,
        S_Material1: costListData?.S_Material1 || null,
        S_Material2: costListData?.S_Material2 || null,
        S_Item1_CD: costListData?.S_Item1_CD || null,
        S_Item2_CD: costListData?.S_Item2_CD || null,
        S_Item3_CD: costListData?.S_Item3_CD || null,
        S_Item4_CD: costListData?.S_Item4_CD || null,
        S_Od_Pending: costListData?.S_Od_Pending || null,
        S_Temp_Shipment: costListData?.S_Temp_Shipment || null,
        S_Unreceived: costListData?.S_Unreceived || null,
        S_Od_CAT1: costListData?.S_Od_CAT1 || null,
        S_Od_CAT2: costListData?.S_Od_CAT2 || null,
        S_Od_CAT3: costListData?.S_Od_CAT3 || null,
        S_St_Delivery_CD: costListData?.S_St_Delivery_CD || null,
        S_Ed_Delivery_CD: costListData?.S_Ed_Delivery_CD || null,
        S_St_Schedule_CD: costListData?.S_St_Schedule_CD || null,
        S_Ed_Schedule_CD: costListData?.S_Ed_Schedule_CD || null,
        S_St_Target_CD: costListData?.S_St_Target_CD || null,
        S_Ed_Target_CD: costListData?.S_Ed_Target_CD || null,
        S_St_Request_Delivery: costListData?.S_St_Request_Delivery || null,
        S_Ed_Request_Delivery: costListData?.S_Ed_Request_Delivery || null,
        S_St_NAV_Delivery: costListData?.S_St_NAV_Delivery || null,
        S_Ed_NAV_Delivery: costListData?.S_Ed_NAV_Delivery || null,
        S_St_Confirm_Delivery: costListData?.S_St_Confirm_Delivery || null,
        S_Ed_Confirm_Delivery: costListData?.S_Ed_Confirm_Delivery || null,
        S_St_Pd_Received_Date: costListData?.S_St_Pd_Received_Date || null,
        S_Ed_Pd_Received_Date: costListData?.S_Ed_Pd_Received_Date || null,
        S_St_Pd_Complete_Date: costListData?.S_St_Pd_Complete_Date || null,
        S_Ed_Pd_Complete_Date: costListData?.S_Ed_Pd_Complete_Date || null,
        S_St_I_Complete_Date: costListData?.S_St_I_Complete_Date || null,
        S_Ed_I_Complete_Date: costListData?.S_Ed_I_Complete_Date || null,
        S_St_Shipment_Date: costListData?.S_St_Shipment_Date || null,
        S_Ed_Shipment_Date: costListData?.S_Ed_Shipment_Date || null,
        S_St_Calc_Date: costListData?.S_St_Calc_Date || null,
        S_Ed_Calc_Date: costListData?.S_Ed_Calc_Date || null,
        S_Parts_No: costListData?.S_Parts_No || null,
        S_Parts_Pending: costListData?.S_Parts_Pending || null,
        S_Parts_CAT1: costListData?.S_Parts_CAT1 || null,
        S_Parts_CAT2: costListData?.S_Parts_CAT2 || null,
        S_Parts_CAT3: costListData?.S_Parts_CAT3 || null,
        S_St_Parts_Delivery: costListData?.S_St_Parts_Delivery || null,
        S_Ed_Parts_Delivery: costListData?.S_Ed_Parts_Delivery || null,
        S_Parts_Material: costListData?.S_Parts_Material || null,
        S_Parts_Instructions: costListData?.S_Parts_Instructions || null,
        S_Parts_Remark: costListData?.S_Parts_Remark || null,
        S_Parts_Information: costListData?.S_Parts_Information || null,
        S_St_Pl_Progress_CD: costListData?.S_St_Pl_Progress_CD || null,
        S_Ed_Pl_Progress_CD: costListData?.S_Ed_Pl_Progress_CD || null,
        S_Pl_Reg_Person_CD: costListData?.S_Pl_Reg_Person_CD || null,
        Plan_Target: costListData?.Plan_Target || null,
        S_St_Pd_Grp_CD: costListData?.S_St_Pd_Grp_CD || null,
        S_Ed_Pd_Grp_CD: costListData?.S_Ed_Pd_Grp_CD || null,
        S_No_Pd_Grp_CD1: costListData?.S_No_Pd_Grp_CD1 || null,
        S_No_Pd_Grp_CD2: costListData?.S_No_Pd_Grp_CD2 || null,
        S_Customer_CD1: costListData?.S_Customer_CD1 || null,
        S_Customer_CD2: costListData?.S_Customer_CD2 || null,
        S_Customer_CD3: costListData?.S_Customer_CD3 || null,
        S_No_Customer_CD: costListData?.S_No_Customer_CD || null,
        S_Product_Name: costListData?.S_Product_Name || null,
        S_Sl_Grp_CD: costListData?.S_Sl_Grp_CD || null,
        S_Sl_Person_CD: costListData?.S_Sl_Person_CD || null,
        S_Coating_CD1: costListData?.S_Coating_CD1 || null,
        S_Coating_CD2: costListData?.S_Coating_CD2 || null,
        S_Coating_CD3: costListData?.S_Coating_CD3 || null,
        S_No_Coating_CD: costListData?.S_No_Coating_CD || null,
        S_Specific_CD1: costListData?.S_Specific_CD1 || null,
        S_Specific_CD2: costListData?.S_Specific_CD2 || null,
        S_No_Specific_CD1: costListData?.S_No_Specific_CD1 || null,
        S_No_Specific_CD2: costListData?.S_No_Specific_CD2 || null,
        S_St_Od_Progress_CD: costListData?.S_St_Od_Progress_CD || null,
        S_Ed_Od_Progress_CD: costListData?.S_Ed_Od_Progress_CD || null,
        S_Od_Ctl_Person_CD: costListData?.S_Od_Ctl_Person_CD || null,
        S_St_Product_Delivery: costListData?.S_St_Product_Delivery || null,
        S_Ed_Product_Delivery: costListData?.S_Ed_Product_Delivery || null,
        S_St_Complete_Date: costListData?.S_St_Complete_Date || null,
        S_Ed_Complete_Date: costListData?.S_Ed_Complete_Date || null,
        S_St_Process_Date: costListData?.S_St_Process_Date || null,
        S_Ed_Process_Date: costListData?.S_Ed_Process_Date || null,
      };
      const apiResponse = await axios.post(
        `${apiUrl_4000}/costlist/costlist-detail`,
        postData
      );

      if (apiResponse.status === 200 && apiResponse.data.status === "success") {
        const costData = apiResponse.data.data.cost;

        if (Array.isArray(costData)) {
          if (costData.length === 0) {
            Swal.fire({
              title: "No Data Found",
              text: "There is no data to generate the report.",
              icon: "warning",
              confirmButtonText: "OK",
            });
            return;
          }

          // ✅ สร้าง CSV
          const csvData = costData.map((row) => ({
            Order_No: row.Order_No,
            Parts_No: row.Parts_No,
            Process_No: row.Process_No,
            Process_Name: row.Process_Name,
            ProcessG_Name: row.ProcessG_Name,
            CostG_CD: row.CostG_CD,
            Machine_Cost_Time: row.Machine_Cost_Time,
            Person_Time: row.Person_Time,
            P_Coefficient: row.P_Coefficient,
            Person_Cost_Time: row.Person_Cost_Time,
            Cs_Complete_Date: row.Cs_Complete_Date
              ? new Date(row.Cs_Complete_Date).toISOString().split("T")[0]
              : "",
          }));

          const csv = Papa.unparse(csvData);
          const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.setAttribute("href", url);
          const today = new Date().toISOString().split("T")[0]; // ได้เป็น '2025-06-11'
          link.setAttribute("download", `QR_Cs_List_Detail_${today}.csv`);
          link.style.visibility = "hidden";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          console.error("Received data.cost is not an array.");
          Swal.fire({
            title: "ผิดพลาด",
            text: "ข้อมูลต้นทุนไม่อยู่ในรูปแบบที่คาดไว้",
            icon: "error",
            confirmButtonText: "ตกลง",
          });
        }
      } else {
        Swal.fire({
          title: "ไม่สามารถดึงข้อมูลได้",
          text: "กรุณาลองใหม่อีกครั้ง",
          icon: "error",
          confirmButtonText: "ตกลง",
        });
      }
    } catch (error) {
      console.error("Error in handleF9Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please contact the administrator.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF11Click = () => {
    window.location.reload();
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: "Do you want to close this window?<br>คุณต้องการปิดหน้าต่างนี้หรือไม่?<br>このウィンドウを閉じますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });
      if (confirmResult.isConfirmed) {
        navigate("/dashboard");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      }); // แจ้งเตือนผู้ใช้เกี่ยวกับข้อผิดพลาด
    }
  };

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-screen mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col overflow-x-hidden flex-grow p-2 bg-white mt-2 rounded-md">
          <div className="grid grid-cols-1">
            <div className="">
              <div className="flex justify-center py-4">
                <h1 className="text-xl font-bold">Cost List</h1>
              </div>
              <hr />
              <div className="container mx-auto px-4 overflow-x-auto ">
                <div className="flex flex-nowrap justify-between items-center gap-2 py-3">
                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[100px] text-sm font-bold">
                      Search_Type
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="Search_Type"
                        defaultValue="Simple"
                        onChange={handleSearchTypeChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full"
                      >
                        <option value="Simple">Simple</option>
                        <option value="Normal">Normal</option>
                        <option value="Detail">Detail</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[70px] text-sm font-bold">
                      Delivery1
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="Delivery1"
                        onChange={handleD1TypeChange}
                        defaultValue="Product"
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8 ps-2.5 text-md"
                      >
                        <option value="Request">Request</option>
                        <option value="NAV">NAV</option>
                        <option value="Confirm">Confirm</option>
                        <option value="Product">Product</option>
                        <option value="Parts">Parts</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[80px] text-sm font-bold">
                      Delivery2
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="Delivery2"
                        defaultValue="Comfirm"
                        onChange={handleD2TypeChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8 ps-2.5 text-md"
                      >
                        <option value="Request">Request</option>
                        <option value="NAV">NAV</option>
                        <option value="Confirm">Confirm</option>
                        <option value="Product">Product</option>
                        <option value="Parts">Parts</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[80px] text-sm font-bold">
                      Delivery3
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="Delivery3"
                        defaultValue="Request"
                        onChange={handleD3TypeChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8 ps-2.5 text-md"
                      >
                        <option value="Request">Request</option>
                        <option value="NAV">NAV</option>
                        <option value="Confirm">Confirm</option>
                        <option value="Product">Product</option>
                        <option value="Parts">Parts</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[120px] text-sm font-bold">
                      View_Schedule
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="View_Schedule"
                        onChange={handleViewSchedule}
                        defaultValue="Manual"
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8 ps-2.5 text-md"
                      >
                        <option value="Manual">Manual</option>
                        <option value="ASP">ASP</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-1 relative">
                    <label className="w-[100px] text-sm font-bold">
                      Plan_Target
                    </label>
                    <div className="relative w-full lg:w-60 xl:w-44">
                      <select
                        id="Plan_Target"
                        value={costListData?.Plan_Target || ""}
                        onChange={handleCostListInputChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8 ps-2.5 text-md"
                      >
                        <option value=""></option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <hr />

              <div className="overflow-x-auto w-full">
                <div className="min-w-[1650px] w-full mb-7">
                  <div className="w-full content-start ms-5 mt-4">
                    <label className="font-bold text-md">
                      Order_Info_Search
                    </label>
                  </div>
                  <br />
                  <div className="col-span-12 me-5 mt-5 ml-14 overflow-x-auto">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="col-span-9">
                        {/* Group 1 */}
                        <div className="gap-2 flex mb-4 justify-start me-5">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Format
                            </label>
                            <div className="w-24">
                              <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full">
                                <option value="Progress">Progress</option>
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <label className="w-24 font-medium text-sm ">
                              Change_Page
                            </label>
                            <div className="w-40">
                              <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full">
                                <option value="No_Change_Page">
                                  No_Change_Page
                                </option>
                                <option value="Product_Section">
                                  Product_Section
                                </option>
                                <option value="Specific_Item">
                                  Specific_Item
                                </option>
                                <option value="Section_SpecItem">
                                  Section_SpecItem
                                </option>
                              </select>
                            </div>
                          </div>
                          <div className="flex ml-4">
                            <label className="font-medium text-sm pr-3">
                              Target
                            </label>
                            <div className="w-32">
                              <select
                                onChange={handleTarget}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full"
                              >
                                <option value="Production">Production</option>
                                <option value="QC">QC</option>
                                <option value="Administrator">
                                  Administrator
                                </option>
                              </select>
                            </div>
                          </div>
                          <div className="flex ml-4">
                            <label className="font-medium text-sm pr-3">
                              Mark_Days
                            </label>
                            <div className="w-32 ">
                              <input
                                id="Mark_Days"
                                value={costListData?.Mark_Days || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className="h-6 bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 2 */}
                        <div className="gap-2 flex mb-4 items-center">
                          <div className="flex gap-2 w-48 mr-5">
                            <label className="w-24 font-medium text-sm">
                              Order_No
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Order_No.enabled}
                                id="S_Order_No"
                                value={costListData?.S_Order_No || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Order_No.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex bg-[#ffff99] gap-2">
                            <div className="flex gap-1 items-center">
                              <div className="w-auto flex me-1">
                                <input
                                  id="Info_View"
                                  value={checkboxGroupState?.Info_View || ""}
                                  onChange={handleCheckboxGroupChange}
                                  type="checkbox"
                                  className="w-6 h-6"
                                />
                              </div>
                              <label className="text-xs font-medium ">
                                Into_View
                              </label>
                            </div>
                            <div className="flex w-24 gap-1 items-center">
                              <div className="w-auto flex me-1">
                                <input
                                  id="Pl_Color_Separate"
                                  value={
                                    checkboxGroupState?.Pl_Color_Separate || ""
                                  }
                                  onChange={handleCheckboxGroupChange}
                                  type="checkbox"
                                  className="w-6 h-6"
                                />
                              </div>
                              <label className="text-xs font-medium ">
                                PI_Colo
                              </label>
                            </div>
                          </div>
                          <label className="w-30 font-medium text-sm pl-10">
                            Ctl_Person
                          </label>
                          <div className="w-24">
                            <select
                              disabled={!formState?.S_Od_Ctl_Person_CD}
                              id="S_Od_Ctl_Person_CD"
                              value={costListData?.S_Od_Ctl_Person_CD || ""}
                              onChange={handleCostListInputChange}
                              className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                            >
                              <option value={costListData?.S_Od_Ctl_Person_CD}>
                                {costListData?.S_Od_Ctl_Person_CD}
                              </option>
                              {Array.isArray(WorkerData) &&
                              WorkerData.length > 0 ? (
                                <>
                                  <option disabled>
                                    Worker_CD | Worker_Abb | Worker_Remark
                                  </option>
                                  {WorkerData.map((item, index) => (
                                    <option key={index} value={item.Worker_CD}>
                                      {item.Worker_CD} | {item.Worker_Abb} |{" "}
                                      {item.Worker_Remark}
                                    </option>
                                  ))}
                                </>
                              ) : (
                                <option value="">No information</option>
                              )}
                            </select>
                          </div>

                          <div className="w-24">
                            <input
                              disabled={!formState?.S_Od_Ctl_Person_Name}
                              type="text"
                              id="S_Od_Ctl_Person_Name"
                              value={selectedSalesGrpAbb || ""} // ใช้ || "" เพื่อให้เป็นช่องว่างหากเป็น null
                              onChange={handleCostListInputChange}
                              className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                            />
                          </div>
                        </div>
                        {/* Group 3 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              NAV_Name
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_NAV_Name.enabled}
                                id="S_NAV_Name"
                                value={costListData?.S_NAV_Name || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_NAV_Name.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-5">
                            <label className="w-auto font-medium text-sm">
                              Product_Grp
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_St_Pd_Grp_CD}
                                id="S_St_Pd_Grp_CD"
                                value={costListData?.S_St_Pd_Grp_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_St_Pd_Grp_CD}>
                                  {costListData?.S_St_Pd_Grp_CD}
                                </option>
                                {Array.isArray(WorkgData) &&
                                WorkgData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      WorkG_CD | WorkG_Abb | WorkG_Name |
                                      WorkG_Remark
                                    </option>
                                    {WorkgData.map((item, index) => (
                                      <option key={index} value={item.WorkG_CD}>
                                        {item.WorkG_CD} | {item.WorkG_Abb} |{" "}
                                        {item.WorkG_Name} | {item.WorkG_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24 ">
                              <input
                                disabled={!formState.S_No_Pd_Grp_Abb1}
                                id="S_No_Pd_Grp_Abb"
                                value={destinationName || ""}
                                onChange={(event) => setWorkgData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>

                            <label className="w-auto font-medium text-sm">
                              ~
                            </label>

                            <div className="w-24">
                              <select
                                disabled={!formState.S_Ed_Pd_Grp_CD}
                                id="S_Ed_Pd_Grp_CD"
                                value={costListData?.S_Ed_Pd_Grp_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_St_Pd_Grp_CD}>
                                  {costListData?.S_St_Pd_Grp_CD}
                                </option>
                                {Array.isArray(WorkgData) &&
                                WorkgData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      WorkG_CD | WorkG_Abb | WorkG_Name |
                                      WorkG_Remark
                                    </option>
                                    {WorkgData.map((item, index) => (
                                      <option key={index} value={item.WorkG_CD}>
                                        {item.WorkG_CD} | {item.WorkG_Abb} |{" "}
                                        {item.WorkG_Name} | {item.WorkG_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Ed_Pd_Grp_Abb}
                                id="S_Ed_Pd_Grp_Abb"
                                value={destinationName || ""}
                                onChange={(event) => setWorkgData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>

                          <div className="flex justify-between w-auto gap-2 ms-5">
                            <label className="w-20 font-medium text-sm">
                              Sales_grp
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Sl_Grp_CD"
                                value={costListData?.S_Sl_Grp_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Sl_Grp_CD}>
                                  {costListData?.S_Sl_Grp_CD}
                                </option>
                                {Array.isArray(WorkgData) &&
                                WorkgData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      WorkG_CD | WorkG_Abb | WorkG_Name |
                                      WorkG_Remark
                                    </option>
                                    {WorkgData.map((item, index) => (
                                      <option key={index} value={item.WorkG_CD}>
                                        {item.WorkG_CD} | {item.WorkG_Abb} |{" "}
                                        {item.WorkG_Name} | {item.WorkG_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Sl_Grp_Name}
                                id="S_Sl_Grp_Name"
                                value={destinationName5 || ""}
                                onChange={(event) => setWorkgData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 4 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Product_Name
                            </label>
                            <div className="w-24">
                              <input
                                id="S_Product_Name"
                                value={costListData?.S_Product_Name || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className="h-6 bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-5">
                            <label className="w-auto font-medium text-sm">
                              Not_Pd_Grp1
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Pd_Grp_CD1"
                                value={costListData?.S_No_Pd_Grp_CD1 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Pd_Grp_CD1?.not}
                                >
                                  {costListData?.S_No_Pd_Grp_CD1?.not}
                                </option>
                                {Array.isArray(WorkgData) &&
                                WorkgData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      WorkG_CD | WorkG_Abb | WorkG_Name |
                                      WorkG_Remark
                                    </option>
                                    {WorkgData.map((item, index) => (
                                      <option key={index} value={item.WorkG_CD}>
                                        {item.WorkG_CD} | {item.WorkG_Abb} |{" "}
                                        {item.WorkG_Name} | {item.WorkG_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.noPdGrpAbb1}
                                id="S_No_Pd_Grp_Abb1"
                                value={destinationName3}
                                onChange={(event) => setWorkgData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full mr-4"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-5">
                            <label className=" font-medium text-sm">
                              Price_CAT
                            </label>
                            <div className="w-16">
                              <select
                                disabled={!formState.S_Price_CD.enabled}
                                id="S_Price_CD"
                                value={costListData?.S_Price_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Price_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value={costListData?.S_Price_CD}>
                                  {costListData?.S_Price_CD}
                                </option>
                                {Array.isArray(PriceData) &&
                                PriceData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Price_CD | Price_Symbol | Price_Remark
                                    </option>
                                    {PriceData.map((item, index) => (
                                      <option key={index} value={item.Price_CD}>
                                        {item.Price_CD} | {item.Price_Symbol} |{" "}
                                        {item.Price_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-16">
                              <input
                                disabled={!formState.S_Price_Name.enabled}
                                id="S_Price_Name"
                                value={PriceName || ""}
                                onChange={(event) => setPriceData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between w-auto gap-2 pl-[10px]">
                            <label className="w-20 font-medium text-sm">
                              Sales_Person
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Sl_Person_CD"
                                value={costListData?.S_Sl_Person_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Sl_Person_CD}>
                                  {costListData?.S_Sl_Person_CD}
                                </option>
                                {Array.isArray(WorkerData) &&
                                WorkerData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Worker_CD | Worker_Abb | Worker_Remark
                                    </option>
                                    {WorkerData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Worker_CD}
                                      >
                                        {item.Worker_CD} | {item.Worker_Abb} |{" "}
                                        {item.Worker_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.slPersonName}
                                id="S_Sl_Person_Name"
                                value={selectedSalesGrpAbb2 || ""}
                                onChange={(event) => setWorkerData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 5 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              NAV_Size
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_NAV_Size.enabled}
                                id="S_NAV_Size"
                                value={costListData?.S_NAV_Size || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_NAV_Size.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-5">
                            <label className="w-auto font-medium text-sm">
                              Not_Pd_Grp2
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Pd_Grp_CD2"
                                value={costListData?.S_No_Pd_Grp_CD2 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Pd_Grp_CD2?.not}
                                >
                                  {costListData?.S_No_Pd_Grp_CD2?.not}
                                </option>
                                {Array.isArray(WorkgData) &&
                                WorkgData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      WorkG_CD | WorkG_Abb | WorkG_Name |
                                      WorkG_Remark
                                    </option>
                                    {WorkgData.map((item, index) => (
                                      <option key={index} value={item.WorkG_CD}>
                                        {item.WorkG_CD} | {item.WorkG_Abb} |{" "}
                                        {item.WorkG_Name} | {item.WorkG_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_No_Pd_Grp_Abb2}
                                id="S_No_Pd_Grp_Abb2"
                                value={destinationName4}
                                onChange={(event) => setWorkgData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex justify-between w-auto gap-2 mr-3">
                            <label className="w-auto font-medium text-sm">
                              Request_CAT
                            </label>
                            <div className="w-16">
                              <select
                                disabled={!formState.S_Request1_CD.enabled}
                                id="S_Request1_CD"
                                value={costListData?.S_Request1_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Request1_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value={costListData?.S_Request1_CD}>
                                  {costListData?.S_Request1_CD}
                                </option>
                                {Array.isArray(Request1Data) &&
                                Request1Data.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Request1_CD | Request1_Abb |
                                      Request1_Remark
                                    </option>
                                    {Request1Data.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Request1_CD}
                                      >
                                        {item.Request1_CD} | {item.Request1_Abb}{" "}
                                        | {item.Request1_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-16">
                              <input
                                disabled={!formState.request1Name}
                                id="S_Request1_Name"
                                value={request1Name}
                                onChange={(event) => setRequest1Data(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <div className="w-16">
                              <select
                                disabled={!formState.S_Request2_CD.enabled}
                                id="S_Request2_CD"
                                value={costListData?.S_Request2_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Request2_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(Request2Data) &&
                                Request2Data.length > 0 ? (
                                  Request2Data.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Request2_CD}
                                    >
                                      {item.Request2_CD}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-16">
                              <input
                                disabled={!formState.S_Request2_Name}
                                id="S_Request2_Name"
                                value={request2Name}
                                onChange={(event) => setRequest2Data(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <div className="w-16">
                              <select
                                disabled={!formState.S_Request3_CD.enabled}
                                id="S_Request3_CD"
                                value={costListData?.S_Request3_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Request3_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(Request3Data) &&
                                Request3Data.length > 0 ? (
                                  Request3Data.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Request3_CD}
                                    >
                                      {item.Request3_CD}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-16">
                              <input
                                disabled={!formState.S_Request3_Name}
                                id="S_Request3_Name"
                                value={request3Name}
                                onChange={(event) => setRequest3Data(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 6 */}
                        <div className="gap-2 flex mb-4 justify-between me-5">
                          <div className="w-1/2 flex gap-2">
                            <div className="flex gap-2 w-48">
                              <label className="w-24 font-medium text-sm">
                                Product_Size
                              </label>
                              <div className="w-24">
                                <input
                                  disabled={!formState.S_Product_Size.enabled}
                                  id="S_Product_Size"
                                  value={costListData?.S_Product_Size || ""}
                                  onChange={handleCostListInputChange}
                                  type="text"
                                  className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                    formState.S_Product_Size.enabled
                                      ? "bg-[#ccffff] border-gray-500"
                                      : "bg-white border-gray-500"
                                  }`}
                                />
                              </div>
                            </div>
                            <div className="flex pl-9">
                              <div className="flex gap-2 w-full">
                                <label className="w-auto font-medium text-sm">
                                  Customer1
                                </label>
                                <div className="w-24">
                                  <select
                                    id="S_Customer_CD1"
                                    value={costListData?.S_Customer_CD1 || ""}
                                    onChange={handleCostListInputChange}
                                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                                  >
                                    <option
                                      value={costListData?.S_Customer_CD1}
                                    >
                                      {costListData?.S_Customer_CD1}
                                    </option>
                                    {Array.isArray(CustomerData) &&
                                    CustomerData.length > 0 ? (
                                      <>
                                        <option disabled>
                                          Customer_CD | Customer_Abb |
                                          Customer_Name | Customer_Remark
                                        </option>
                                        {CustomerData.map((item, index) => (
                                          <option
                                            key={index}
                                            value={item.Customer_CD}
                                          >
                                            {item.Customer_CD} |{" "}
                                            {item.Customer_Abb} |{" "}
                                            {item.Customer_Name} |{" "}
                                            {item.Customer_Remark}
                                          </option>
                                        ))}
                                      </>
                                    ) : (
                                      <option value="">No information</option>
                                    )}
                                  </select>
                                </div>
                                <div className="w-24">
                                  <input
                                    disabled={!formState.S_Customer_Abb1}
                                    id="S_Customer_Abb1"
                                    value={selectedCustomerAbb || ""}
                                    onChange={(event) => setCustomerData(event)}
                                    type="text"
                                    className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                                  />
                                </div>
                              </div>
                            </div>

                            <div className="flex">
                              <div className="flex gap-2">
                                <label className="font-semibold text-sm ml-4">
                                  Customer1
                                </label>
                                <input
                                  type="text"
                                  className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                                />
                              </div>

                              <div className="flex pl-5">
                                <label className="font-bold text-xs mr-2">
                                  Mate1
                                </label>
                                <div className="w-24">
                                  <input
                                    disabled={!formState.S_Material1.enabled}
                                    id="S_Material1"
                                    value={costListData?.S_Material1 || ""}
                                    onChange={handleCostListInputChange}
                                    type="text"
                                    className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                      formState.S_Material1.enabled
                                        ? "bg-[#ccffff] border-gray-500"
                                        : "bg-white border-gray-500"
                                    }`}
                                  />
                                </div>
                              </div>

                              <div className="flex">
                                <label className="ml-4 font-medium text-sm mr-2">
                                  Mate2
                                </label>
                                <div className="w-24">
                                  <input
                                    disabled={!formState.S_Material2.enabled}
                                    id="S_Material2"
                                    value={costListData?.S_Material2 || ""}
                                    onChange={handleCostListInputChange}
                                    type="text"
                                    className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                      formState.S_Material2.enabled
                                        ? "bg-[#ccffff] border-gray-500"
                                        : "bg-white border-gray-500"
                                    }`}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        {/* Group 7 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Cus_Draw_No
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Customer_Draw.enabled}
                                id="S_Customer_Draw"
                                value={costListData?.S_Customer_Draw || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Customer_Draw.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-8">
                            <label className="w-auto font-medium text-sm">
                              Customer2
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Customer_CD2}
                                id="S_Customer_CD2"
                                value={costListData?.S_Customer_CD2 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Customer_CD2}>
                                  {costListData?.S_Customer_CD2}
                                </option>
                                {Array.isArray(CustomerData) &&
                                CustomerData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Customer_CD | Customer_Abb | Customer_Name
                                      | Customer_Remark
                                    </option>
                                    {CustomerData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Customer_CD}
                                      >
                                        {item.Customer_CD} | {item.Customer_Abb}{" "}
                                        | {item.Customer_Name} |{" "}
                                        {item.Customer_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Customer_Abb2}
                                id="S_Customer_Abb2"
                                value={selectedCustomerAbb2 || ""}
                                onChange={(event) => setCustomerData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="font-semibold text-sm ml-4">
                              Customer2
                            </label>
                            <input
                              type="text"
                              className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                            />
                          </div>
                          <div className="flex gap-2 pl-3">
                            <label className="w-auto font-medium text-sm">
                              Item1
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Item1_CD}
                                id="S_Item1_CD"
                                value={costListData?.S_Item1_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Item1_CD}>
                                  {costListData?.S_Item1_CD}
                                </option>
                                {Array.isArray(Item1Data) &&
                                Item1Data.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Item1_CD | Item1_Abb | Item1_Remark
                                    </option>
                                    {Item1Data.map((item, index) => (
                                      <option key={index} value={item.Item1_CD}>
                                        {item.Item1_CD} | {item.Item1_Abb} |{" "}
                                        {item.Item1_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Item1_Name}
                                id="S_Item1_Name"
                                value={itemName || ""}
                                onChange={(event) => setItem1Data(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 8 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Com_Draw_No
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Company_Draw.enabled}
                                id="S_Customer_Draw"
                                value={costListData?.S_Company_Draw || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Company_Draw.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-8">
                            <label className="w-auto font-medium text-sm">
                              Customer3
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Customer_CD3"
                                value={costListData?.S_Customer_CD3 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Customer_CD3}>
                                  {costListData?.S_Customer_CD3}
                                </option>
                                {Array.isArray(CustomerData) &&
                                CustomerData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Customer_CD | Customer_Abb | Customer_Name
                                      | Customer_Remark
                                    </option>
                                    {CustomerData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Customer_CD}
                                      >
                                        {item.Customer_CD} | {item.Customer_Abb}{" "}
                                        | {item.Customer_Name} |{" "}
                                        {item.Customer_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.customerAbb3}
                                id="S_Customer_Abb3"
                                value={selectedCustomerAbb3 || ""}
                                onChange={(event) => setCustomerData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="font-semibold text-sm ml-4">
                              Customer2
                            </label>
                            <input
                              type="text"
                              className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                            />
                          </div>
                          <div className="flex gap-2 pl-3">
                            <label className="w-auto font-medium text-sm">
                              Item2
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Item2_CD.enabled}
                                id="S_Item2_CD"
                                value={costListData?.S_Item2_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Item2_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">none</option>
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 9 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Pd_Draw_No
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Product_Draw.enabled}
                                id="S_Product_Draw"
                                value={costListData?.S_Product_Draw || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Product_Draw.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-2">
                            <label className="w-auto font-medium text-sm">
                              Not_Customer
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Customer_CD"
                                value={costListData?.S_No_Customer_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Customer_CD?.not}
                                >
                                  {costListData?.S_No_Customer_CD?.not}
                                </option>
                                {Array.isArray(CustomerData) &&
                                CustomerData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Customer_CD | Customer_Abb | Customer_Name
                                      | Customer_Remark
                                    </option>
                                    {CustomerData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Customer_CD}
                                      >
                                        {item.Customer_CD} | {item.Customer_Abb}{" "}
                                        | {item.Customer_Name} |{" "}
                                        {item.Customer_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.noCustomerAbb}
                                id="S_No_Customer_Abb"
                                value={selectedCustomerAbb4 || ""}
                                onChange={(event) => setCustomerData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <label className="w-auto font-medium text-sm">
                              Not_Customer
                            </label>
                            <div className="w-24">
                              <input
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-3">
                            <label className="w-auto font-medium text-sm">
                              Item3
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Item3_CD.enabled}
                                id="S_Item3_CD"
                                value={costListData?.S_Item3_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Item3_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 10 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Sales_Note
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Sl_Instructions.enabled}
                                id="S_Sl_Instructions"
                                value={costListData?.S_Sl_Instructions || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Sl_Instructions.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-11">
                            <label className="w-auto font-medium text-sm">
                              Specific1
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Specific_CD1"
                                value={costListData?.S_Specific_CD1 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Specific_CD1}>
                                  {costListData?.S_Specific_CD1}
                                </option>
                                {Array.isArray(SpecificData) &&
                                SpecificData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Specific_CD | Specific_Abb |
                                      Specific_Remark
                                    </option>
                                    {SpecificData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Specific_CD}
                                      >
                                        {item.Specific_CD} | {item.Specific_Abb}{" "}
                                        | {item.Specific_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.specificName1}
                                id="S_Specific_Name1"
                                value={SpecificName || ""}
                                onChange={(event) => setSpecificData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Coating1
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Coating_CD1"
                                value={costListData?.S_Coating_CD1 || ""}
                                onChange={handleCostListInputChange}
                                className="h-6 border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Coating_CD1}>
                                  {costListData?.S_Coating_CD1}
                                </option>
                                {Array.isArray(CoatingData) &&
                                CoatingData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Coating_CD | Coating_Symbol |
                                      Coating_Remark
                                    </option>
                                    {CoatingData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Coating_CD}
                                      >
                                        {item.Coating_CD} |{" "}
                                        {item.Coating_Symbol} |{" "}
                                        {item.Coating_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.coatingName1}
                                id="S_Coating_Name1"
                                value={coatingName || ""}
                                onChange={(event) => setCoatingData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Item4
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Item4_CD.enabled}
                                id="S_Item4_CD"
                                value={costListData?.S_Item4_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Item4_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 11 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Pd_Note
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Pd_Instructions.enabled}
                                id="S_Pd_Instructions"
                                value={costListData?.S_Pd_Instructions || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Pd_Instructions.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-11">
                            <label className="w-auto font-medium text-sm">
                              Specific2
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Specific_CD2"
                                value={costListData?.S_Specific_CD2 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Specific_CD2}>
                                  {costListData?.S_Specific_CD2}
                                </option>
                                {Array.isArray(SpecificData) &&
                                SpecificData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Specific_CD | Specific_Abb |
                                      Specific_Remark
                                    </option>
                                    {SpecificData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Specific_CD}
                                      >
                                        {item.Specific_CD} | {item.Specific_Abb}{" "}
                                        | {item.Specific_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.specificName2}
                                id="S_Specific_Name2"
                                value={SpecificName2 || ""}
                                onChange={(event) => setSpecificData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Coating2
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Coating_CD2"
                                value={costListData?.S_Coating_CD2 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Coating_CD2}>
                                  {costListData?.S_Coating_CD2}
                                </option>
                                {Array.isArray(CoatingData) &&
                                CoatingData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Coating_CD | Coating_Symbol |
                                      Coating_Remark
                                    </option>
                                    {CoatingData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Coating_CD}
                                      >
                                        {item.Coating_CD} |{" "}
                                        {item.Coating_Symbol} |{" "}
                                        {item.Coating_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.coatingName2}
                                id="S_Coating_Name1"
                                value={coatingName2 || ""}
                                onChange={(event) => setCoatingData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Od_Pend
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Od_Pending.enabled}
                                id="S_Od_Pending"
                                value={costListData?.S_Od_Pending || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Od_Pending.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Od_CAT1
                            </label>
                            <div className="w-24 ml-1">
                              <select
                                disabled={!formState.S_Od_CAT1.enabled}
                                id="S_Od_CAT1"
                                value={costListData?.S_Od_CAT1 || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Od_CAT1.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 12 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              Pd_Remark
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_Pd_Remark.enabled}
                                id="S_Pd_Remark"
                                value={costListData?.S_Pd_Remark || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Pd_Remark.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-4">
                            <label className="w-auto font-medium text-sm">
                              Not_Specific1
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Specific_CD1"
                                value={costListData?.S_No_Specific_CD1 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Specific_CD1?.not}
                                >
                                  {costListData?.S_No_Specific_CD1?.not}
                                </option>
                                {Array.isArray(SpecificData) &&
                                SpecificData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Specific_CD | Specific_Abb |
                                      Specific_Remark
                                    </option>
                                    {SpecificData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Specific_CD}
                                      >
                                        {item.Specific_CD} | {item.Specific_Abb}{" "}
                                        | {item.Specific_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.noSpecificName1}
                                id="S_No_Specific_Name1"
                                value={SpecificName3 || ""}
                                onChange={(event) => setSpecificData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Coating3
                            </label>
                            <div className="w-24">
                              <select
                                id="S_Coating_CD3"
                                value={costListData?.S_Coating_CD3 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                              >
                                <option value={costListData?.S_Coating_CD3}>
                                  {costListData?.S_Coating_CD3}
                                </option>
                                {Array.isArray(CoatingData) &&
                                CoatingData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Coating_CD | Coating_Symbol |
                                      Coating_Remark
                                    </option>
                                    {CoatingData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Coating_CD}
                                      >
                                        {item.Coating_CD} |{" "}
                                        {item.Coating_Symbol} |{" "}
                                        {item.Coating_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.coatingName3}
                                id="S_Coating_Name3"
                                value={coatingName3 || ""}
                                onChange={(event) => setCoatingData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <label className="w-auto font-medium text-sm">
                              TempShip
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Temp_Shipment.enabled}
                                id="S_Temp_Shipment"
                                value={costListData?.S_Temp_Shipment || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Temp_Shipment.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Od_CAT2
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Od_CAT2.enabled}
                                id="S_Od_CAT2"
                                value={costListData?.S_Od_CAT2 || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Od_CAT2.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 13 */}
                        <div className="gap-2 flex mb-4">
                          <div className="flex gap-2 w-48">
                            <label className="w-24 font-medium text-sm">
                              QC_Remark
                            </label>
                            <div className="w-24">
                              <input
                                disabled={!formState.S_I_Remark.enabled}
                                id="S_I_Remark"
                                value={costListData?.S_I_Remark || ""}
                                onChange={handleCostListInputChange}
                                type="text"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_I_Remark.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-3">
                            <label className="w-auto font-medium text-sm">
                              Not_Specific2
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Specific_CD2"
                                value={costListData?.S_No_Specific_CD2 || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Specific_CD2?.not}
                                >
                                  {costListData?.S_No_Specific_CD2?.not}
                                </option>
                                {Array.isArray(SpecificData) &&
                                SpecificData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Specific_CD | Specific_Abb |
                                      Specific_Remark
                                    </option>
                                    {SpecificData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Specific_CD}
                                      >
                                        {item.Specific_CD} | {item.Specific_Abb}{" "}
                                        | {item.Specific_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.noSpecificName2}
                                id="S_No_Specific_Name2"
                                value={SpecificName4 || ""}
                                onChange={(event) => setSpecificData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Not_Coat
                            </label>
                            <div className="w-24">
                              <select
                                id="S_No_Coating_CD"
                                value={costListData?.S_No_Coating_CD || ""}
                                onChange={handleCostListInputChange}
                                className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                              >
                                <option
                                  value={costListData?.S_No_Coating_CD?.not}
                                >
                                  {costListData?.S_No_Coating_CD?.not}
                                </option>
                                {Array.isArray(CoatingData) &&
                                CoatingData.length > 0 ? (
                                  <>
                                    <option disabled>
                                      Coating_CD | Coating_Symbol |
                                      Coating_Remark
                                    </option>
                                    {CoatingData.map((item, index) => (
                                      <option
                                        key={index}
                                        value={item.Coating_CD}
                                      >
                                        {item.Coating_CD} |{" "}
                                        {item.Coating_Symbol} |{" "}
                                        {item.Coating_Remark}
                                      </option>
                                    ))}
                                  </>
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                disabled={!formState.noCoatingName}
                                id="S_No_Coating_Name"
                                value={coatingName4 || ""}
                                onChange={(event) => setCoatingData(event)}
                                type="text"
                                className="h-6 bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2 pl-1">
                            <label className="w-auto font-medium text-sm">
                              Unrecive
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Unreceived.enabled}
                                id="S_Unreceived"
                                value={costListData?.S_Unreceived || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Unreceived.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <label className="w-auto font-medium text-sm">
                              Od_CAT3
                            </label>
                            <div className="w-24">
                              <select
                                disabled={!formState.S_Od_CAT3.enabled}
                                id="S_Od_CAT3"
                                value={costListData?.S_Od_CAT3 || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Od_CAT3.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-span-3">
                        {/* Group 1 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Order_Progress
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                id="S_St_Od_Progress_CD"
                                value={costListData?.S_St_Od_Progress_CD || ""}
                                onChange={handleCostListInputChange}
                                className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-40"
                              >
                                <option value=""></option>
                                {Array.isArray(OdProgressData) &&
                                OdProgressData.length > 0 ? (
                                  OdProgressData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Od_Progress_CD}
                                    >
                                      {item.Od_Progress_Symbol}{" "}
                                      {item.Od_Progress_Remark}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                id="S_Ed_Od_Progress_CD"
                                value={costListData?.S_Ed_Od_Progress_CD || ""}
                                onChange={handleCostListInputChange}
                                className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-40"
                              >
                                <option value=""></option>
                                {Array.isArray(OdProgressData) &&
                                OdProgressData.length > 0 ? (
                                  OdProgressData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Od_Progress_CD}
                                    >
                                      {item.Od_Progress_Symbol}{" "}
                                      {item.Od_Progress_Remark}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 2 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Delivery_CAT
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_St_Delivery_CD.enabled}
                                id="S_St_Delivery_CD"
                                value={costListData?.S_St_Delivery_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_St_Delivery_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(DeliveryData) &&
                                DeliveryData.length > 0 ? (
                                  DeliveryData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Delivery_CD}
                                    >
                                      {item.Delivery_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_Ed_Delivery_CD.enabled}
                                id="S_Ed_Delivery_CD"
                                value={costListData?.S_Ed_Delivery_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Ed_Delivery_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(DeliveryData) &&
                                DeliveryData.length > 0 ? (
                                  DeliveryData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Delivery_CD}
                                    >
                                      {item.Delivery_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 3 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Schedule_CAT
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_St_Schedule_CD.enabled}
                                id="S_St_Schedule_CD"
                                value={costListData?.S_St_Schedule_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_St_Schedule_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(scheduleData) &&
                                scheduleData.length > 0 ? (
                                  scheduleData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Schedule_CD}
                                    >
                                      {item.Schedule_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_Ed_Schedule_CD.enabled}
                                id="S_Ed_Schedule_CD"
                                value={costListData?.S_Ed_Schedule_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Ed_Schedule_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(scheduleData) &&
                                scheduleData.length > 0 ? (
                                  scheduleData.map((item, index) => (
                                    <option
                                      key={index}
                                      value={item.Schedule_CD}
                                    >
                                      {item.Schedule_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 4 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Target_CAT
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_St_Target_CD.enabled}
                                id="S_St_Target_CD"
                                value={costListData?.S_St_Target_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_St_Target_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(TargetData) &&
                                TargetData.length > 0 ? (
                                  TargetData.map((item, index) => (
                                    <option key={index} value={item.Target_CD}>
                                      {item.Target_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <select
                                disabled={!formState.S_Ed_Target_CD.enabled}
                                id="S_Ed_Target_CD"
                                value={costListData?.S_Ed_Target_CD || ""}
                                onChange={handleCostListInputChange}
                                className={`border-gray-500 border-solid border-2 rounded-md  w-full ${
                                  formState.S_Ed_Target_CD.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              >
                                <option value=""></option>
                                {Array.isArray(TargetData) &&
                                TargetData.length > 0 ? (
                                  TargetData.map((item, index) => (
                                    <option key={index} value={item.Target_CD}>
                                      {item.Target_Symbol}
                                    </option>
                                  ))
                                ) : (
                                  <option value="">No information</option>
                                )}
                              </select>
                            </div>
                          </div>
                        </div>
                        {/* Group 5 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Request_Delivery
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_St_Request_Delivery.enabled
                                }
                                id="S_St_Request_Delivery"
                                value={
                                  costListData?.S_St_Request_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Request_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_Ed_Request_Delivery.enabled
                                }
                                id="S_Ed_Request_Delivery"
                                value={
                                  costListData?.S_Ed_Request_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Request_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 6 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Nav_Delivery
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_St_NAV_Delivery.enabled}
                                id="S_St_NAV_Delivery"
                                value={costListData?.S_St_NAV_Delivery || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_NAV_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_Ed_NAV_Delivery.enabled}
                                id="S_Ed_NAV_Delivery"
                                value={costListData?.S_Ed_NAV_Delivery || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_NAV_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 7 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Confirm_Delivery
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_St_Confirm_Delivery.enabled
                                }
                                id="S_St_Confirm_Delivery"
                                value={
                                  costListData?.S_St_Confirm_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Confirm_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_Ed_Confirm_Delivery.enabled
                                }
                                id="S_Ed_Confirm_Delivery"
                                value={
                                  costListData?.S_Ed_Confirm_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Confirm_Delivery.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 8 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Product_Delivery
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                id="S_St_Product_Delivery"
                                value={
                                  costListData?.S_St_Product_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className="h-6 bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                id="S_Ed_Product_Delivery"
                                value={
                                  costListData?.S_Ed_Product_Delivery || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className="h-6 bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 9 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Product_Received
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_St_Pd_Received_Date.enabled
                                }
                                id="S_St_Pd_Received_Date"
                                value={
                                  costListData?.S_St_Pd_Received_Date || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Pd_Received_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_Ed_Pd_Received_Date.enabled
                                }
                                id="S_Ed_Pd_Received_Date"
                                value={
                                  costListData?.S_Ed_Pd_Received_Date || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Pd_Received_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 10 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Product_Complete
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_St_Pd_Complete_Date.enabled
                                }
                                id="S_St_Pd_Complete_Date"
                                value={
                                  costListData?.S_St_Pd_Complete_Date || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Pd_Complete_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_Ed_Pd_Complete_Date.enabled
                                }
                                id="S_Ed_Pd_Complete_Date"
                                value={
                                  costListData?.S_Ed_Pd_Complete_Date || ""
                                }
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Pd_Complete_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 11 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              QC_Complete
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_St_I_Complete_Date.enabled
                                }
                                id="S_St_I_Complete_Date"
                                value={costListData?.S_St_I_Complete_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_I_Complete_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={
                                  !formState.S_Ed_I_Complete_Date.enabled
                                }
                                id="S_Ed_I_Complete_Date"
                                value={costListData?.S_Ed_I_Complete_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_I_Complete_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 12 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Shipment_Date
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_St_Shipment_Date.enabled}
                                id="S_St_Shipment_Date"
                                value={costListData?.S_St_Shipment_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Shipment_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_Ed_Shipment_Date.enabled}
                                id="S_Ed_Shipment_Date"
                                value={costListData?.S_Ed_Shipment_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Shipment_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                        {/* Group 13 */}
                        <div className="mb-4">
                          <div className="flex gap-2 justify-end">
                            <label className="w-28 font-bold text-xs">
                              Cale_Date
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_St_Calc_Date.enabled}
                                id="S_St_Calc_Date"
                                value={costListData?.S_St_Calc_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_St_Calc_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                            <label className="w-auto font-bold text-xs">
                              ~
                            </label>
                            <div className="relative w-40 lg:w-44">
                              <input
                                disabled={!formState.S_Ed_Calc_Date.enabled}
                                id="S_Ed_Calc_Date"
                                value={costListData?.S_Ed_Calc_Date || ""}
                                onChange={handleCostListInputChange}
                                type="date"
                                className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                                  formState.S_Ed_Calc_Date.enabled
                                    ? "bg-[#ccffff] border-gray-500"
                                    : "bg-white border-gray-500"
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <hr />

              {/* plan-info-search */}
              <div className="w-full content-start ms-5 mt-4">
                <label className="font-bold text-sm">Plan_Info_Search</label>
              </div>
              <div className="w-full mt-5 overflow-x-auto">
                <div className="min-w-[1400px] w-full mb-7 pl-10 pr-10">
                  {/* Group 1 */}
                  <div className="flex flex-warp justify-start gap-2 mb-2 items-center">
                    <div className="flex item-center gap-2">
                      <label className="w-auto font-bold text-xs mr-7">
                        Parts_No
                      </label>
                      <div className="items-center w-24">
                        <input
                          disabled={!formState.S_Parts_No.enabled}
                          id="S_Parts_No"
                          value={costListData?.S_Parts_No || ""}
                          onChange={handleCostListInputChange}
                          type="text"
                          className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                            formState.S_Parts_No.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2 pl-3">
                      <label className="font-bold text-xs">Parts_Pend</label>
                      <div className="items-center w-24">
                        <select
                          disabled={!formState.S_Parts_Pending.enabled}
                          id="S_Parts_Pending"
                          value={costListData?.S_Parts_Pending || ""}
                          onChange={handleCostListInputChange}
                          className={`border-gray-500 border-solid border-2 rounded-md w-full ${
                            formState.S_Parts_Pending.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        >
                          <option value=""></option>
                          <option value="true">Yes</option>
                          <option value="false">No</option>
                        </select>
                      </div>
                      <div className="flex items-center gap-2 pl-4">
                        <label className="w-auto font-bold text-xs pr-[10px]">
                          Pt_CAT1
                        </label>
                        <div className="items-center w-24">
                          <select
                            disabled={!formState.S_Parts_CAT1.enabled}
                            id="S_Parts_CAT1"
                            value={costListData?.S_Parts_CAT1 || ""}
                            onChange={handleCostListInputChange}
                            className={`border-gray-500 border-solid border-2 rounded-md  w-24 h-6 ${
                              formState.S_Parts_CAT1.enabled
                                ? "bg-[#ccffff] border-gray-500"
                                : "bg-white border-gray-500"
                            }`}
                          >
                            <option value=""></option>
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex item-center gap-2 pl-4">
                        <label className="font-bold text-xs mt-1">
                          Pt_CAT2
                        </label>
                        <div className="items-center w-24">
                          <select
                            disabled={!formState.S_Parts_CAT1.enabled}
                            id="S_Parts_CAT1"
                            value={costListData?.S_Parts_CAT1 || ""}
                            onChange={handleCostListInputChange}
                            className={`border-gray-500 border-solid border-2 rounded-md  w-24 h-6 ${
                              formState.S_Parts_CAT1.enabled
                                ? "bg-[#ccffff] border-gray-500"
                                : "bg-white border-gray-500"
                            }`}
                          >
                            <option value=""></option>
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                          </select>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 pl-4">
                        <label className="w-auto font-bold text-xs">
                          Pt_CAT3
                        </label>
                        <div className="items-center w-24">
                          <select
                            disabled={!formState.S_Parts_CAT3.enabled}
                            id="S_Parts_CAT3"
                            value={costListData?.S_Parts_CAT3 || ""}
                            onChange={handleCostListInputChange}
                            className={`border-gray-500 border-solid border-2 rounded-md  w-24 h-6 ${
                              formState.S_Parts_CAT3.enabled
                                ? "bg-[#ccffff] border-gray-500"
                                : "bg-white border-gray-500"
                            }`}
                          >
                            <option value=""></option>
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 ml-auto">
                      <label className="w-auto font-bold text-xs mt-1">
                        Parts_Delivery
                      </label>
                      <div className="items-center w-32">
                        <input
                          disabled={!formState.S_St_Parts_Delivery.enabled}
                          id="S_St_Parts_Delivery"
                          value={costListData?.S_St_Parts_Delivery || ""}
                          onChange={handleCostListInputChange}
                          type="date"
                          className={`h-6 border-solid border-2 rounded-md px-1 w-full ${
                            formState.S_St_Parts_Delivery.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        />
                      </div>
                      <label className="font-bold text-xs">~</label>
                      <div>
                        <input
                          disabled={!formState.S_Ed_Parts_Delivery.enabled}
                          id="S_Ed_Parts_Delivery"
                          value={costListData?.S_Ed_Parts_Delivery || ""}
                          onChange={handleCostListInputChange}
                          type="date"
                          className={`h-6 border-solid border-2 rounded-md px-1 w-32 ${
                            formState.S_Ed_Parts_Delivery.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                  {/* group3 */}
                  <div className="flex flex-warp justify-start gap-2 mb-2 mt-4 items-center">
                    <div className="flex item-center gap-2">
                      <label className="w-auto font-bold text-xs mr-[15px]">
                        Reg_Person
                      </label>
                      <div className="items-center w-full">
                        <select
                          id="S_Pl_Reg_Person_CD"
                          value={costListData?.S_Pl_Reg_Person_CD || ""}
                          onChange={handleCostListInputChange}
                          className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-24 h-6"
                        >
                          <option value={costListData?.S_Pl_Reg_Person_CD}>
                            {costListData?.S_Pl_Reg_Person_CD}
                          </option>
                          {Array.isArray(WorkerData) &&
                          WorkerData.length > 0 ? (
                            <>
                              <option disabled>
                                Worker_CD | Worker_Abb | Worker_Remark
                              </option>
                              {WorkerData.map((item, index) => (
                                <option key={index} value={item.Worker_CD}>
                                  {/* แสดงข้อมูลทั้งหมด (Worker_CD - Worker_Abb - Worker_Remark) */}
                                  {`${item.Worker_CD} | ${item.Worker_Abb} | ${item.Worker_Remark}`}
                                </option>
                              ))}
                            </>
                          ) : (
                            <option value="">No information</option>
                          )}
                        </select>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 pl-2">
                      <label className="w-auto font-bold text-xs">
                        Parts_Mate
                      </label>
                      <div className="items-center w-full">
                        <input
                          disabled={!formState.S_Parts_Material.enabled}
                          id="S_Parts_Material"
                          value={costListData?.S_Parts_Material || ""}
                          onChange={handleCostListInputChange}
                          className={`border-gray-500 border-solid border-2 rounded-md  w-24 h-6 ${
                            formState.S_Parts_Material.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        ></input>
                      </div>
                      <div className="flex items-center gap-2">
                        <label className="w-auto font-bold text-xs mr-3">
                          Parts_Note
                        </label>
                        <div className="items-center w-full">
                          <input
                            disabled={!formState.S_Parts_Instructions.enabled}
                            id="S_Parts_Instructions"
                            value={costListData?.S_Parts_Instructions || ""}
                            onChange={handleCostListInputChange}
                            className={`border-gray-500 border-solid border-2 rounded-md w-24 h-6 ${
                              formState.S_Parts_Instructions.enabled
                                ? "bg-[#ccffff] border-gray-500"
                                : "bg-white border-gray-500"
                            }`}
                          ></input>
                        </div>
                      </div>
                    </div>
                    <div className="flex item-center gap-2">
                      <label className="w-auto font-bold text-xs">
                        Pt_Remark
                      </label>
                      <div className="items-center w-full">
                        <input
                          disabled={!formState.S_Parts_Remark.enabled}
                          id="S_Parts_Remark"
                          value={costListData?.S_Parts_Remark || ""}
                          onChange={handleCostListInputChange}
                          className={`border-gray-500 border-solid border-2 rounded-md  w-24 h-6 ${
                            formState.S_Parts_Remark.enabled
                              ? "bg-[#ccffff] border-gray-500"
                              : "bg-white border-gray-500"
                          }`}
                        ></input>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 pl-2">
                      <label className="w-auto font-bold text-xs">
                        Parts_Info
                      </label>
                      <div className="items-center w-full">
                        <input
                          id="S_Parts_Information"
                          value={costListData?.S_Parts_Information || ""}
                          onChange={handleCostListInputChange}
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-24 h-6"
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-auto">
                      <label className="w-auto font-bold text-xs">
                        Pl_Progress
                      </label>
                      <div className="w-32">
                        <select
                          id="S_St_Pl_Progress_CD"
                          value={costListData?.S_St_Pl_Progress_CD ?? ""}
                          onChange={handleCostListInputChange}
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-36"
                        >
                          <option value=""></option>
                          {Array.isArray(PlProgressData) &&
                          PlProgressData.length > 0 ? (
                            PlProgressData.map((item, index) => (
                              <option key={index} value={item.Pl_Progress_CD}>
                                {item.Pl_Progress_Symbol}{" "}
                                {item.Pl_Progress_Remark}
                              </option>
                            ))
                          ) : (
                            <option value="">No information</option>
                          )}
                        </select>
                      </div>
                      <label className="font-bold text-xs">~</label>
                      <div>
                        <select
                          id="S_Ed_Pl_Progress_CD"
                          value={costListData?.S_Ed_Pl_Progress_CD ?? ""}
                          onChange={handleCostListInputChange}
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-36"
                        >
                          <option value=""></option>
                          {Array.isArray(PlProgressData) &&
                          PlProgressData.length > 0 ? (
                            PlProgressData.map((item, index) => (
                              <option key={index} value={item.Pl_Progress_CD}>
                                {item.Pl_Progress_Symbol}{" "}
                                {item.Pl_Progress_Remark}
                              </option>
                            ))
                          ) : (
                            <option value="">No information</option>
                          )}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Cost-Info-Seacrh */}
              <div className="p-2 mt-2">
                <div className="mb-2">
                  <label className="font-bold text-sm pl-4">
                    Cost_Info_Search
                  </label>
                </div>
                <div className="flex gap-6 mb-2 justify-end pr-3">
                  <div className="flex item-center gap-2">
                    <label className="w-auto font-bold text-xs ">
                      Process_Date
                    </label>
                    <div>
                      <input
                        id="S_St_Process_Date"
                        value={costListData?.S_St_Process_Date || ""}
                        onChange={handleCostListInputChange}
                        type="date"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32 h-6"
                      />
                    </div>
                    <label className="font-bold text-xs">~</label>
                    <div>
                      <input
                        id="S_Ed_Process_Date"
                        value={costListData?.S_Ed_Process_Date || ""}
                        onChange={handleCostListInputChange}
                        type="date"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32 h-6"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex gap-6 justify-end pr-3 mt-5">
                  <div className="flex item-center gap-2">
                    <label className="w-auto font-bold text-xs mt-1">
                      Complete_Date
                    </label>
                    <div>
                      <input
                        id="S_St_Complete_Date"
                        value={costListData?.S_St_Complete_Date || ""}
                        onChange={handleCostListInputChange}
                        type="date"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32 h-6"
                      />
                    </div>
                    <label className="font-bold text-xs">~</label>
                    <div>
                      <input
                        id="S_Ed_Complete_Date"
                        value={costListData?.S_Ed_Complete_Date || ""}
                        onChange={handleCostListInputChange}
                        type="date"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-32 h-6"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <hr className="mt-7 mb-2 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />
              {/* Result_Search */}
              <div className="overflow-x-auto w-full">
                <div className="flex">
                  <div className="p-2">
                    <div>
                      <label className="font-bold text-sm pl-4">
                        Result_Search
                      </label>
                    </div>
                    <div className="flex item-center gap-4 pl-10 mt-5 ">
                      <div className="flex w-full gap-4">
                        {/* div ย่อยที่ 1 */}
                        <div className="flex gap-6">
                          <div className="flex  item-center gap-2">
                            <label className="w-auto font-bold text-xs ">
                              Select_Od_No
                            </label>
                          </div>
                          <div>
                            <input
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 h-6"
                            />
                          </div>
                          {/* div ย่อยที่ 2 */}
                          <div className="flex gap-6">
                            <div className="flex  item-center gap-2">
                              <label className="w-auto font-bold text-xs ">
                                Select_Pt_No
                              </label>
                            </div>
                            <div>
                              <input
                                type="text"
                                className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 h-6"
                              />
                            </div>
                            {/* div ย่อยที่ 3 */}
                            <div className="flex gap-6">
                              <div className=" ">
                                <div className="flex item-center gap-2">
                                  <div className="flex  item-center gap-2">
                                    <label className="w-auto font-bold text-xs ">
                                      l_List_View_W(22.8)
                                    </label>
                                  </div>
                                  <div>
                                    <input
                                      type="text"
                                      className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 h-6"
                                    />
                                  </div>
                                </div>
                              </div>
                              {/* div ย่อยที่ 4 */}
                              <div className="flex gap-6">
                                <div className="flex item-center">
                                  <label className="w-auto font-bold text-xs">
                                    Pl_List_ViewH(3~15cm)
                                  </label>
                                </div>
                                <div>
                                  <input
                                    type="text"
                                    className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 h-6"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end p-4">
                <button className="bg-blue-500 p-3 rounded-md hover:bg-blue-700 font-medium text-white">
                  Change_View
                </button>
              </div>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />
              {isTableVisible && filteredCostlistData.length >= 0 && (
                <>
                  <div className="overflow-x-auto w-full mt-4">
                    <table className="min-w-full table-auto border-collapse border border-gray-800 shadow-md rounded-lg">
                      <thead className="bg-gray-200 text-black">
                        <tr>
                          {columnsVisibility.Product_Delivery && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Product_Delivery
                            </th>
                          )}
                          {columnsVisibility.Order_No && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Order_No
                            </th>
                          )}
                          {columnsVisibility.Parts_No && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[150px]">
                              Parts_No
                            </th>
                          )}
                          {columnsVisibility.Product_Grp && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Product_Grp
                            </th>
                          )}
                          {columnsVisibility.Customer_CD && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[200px]">
                              Customer_CD
                            </th>
                          )}
                          {columnsVisibility.Customer_Abb && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[250px]">
                              Customer_Abb
                            </th>
                          )}
                          {columnsVisibility.Product_Name && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[400px]">
                              Product_Name
                            </th>
                          )}
                          {columnsVisibility.Product_Size && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[400px]">
                              Product_Size
                            </th>
                          )}
                          {columnsVisibility.Product_Draw && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[250px]">
                              Product_Draw
                            </th>
                          )}
                          {columnsVisibility.Quantity && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Quantity
                            </th>
                          )}
                          {columnsVisibility.Pd_Calc_Qty && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Calc_Qty
                            </th>
                          )}
                          {columnsVisibility.Unit && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[200px]">
                              Unit
                            </th>
                          )}
                          {columnsVisibility.Target && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[150px]">
                              Target
                            </th>
                          )}
                          {columnsVisibility.Product_Docu && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium min-w-[400px]">
                              Product_Docu
                            </th>
                          )}
                          {columnsVisibility.Sales_Grp && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Sales_Grp
                            </th>
                          )}
                          {columnsVisibility.Sales_Person && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Sales_Person
                            </th>
                          )}
                          {columnsVisibility.Request1 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Request1
                            </th>
                          )}
                          {columnsVisibility.Request2 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Request2
                            </th>
                          )}
                          {columnsVisibility.Request3 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Request3
                            </th>
                          )}
                          {columnsVisibility.Material1 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Material1
                            </th>
                          )}
                          {columnsVisibility.Material2 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Material2
                            </th>
                          )}
                          {columnsVisibility.Coating_CD && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Coating_CD
                            </th>
                          )}
                          {columnsVisibility.Item1 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Item1
                            </th>
                          )}
                          {columnsVisibility.Item2 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Item2
                            </th>
                          )}
                          {columnsVisibility.Item3 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Item3
                            </th>
                          )}
                          {columnsVisibility.Item4 && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Item4
                            </th>
                          )}
                          {columnsVisibility.Price && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Price
                            </th>
                          )}
                          {columnsVisibility.Unit_Price && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Unit_Price
                            </th>
                          )}
                          {columnsVisibility.Pd_Received_Date && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Received_Date
                            </th>
                          )}
                          {columnsVisibility.Request_Delivery && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Request_Delivery
                            </th>
                          )}
                          {columnsVisibility.NAV_Delivery && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              NAV_Delivery
                            </th>
                          )}
                          {columnsVisibility.I_Completed_Date && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              I_Completed_Date
                            </th>
                          )}
                          {columnsVisibility.Pd_Calc_Date && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Calc_Date
                            </th>
                          )}
                          {columnsVisibility.Shipment_Date && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Shipment_Date
                            </th>
                          )}
                          {columnsVisibility.Specific && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Specific
                            </th>
                          )}
                          {columnsVisibility.Confirm_Delivery && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Confirm_Delivery
                            </th>
                          )}
                          {columnsVisibility.Delivery && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Delivery
                            </th>
                          )}
                          {columnsVisibility.Schedule && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Schedule
                            </th>
                          )}
                          {columnsVisibility.Od_Progress && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Od_Progress
                            </th>
                          )}
                          {columnsVisibility.Sl_Instructions && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Sl_Instructions
                            </th>
                          )}
                          {columnsVisibility.Pd_Instructions && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Instructions
                            </th>
                          )}
                          {columnsVisibility.Pd_Remark && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Remark
                            </th>
                          )}
                          {columnsVisibility.I_Remark && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              I_Remark
                            </th>
                          )}
                          {columnsVisibility.Pd_Complete_Date && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Pd_Complete_Date
                            </th>
                          )}
                          {columnsVisibility.Supple_Docu && (
                            <th className="border border-gray-300 px-6 py-3 text-center text-sm font-medium">
                              Supple_Docu
                            </th>
                          )}
                          {Array.from({ length: 36 }).map((_, index) => {
                            const processKey = `Process${index + 1}`;
                            return (
                              columnsVisibility[processKey] && (
                                <th
                                  key={processKey}
                                  className="border border-gray-300 px-6 py-3 text-center text-sm font-medium"
                                >
                                  {processKey}
                                </th>
                              )
                            );
                          })}
                        </tr>
                      </thead>
                      <tbody>
                        {displayedData.length > 0 &&
                          displayedData.map((cost, index) => {
                            const customer = Array.isArray(CustomerData)
                              ? CustomerData.find(
                                  (customer) =>
                                    customer.Customer_CD === cost.Customer_CD
                                )
                              : null;

                            const plan = Array.isArray(costListData.data)
                              ? costListData.data.find(
                                  (plan) => plan.Order_No === cost.Order_No
                                )
                              : null;

                            return (
                              <tr
                                key={index}
                                className="bg-white transition-colors duration-300"
                              >
                                {columnsVisibility.Product_Delivery && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Delivery
                                      ? new Date(
                                          cost.Product_Delivery
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Order_No && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Order_No}
                                  </td>
                                )}
                                {columnsVisibility.Parts_No && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Parts_No}
                                  </td>
                                )}
                                {columnsVisibility.Product_Grp && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Grp_CD}
                                  </td>
                                )}
                                {columnsVisibility.Customer_CD && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Customer_CD}
                                  </td>
                                )}
                                {columnsVisibility.Customer_Abb && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {customer ? customer.Customer_Abb : ""}
                                  </td>
                                )}
                                {columnsVisibility.Product_Name && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Name}
                                  </td>
                                )}
                                {columnsVisibility.Product_Size && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Size}
                                  </td>
                                )}
                                {columnsVisibility.Product_Draw && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Draw}
                                  </td>
                                )}
                                {columnsVisibility.Quantity && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Quantity}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Calc_Qty && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Calc_Qty}
                                  </td>
                                )}
                                {columnsVisibility.Unit && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Unit_CD}
                                  </td>
                                )}
                                {columnsVisibility.Target && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Target_CD}
                                  </td>
                                )}
                                {columnsVisibility.Product_Docu && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Product_Docu}
                                  </td>
                                )}
                                {columnsVisibility.Sales_Grp && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Sales_Grp_CD}
                                  </td>
                                )}
                                {columnsVisibility.Sales_Person && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Sales_Person_CD}
                                  </td>
                                )}
                                {columnsVisibility.Request1 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Request1_CD}
                                  </td>
                                )}
                                {columnsVisibility.Request2 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Request2_CD}
                                  </td>
                                )}
                                {columnsVisibility.Request3 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Request3_CD}
                                  </td>
                                )}
                                {columnsVisibility.Material1 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Material1}
                                  </td>
                                )}
                                {columnsVisibility.Material2 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Material2}
                                  </td>
                                )}
                                {columnsVisibility.Coating_CD && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Coating_CD}
                                  </td>
                                )}
                                {columnsVisibility.Item1 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Item1_CD}
                                  </td>
                                )}
                                {columnsVisibility.Item2 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Item2_CD}
                                  </td>
                                )}
                                {columnsVisibility.Item3 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Item3_CD}
                                  </td>
                                )}
                                {columnsVisibility.Item4 && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Item4_CD}
                                  </td>
                                )}
                                {columnsVisibility.Price && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Price_CD}
                                  </td>
                                )}
                                {columnsVisibility.Unit_Price && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Unit_Price}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Received_Date && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Received_Date
                                      ? new Date(
                                          cost.Pd_Received_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Request_Delivery && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Request_Delivery
                                      ? new Date(
                                          cost.Request_Delivery
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.NAV_Delivery && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.NAV_Delivery
                                      ? new Date(
                                          cost.NAV_Delivery
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.I_Completed_Date && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.I_Completed_Date
                                      ? new Date(
                                          cost.I_Completed_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Calc_Date && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Calc_Date
                                      ? new Date(
                                          cost.Pd_Calc_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Shipment_Date && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Shipment_Date
                                      ? new Date(
                                          cost.Shipment_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Specific && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Specific_CD}
                                  </td>
                                )}
                                {columnsVisibility.Confirm_Delivery && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Confirm_Delivery
                                      ? new Date(
                                          cost.Confirm_Delivery
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Delivery && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Delivery_CD}
                                  </td>
                                )}
                                {columnsVisibility.Schedule && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Schedule_CD}
                                  </td>
                                )}
                                {columnsVisibility.Od_Progress && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Od_Progress_CD}
                                  </td>
                                )}
                                {columnsVisibility.Sl_Instructions && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Sl_Instructions}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Instructions && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Instructions}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Remark && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Remark}
                                  </td>
                                )}
                                {columnsVisibility.I_Remark && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.I_Remark}
                                  </td>
                                )}
                                {columnsVisibility.Pd_Complete_Date && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Pd_Complete_Date
                                      ? new Date(
                                          cost.Pd_Complete_Date
                                        ).toLocaleDateString("en-GB")
                                      : ""}
                                  </td>
                                )}
                                {columnsVisibility.Supple_Docu && (
                                  <td className="border border-gray-300 px-6 py-3 text-sm text-gray-800">
                                    {cost.Supple_Docu}
                                  </td>
                                )}
                                {Array.from({ length: 36 }).map((_, index) => {
                                  const processKey = `Process${index + 1}`;
                                  const ppcKey = `PPC${index + 1}`;

                                  return (
                                    columnsVisibility[processKey] && (
                                      <td
                                        key={processKey}
                                        className="border border-gray-300 px-6 py-3 text-sm text-gray-800"
                                      >
                                        {cost[ppcKey]}
                                      </td>
                                    )
                                  );
                                })}
                              </tr>
                            );
                          })}
                      </tbody>
                    </table>
                  </div>
                  <div className="flex justify-between items-center mt-4">
                    <button
                      onClick={goToPrevPage}
                      disabled={currentPage === 1}
                      className={`p-2 rounded-full ${
                        currentPage === 1
                          ? "bg-gray-300 cursor-not-allowed"
                          : "bg-blue-500 text-white hover:bg-blue-600"
                      }`}
                    >
                      <HiChevronLeft size={20} />
                    </button>

                    <div className="flex items-center gap-4">
                      <span>
                        Page {currentPage} of {totalPages}
                      </span>

                      <select
                        className="border border-gray-400 rounded px-2 py-1"
                        value={rowsPerPage}
                        onChange={(e) => {
                          setRowsPerPage(Number(e.target.value));
                          setCurrentPage(1);
                        }}
                      >
                        <option value={10}>10 Rows</option>
                        <option value={15}>15 Rows</option>
                        <option value={20}>20 Rows</option>
                        <option value={25}>25 Rows</option>
                      </select>
                    </div>

                    <button
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                      className={`p-2 rounded-full ${
                        currentPage === totalPages
                          ? "bg-gray-300 cursor-not-allowed"
                          : "bg-blue-500 text-white hover:bg-blue-600"
                      }`}
                    >
                      <HiChevronRight size={20} />
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="bg-white p-2 mt-3">
          <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-2">
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F1"
                disabled={!buttonState.F1}
                className={`bg-blue-500 p-1 py-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Search <br />
                検索 (F1)
              </button>
              <button
                id="F2"
                disabled={!buttonState.F2}
                onClick={handleF2Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Setting <br />
                設定 (F2)
              </button>
              {showDialog && (
                <div className="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
                  <div className="bg-white p-6 rounded-lg shadow-lg w-[300px]">
                    <h3 className="text-lg font-bold mb-4">Column Settings</h3>
                    <form className="max-h-[200px] overflow-y-auto">
                      {/* Check All button */}
                      <div className="flex items-center mb-2">
                        <input
                          type="checkbox"
                          id="checkAll"
                          onChange={handleCheckAll}
                          checked={Object.values(columnsVisibility).every(
                            (value) => value
                          )}
                          className="mr-2"
                        />
                        <label htmlFor="checkAll" className="text-sm">
                          Select All
                        </label>
                      </div>

                      <hr className="mb-2" />

                      {Object.keys(columnsVisibility).map((column) => (
                        <div key={column} className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={column}
                            name={column}
                            checked={columnsVisibility[column]}
                            onChange={handleCheckboxChange}
                            className="mr-2"
                          />
                          <label htmlFor={column} className="text-sm">
                            {column}
                          </label>
                        </div>
                      ))}
                    </form>
                    <div className="mt-4 flex justify-end">
                      <button
                        onClick={handleCloseDialog}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-700"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
              <button
                id="F3"
                disabled={!buttonState.F3}
                onClick={handleF3Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Show <br />
                照会 (F3)
              </button>

              <button
                id="F4"
                disabled={!buttonState.F4}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Target <br />
                対象 (F4)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F5"
                disabled={!buttonState.F5}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Product <br />
                部門 (F5)
              </button>
              <button
                id="F6"
                disabled={!buttonState.F6}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Calc <br />
                生産 (F6)
              </button>
              <button
                id="F7"
                disabled={!buttonState.F7}
                onClick={handleF7Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                List <br />一 覽 (F7)
              </button>
              <button
                id="F8"
                disabled={!buttonState.F8}
                onClick={handleF8Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Data <br />
                データ (F8)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F9"
                onClick={handleF9Click}
                disabled={!buttonState.F9}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                <label className="flex justify-center items-center">
                  <IoIosArrowRoundForward className="font-medium text-2xl" />{" "}
                  CSV{" "}
                </label>
                (F9)
              </button>
              <button
                id="F10"
                disabled={!buttonState.F10}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                (F10)
              </button>
              <button
                id="F11"
                disabled={!buttonState.F11}
                onClick={handleF11Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Clear <br />
                クリア (F11)
              </button>
              <button
                id="F12"
                disabled={!buttonState.F12}
                onClick={handleF12Click}
                className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
              >
                Exit <br />
                終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
