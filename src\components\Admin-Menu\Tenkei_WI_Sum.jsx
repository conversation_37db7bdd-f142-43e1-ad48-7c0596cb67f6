import React, { useState, useEffect } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Tenkei_WI_Sum() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [allSelected, setAllSelected] = useState(false);
  const [checkboxes, setCheckboxes] = useState({
    Plan_WI: false,
    WI_SUM: false,
    WI_SUM_Coating: false,
    WI_SUM_Compound: false,
    WI_SUM_Item1: false,
    WI_SUM_Item_1G: false,
    WI_SUM_Outside: false,
    WI_SUM_Pending: false,
    WI_SUM_Plan: false,
    WI_SUM_Progress: false,
    WI_SUM_Type: false,
    WI_SUM_Week: false,
  });

  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();

  const handleAllSelect = () => {
    const newValue = !allSelected;
    setAllSelected(newValue);
    setCheckboxes((prev) =>
      Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: newValue }), {})
    );
  };

  const handleCheckboxChange = (key) => {
    setCheckboxes((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleExecute = () => {
    const selectedKeys = Object.keys(checkboxes).filter(
      (key) => checkboxes[key]
    );
    if (selectedKeys.length === 0) {
      alert("Please select the required information.");
      return;
    }

    // คำนวณตำแหน่งและขนาดของหน้าต่างใหม่
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const width = Math.floor(screenWidth * 0.8);
    const height = Math.floor(screenHeight * 0.8);

    // เปิดแท็บสำหรับแต่ละ checkbox ที่เลือก
    selectedKeys.forEach((key, index) => {
      const left = Math.floor((screenWidth - width) / 2) + index * 30; // เพิ่มการเลื่อนตำแหน่งแท็บ
      const top = Math.floor((screenHeight - height) / 2) + index * 30;

      const urlMap = {
        Plan_WI: "/plan_wi",
        WI_SUM: "/wi_sum",
        WI_SUM_Coating: "/wi_sum_coating",
        WI_SUM_Compound: "/wi_sum_compound",
        WI_SUM_Item1: "/wi_sum_item1",
        WI_SUM_Item_1G: "/wi_sum_item_1g",
        WI_SUM_Outside: "/wi_sum_outside",
        WI_SUM_Pending: "/wi_sum_pending",
        WI_SUM_Plan: "/wi_sum_plan",
        WI_SUM_Progress: "/wi_sum_progress",
        WI_SUM_Type: "/wi_sum_type",
        WI_SUM_Week: "/wi_sum_week",
      };

      window.open(
        urlMap[key], // URL ของแต่ละ key
        "_blank",
        `width=${width},height=${height},left=${left},top=${top}`
      );
    });
  };

  const [data, setData] = useState(null);

  // ฟังก์ชันดึงข้อมูล
  const fetchData = async () => {
    localStorage.removeItem("wiPlanTable"); // ลบข้อมูลเก่าก่อนโหลดใหม่

    const cachedData = localStorage.getItem("wiPlanTable");
    if (cachedData) {
      setData(JSON.parse(cachedData).QD_Plan_WI);
      return;
    }

    try {
      const response = await axios.post(`${apiUrl_4000}/wi-sum/wi-plan`);
      if (response.data.status === "success") {
        const formattedData = { QD_Plan_WI: response.data.data };
        setData(formattedData.QD_Plan_WI);
        localStorage.setItem("wiPlanTable", JSON.stringify(formattedData));
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      alert("Error fetching data");
    }
  };

  // เรียก fetchData เมื่อเข้ามาหน้านี้
  useEffect(() => {
    fetchData();
  }, []);

  const handleBackToAdminClick = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to admin?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า admin หรือไม่?<br>データは編集されました。admin に戻りますか？"
          : "Do you want to go back to admin?<br>คุณต้องการกลับไปที่หน้า admin หรือไม่?<br>admin に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/admin-menu");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "เกิดข้อผิดพลาด",
        text: "กรุณาลองอีกครั้ง",
        icon: "error",
        confirmButtonText: "ตกลง",
      });
    }
  };

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="flex justify-between items-center mt-5 px-5 text-base">
              <h1 className="text-2xl font-bold text-center flex-1 lg:pl-32">
                TENKEI WI SUM
              </h1>
              <button
                onClick={handleBackToAdminClick}
                className="bg-red-500 text-white px-4 py-2 rounded-md shadow-md ease-in-out hover:bg-red-600"
              >
                Back to Admin
              </button>
            </div>

            <hr className="my-5 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

            <div className="space-x-4 p-4 bg-gray-200 flex overflow-x-auto min-w-[100px] items-center lg:justify-center justify-start">
              {/* First Column */}
              <div className="flex flex-col space-y-2">
                {[
                  "Plan_WI",
                  "WI_SUM",
                  "WI_SUM_Coating",
                  "WI_SUM_Compound",
                  "WI_SUM_Item1",
                  "WI_SUM_Item_1G",
                ].map((key) => (
                  <label key={key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="form-checkbox"
                      checked={checkboxes[key]}
                      onChange={() => handleCheckboxChange(key)}
                    />
                    <span>{key}</span>
                  </label>
                ))}
              </div>
              {/* Second Column */}
              <div className="flex flex-col space-y-2">
                {[
                  "WI_SUM_Outside",
                  "WI_SUM_Pending",
                  "WI_SUM_Plan",
                  "WI_SUM_Progress",
                  "WI_SUM_Type",
                  "WI_SUM_Week",
                ].map((key) => (
                  <label key={key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="form-checkbox"
                      checked={checkboxes[key]}
                      onChange={() => handleCheckboxChange(key)}
                    />
                    <span>{key}</span>
                  </label>
                ))}
              </div>
              {/* Third Column */}
              <div className="flex flex-col space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    className="form-checkbox"
                    checked={allSelected}
                    onChange={handleAllSelect}
                  />
                  <span>All_Select</span>
                </label>
                <button
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                  onClick={handleExecute}
                >
                  Execute
                </button>
                <button
                  onClick={() => {
                    setCheckboxes({
                      Plan_WI: false,
                      WI_SUM: false,
                      WI_SUM_Coating: false,
                      WI_SUM_Compound: false,
                      WI_SUM_Item1: false,
                      WI_SUM_Item_1G: false,
                      WI_SUM_Outside: false,
                      WI_SUM_Pending: false,
                      WI_SUM_Plan: false,
                      WI_SUM_Progress: false,
                      WI_SUM_Type: false,
                      WI_SUM_Week: false,
                    });
                    setAllSelected(false);
                  }}
                  className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
