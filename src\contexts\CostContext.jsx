import { useState, createContext, useEffect } from "react";
import axios from "../configs/axios";

export const CostContext = createContext();

export default function CostContextProvider({ children }) {
  const [CostData, setCostData] = useState(null);
  const [CostNoData, setCostNo] = useState(null);
  const [ResourceData, setResourceData] = useState(null);
  const [CsProgressData, setCsProgressData] = useState(null);
  const [PlanppcData, setPlanppcData] = useState(null);
  const [ProcessCData, setProcessCData] = useState(null);
  const [Act_ProcessData, setAct_ProcessData] = useState(null);
  const [Act_PartData, setAct_PartData] = useState(null);
  const [Plan_MultiData, setPlan_MultiData] = useState(null);
  const [Order_MultiData, setOrder_MultiData] = useState(null);
  const [sData, setSData] = useState({
    Obj_Od_No: "",
    Obj_Pt_No: "",
    Old_Od_Progress: "",
    Cost_New: false,
    Old_Obj_Cs_No: "",
    Old_Obj_Pr_No: "",
    Old_Cs_Progress: "",
    Old_Cs_Final: false,
  });

  const SearchCostData = async (orderNo, partsNO) => {
    try {
      const response = await axios.post("/cost/cost-part", {
        Order_No: orderNo,
        Parts_No: partsNO,
      });

      if (response.data && response.data.data && response.data.data.cost) {
        setCostNo(response.data.data.cost);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error fetching Cost data:", error);
      return false;
    }
  };

  const fetchResource = async () => {
    try {
      const response = await axios.get("/resource/fetch-resource");
      setResourceData(response.data.message);

      return response;
    } catch (error) {
      console.error("Error fetching resource:", error);
      throw error;
    }
  };

  const fetchCsProgress = async () => {
    try {
      const response = await axios.get("/csprogress/fetch-csprogress");
      setCsProgressData(response.data.data.csprogress);

      return response;
    } catch (error) {
      console.error("Error fetching csprogress:", error);
      throw error;
    }
  };

  const fetchPlanppc = async (orderNo,partsNO) => {
    try {
      const response = await axios.post("/planppc/pr-abb",{
        Order_No: orderNo,
        Parts_No: partsNO,  
      });
      setPlanppcData(response.data.data.costs);

      return response;
    } catch (error) {
      console.error("Error fetching planppc:", error);
      throw error;
    }
  };

  const SearchCostNo = async (orderNo, partsNO, CostNo) => {
    try {
      const response = await axios.post("/cost/cost-no", {
        Order_No: orderNo,
        Parts_No: partsNO,
        Cost_No: CostNo,
      });

      if (response.data && response.data.data && response.data.data.cost) {
        setCostData(response.data.data.cost);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error fetching Cost data:", error);
      return false;
    }
  };

  const editCost = async () => {
    try {
      const response = await axios.put("/cost/cost-update", CostData);
      console.log("Cost updated successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error updating Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

  const createCost = async () => {
    try {
      const response = await axios.post("/cost/cost-add", CostData);
      console.log("Cost create successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error create Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

  const UpdateCost = async (CostData, orderData, planData, StatusData) => {
    try {
      const updatedCostData = {
        ...CostData,
        Cs_Remark: CostData?.Cs_Remark || "",
        Od_Progress_CD: orderData?.Od_Progress_CD,
        Pl_Progress_CD: planData?.Pl_Progress_CD,
        Pt_l_Comp_Qty: planData?.Pt_l_Comp_Qty,
        I_Complete_Qty: orderData?.I_Complete_Qty,
        Pt_I_Date: planData?.Pt_I_Date,
        Pt_Qty: planData?.Pt_Qty,
        Quantity: orderData?.Quantity,
        I_Completed_Date: orderData?.I_Completed_Date,
        Outside1: planData?.Outside,
        Obj_Od_No: StatusData?.Obj_Od_No,
        Obj_Pt_No: StatusData?.Obj_Pt_No,
        Act_Pr_No: StatusData?.Act_Pr_No,
        Cost_New: StatusData?.Cost_New,
        Old_Obj_Pr_No: StatusData?.Old_Obj_Pr_No,
        Now_No: planData?.Now_No,
        Re_Pr_Qty: planData?.Re_Pr_Qty,
        Re_Total_M_Time: planData?.Re_Total_M_Time,
        Re_Total_P_Time: planData?.Re_Total_P_Time,
        Re_Total_N_Time: planData?.Re_Total_N_Time,
      };
      const response = await axios.post("/cost/cost-update", updatedCostData);  
      const rawData = response?.data?.data;
      setSData({ ...rawData });
      return response.data;
    } catch (error) {
      console.error(
        "Error create Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

  const UpdateCostConfirms = async (
    CostData,
    orderData,
    planData,
    StatusData
  ) => {
    try {
      const updatedCostData = {
        ...CostData,
        Cs_Remark: CostData?.Cs_Remark || "",
        Od_Progress_CD: orderData?.Od_Progress_CD,
        Pl_Progress_CD: planData?.Pl_Progress_CD,
        Pt_l_Comp_Qty: planData?.Pt_l_Comp_Qty,
        I_Complete_Qty: orderData?.I_Complete_Qty,
        Pt_I_Date: planData?.Pt_I_Date,
        Pt_Qty: planData?.Pt_Qty,
        Quantity: orderData?.Quantity,
        I_Completed_Date: orderData?.I_Completed_Date,
        Outside1: planData?.Outside,
        Obj_Od_No: StatusData?.Obj_Od_No,
        Obj_Pt_No: StatusData?.Obj_Pt_No,
        Act_Pr_No: StatusData?.Act_Pr_No,
        Cost_New: StatusData?.Cost_New,
        Old_Obj_Pr_No: StatusData?.Old_Obj_Pr_No,
        Now_No: planData?.Now_No,
        Re_Pr_Qty: planData?.Re_Pr_Qty,
        Re_Total_M_Time: planData?.Re_Total_M_Time,
        Re_Total_P_Time: planData?.Re_Total_P_Time,
        Re_Total_N_Time: planData?.Re_Total_N_Time,
      };
      const response = await axios.post("/cost/cost-confirms", updatedCostData);
      return response.data;
    } catch (error) {
      console.error(
        "Error create Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

  const fetchProcessCost = async () => {
    try {
      const response = await axios.get("/process/fetch-processC");
      setProcessCData(response.data.data.process);

      return response;
    } catch (error) {
      console.error("Error fetching process:", error);
      throw error;
    }
  };

  const Cs_Progress_CD_AfterUpdate = async () => {
    try {
      const response = await axios.post("/cost/cost-cs_progress_cd", CostData);
      setCostData(response.data.data);
      return response.data;
    } catch (error) {
      console.error("Error fetch Cs_Progress_CD_AfterUpdate:", error);
      throw new Error("Failed to fetch Cs_Progress_CD_AfterUpdate");
    }
  };

  const searchPartData = async (orderNos) => {
    try {
      const response = await axios.post("/cost/cost-part-multi", orderNos);

      if (!response.data) return false;

      // อัปเดตข้อมูลถ้ามี data
      if (response.data.data) {
        setAct_PartData((prevData) => ({
          ...prevData,
          ...response.data.data,
        }));
      }

      // อัปเดตข้อมูลถ้ามี data2
      if (response.data.data2) {
        setOrder_MultiData((prevData) => ({
          ...prevData,
          ...response.data.data2,
        }));
      }

      return true;
    } catch (error) {
      console.error("Error fetching order data:", error);
      return false;
    }
  };

  const Search_Parts_No_AfterUpdate = async (
    Act_Order_No,
    Act_Parts_No,
    orderIndex
  ) => {
    try {
      const requestBody = {
        [`Act_Order_No${orderIndex}`]: Act_Order_No,
        [`Act_Parts_No${orderIndex}`]: Act_Parts_No,
      };

      const response = await axios.post("/cost/cost-plan-multi", requestBody);

      const planData = response.data.data[`plans${orderIndex}`];
      const costData = response.data.data[`CostNo${orderIndex}`];

      if (planData && planData.length > 0) {
        const planDataItem = planData[0];
        setPlan_MultiData((prevData) => ({
          ...prevData,
          ...planDataItem,
          [`plans${orderIndex}`]: planDataItem || {},
        }));
      }

      if (costData && costData.length > 0) {
        const newCostData = costData.reduce((acc, item) => {
          return {
            ...acc,
            [`CostNo${orderIndex}`]: item,
          };
        }, {});
        setCostData((prevData) => ({
          ...prevData,
          ...newCostData,
        }));
      }
      return response;
    } catch (error) {
      console.error("Error creating Search_Parts_No_AfterUpdate:", error);
      throw new Error("Failed to create Search_Parts_No_AfterUpdate");
    }
  };

  const cost_process = async (Act_Order_No, Act_Parts_No, orderIndex) => {
    try {
      const requestBody = {
        [`Act_Order_No${orderIndex}`]: Act_Order_No,
        [`Act_Parts_No${orderIndex}`]: Act_Parts_No,
      };

      const response = await axios.post("/cost/cost-process", requestBody);

      setAct_ProcessData((prevData) => ({
        ...prevData,
        ...response.data.data.costs,
      }));
      return response;
    } catch (error) {
      console.error("Error creating cost_process:", error);
      throw new Error("Failed to create cost_process");
    }
  };

  const Cs_Progress_CD_AfterUpdate2 = async (CostData, orderIndex) => {
    try {
      const updatedCostData = {
        ...CostData,
        index: orderIndex,
        [`Order_No${orderIndex}`]: CostData?.[`Act_Order_No${orderIndex}`],
        [`Parts_No${orderIndex}`]: CostData?.[`Act_Parts_No${orderIndex}`],
        [`Process_No${orderIndex}`]: CostData?.[`Act_Process_No${orderIndex}`],
        [`Cs_Progress_CD${orderIndex}`]:
          CostData?.[`Act_Cs_Progress_CD${orderIndex}`],
        [`CMC${orderIndex}`]: CostData?.[`Act_CMC${orderIndex}`],
        [`Cs_Complete_Date${orderIndex}`]:
          CostData?.[`Act_Cs_Complete_Date${orderIndex}`],
        [`Cs_Complete_Qty${orderIndex}`]:
          CostData?.[`Act_Cs_Complete_Qty${orderIndex}`],
        [`Cs_Final_Complete${orderIndex}`]:
          CostData?.[`Act_Cs_Final_Complete${orderIndex}`],
        [`Outside${orderIndex}`]: CostData?.[`Act_Outside${orderIndex}`],
        [`CPD${orderIndex}`]: CostData?.[`Act_CPD${orderIndex}`],
        [`CPN${orderIndex}`]: CostData?.[`Act_CPN${orderIndex}`],
        [`CMT${orderIndex}`]: CostData?.[`Act_CMT${orderIndex}`],
        [`CPT${orderIndex}`]: CostData?.[`Act_CPT${orderIndex}`],
      };
      const response = await axios.post(
        "/cost/cost-cs_progress_cd2",
        updatedCostData
      );
      setCostData((prevState) => ({
        ...prevState,
        [`Act_Cs_Complete_Date${orderIndex}`]:
          response.data.data[`Cs_Complete_Date${orderIndex}`],
        [`Act_CMC${orderIndex}`]: response.data.data[`CMC${orderIndex}`],
        [`Act_Cs_Complete_Qty${orderIndex}`]:
          response.data.data[`Cs_Complete_Qty${orderIndex}`],
        [`Act_Cs_Final_Complete${orderIndex}`]:
          response.data.data[`Cs_Final_Complete${orderIndex}`],
        [`Act_Outside${orderIndex}`]:
          response.data.data[`Outside${orderIndex}`],
        [`Act_CPD${orderIndex}`]: response.data.data[`CPD${orderIndex}`],
        [`Act_CPN${orderIndex}`]: response.data.data[`CPN${orderIndex}`],
        [`Act_CMT${orderIndex}`]: response.data.data[`CMT${orderIndex}`],
        [`Act_CPT${orderIndex}`]: response.data.data[`CPT${orderIndex}`],
        [`Cs_Complete_Dates${orderIndex}`]:
          response.data.data[`Cs_Complete_Dates${orderIndex}`],
        [`Cs_Complete_Qtys${orderIndex}`]:
          response.data.data[`Cs_Complete_Qtys${orderIndex}`],
        [`Cs_Final_Completes${orderIndex}`]:
          response.data.data[`Cs_Final_Completes${orderIndex}`],
        [`Outsides${orderIndex}`]: response.data.data[`Outsides${orderIndex}`],
      }));

      return response.data;
    } catch (error) {
      console.error("Error fetch Cs_Progress_CD_AfterUpdate:", error);
      throw new Error("Failed to fetch Cs_Progress_CD_AfterUpdate");
    }
  };

  const UpdateCost2 = async (
    CostData,
    orderIndex,
    StatusData,
    Plan_MultiData,
    Order_MultiData,
    incrementedCostNo
  ) => {
    try {
      const updatedCostData = {
        Order_No: CostData?.[`Act_Order_No${orderIndex}`],
        Parts_No: CostData?.[`Act_Parts_No${orderIndex}`],
        Cost_No: incrementedCostNo,
        Process_No: CostData?.[`Act_Process_No${orderIndex}`],
        CMC: CostData?.[`Act_CMC${orderIndex}`],
        CMT: CostData?.[`Act_CMT${orderIndex}`],
        CPC: CostData?.[`Act_CPC${orderIndex}`],
        CPT: CostData?.[`Act_CPT${orderIndex}`],
        CPD: CostData?.[`Act_CPD${orderIndex}`],
        CPN: CostData?.[`Act_CPN${orderIndex}`],
        Cs_Remark: CostData?.[`Act_Cs_Remark${orderIndex}`] || "",
        Cs_Complete_Date: CostData?.[`Act_Cs_Complete_Date${orderIndex}`] || "",
        Cs_Complete_Qty: CostData?.[`Act_Cs_Complete_Qty${orderIndex}`] || "0",
        Cs_Progress_CD: CostData?.[`Act_Cs_Progress_CD${orderIndex}`],
        Cs_Final_Complete: CostData?.[`Act_Cs_Final_Complete${orderIndex}`],
        Od_Progress_CD:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.Od_Progress_CD,
        Pl_Progress_CD: Plan_MultiData?.[`plans${orderIndex}`]?.Pl_Progress_CD,
        Pt_l_Comp_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_l_Comp_Qty,
        I_Complete_Qty:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.I_Complete_Qty,
        Pt_I_Date: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_I_Date,
        Pt_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_Qty,
        Quantity: Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.Quantity,
        I_Completed_Date:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.I_Completed_Date,
        Outside1: Plan_MultiData?.[`plans${orderIndex}`]?.Outside,
        Obj_Od_No: StatusData?.Obj_Od_No,
        Obj_Pt_No: StatusData?.Obj_Pt_No,
        Act_Pr_No: StatusData?.Act_Pr_No,
        Cost_New: StatusData?.Cost_New,
        Old_Obj_Pr_No: StatusData?.Old_Obj_Pr_No,
        Now_No: Plan_MultiData?.[`plans${orderIndex}`]?.Now_No,
        Re_Pr_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Re_Pr_Qty,
        Re_Total_M_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_M_Time,
        Re_Total_P_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_P_Time,
        Re_Total_N_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_N_Time,
      };
      const response = await axios.post("/cost/cost-update-multi", updatedCostData);
      return response.data;
    } catch (error) {
      console.error(
        "Error create Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

  const UpdateCostConfirms2 = async (
    CostData,
    orderIndex,
    StatusData,
    Plan_MultiData,
    Order_MultiData,
    incrementedCostNo
  ) => {
    try {
      const updatedCostData = {
        Order_No: CostData?.[`Act_Order_No${orderIndex}`],
        Parts_No: CostData?.[`Act_Parts_No${orderIndex}`],
        Cost_No: incrementedCostNo,
        Process_No: CostData?.[`Act_Process_No${orderIndex}`],
        CMC: CostData?.[`Act_CMC${orderIndex}`],
        CMT: CostData?.[`Act_CMT${orderIndex}`],
        CPC: CostData?.[`Act_CPC${orderIndex}`],
        CPT: CostData?.[`Act_CPT${orderIndex}`],
        CPD: CostData?.[`Act_CPD${orderIndex}`],
        CPN: CostData?.[`Act_CPN${orderIndex}`],
        Cs_Remark: CostData?.[`Act_Cs_Remark${orderIndex}`] || "",
        Cs_Complete_Date: CostData?.[`Act_Cs_Complete_Date${orderIndex}`] || "",
        Cs_Complete_Qty: CostData?.[`Act_Cs_Complete_Qty${orderIndex}`] || "0",
        Cs_Progress_CD: CostData?.[`Act_Cs_Progress_CD${orderIndex}`],
        Cs_Final_Complete: CostData?.[`Act_Cs_Final_Complete${orderIndex}`],
        Od_Progress_CD:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.Od_Progress_CD,
        Pl_Progress_CD: Plan_MultiData?.[`plans${orderIndex}`]?.Pl_Progress_CD,
        Pt_l_Comp_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_l_Comp_Qty,
        I_Complete_Qty:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.I_Complete_Qty,
        Pt_I_Date: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_I_Date,
        Pt_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Pt_Qty,
        Quantity: Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.Quantity,
        I_Completed_Date:
          Order_MultiData?.[`Orders${orderIndex}`]?.[0]?.I_Completed_Date,
        Outside1: Plan_MultiData?.[`plans${orderIndex}`]?.Outside,
        Obj_Od_No: StatusData?.Obj_Od_No,
        Obj_Pt_No: StatusData?.Obj_Pt_No,
        Act_Pr_No: StatusData?.Act_Pr_No,
        Cost_New: StatusData?.Cost_New,
        Old_Obj_Pr_No: StatusData?.Old_Obj_Pr_No,
        Now_No: Plan_MultiData?.[`plans${orderIndex}`]?.Now_No,
        Re_Pr_Qty: Plan_MultiData?.[`plans${orderIndex}`]?.Re_Pr_Qty,
        Re_Total_M_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_M_Time,
        Re_Total_P_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_P_Time,
        Re_Total_N_Time:
          Plan_MultiData?.[`plans${orderIndex}`]?.Re_Total_N_Time,
      };
      const response = await axios.post("/cost/cost-confirms-multi", updatedCostData);
      return response.data;
    } catch (error) {
      console.error(
        "Error create Cost:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update Cost");
    }
  };

useEffect(() => {
  const fetchAllData = async () => {
    try {
      const [resource, csProgress, processCost] = await Promise.all([
        fetchResource(),
        fetchCsProgress(),
        fetchProcessCost(),
      ]);

      // ถ้าทั้ง 3 function มี return ก็สามารถ setState ได้ตรงนี้
      // setResourceData(resource);
      // setCsProgressData(csProgress);
      // setProcessCostData(processCost);
    } catch (error) {
      console.error("เกิดข้อผิดพลาดในการดึงข้อมูล:", error);
    }
  };

  fetchAllData();
}, []);

  return (
    <CostContext.Provider
      value={{
        CostData,
        setCostData,
        SearchCostData,
        CostNoData,
        setCostNo,
        ResourceData,
        setResourceData,
        CsProgressData,
        setCsProgressData,
        SearchCostNo,
        editCost,
        createCost,
        PlanppcData,
        setPlanppcData,
        ProcessCData,
        setProcessCData,
        UpdateCost,
        UpdateCostConfirms,
        Cs_Progress_CD_AfterUpdate,
        cost_process,
        Act_ProcessData,
        searchPartData,
        Act_PartData,
        Search_Parts_No_AfterUpdate,
        Plan_MultiData,
        Order_MultiData,
        Cs_Progress_CD_AfterUpdate2,
        UpdateCost2,
        UpdateCostConfirms2,
        fetchPlanppc,
        sData,
      }}
    >
      {children}
    </CostContext.Provider>
  );
}
