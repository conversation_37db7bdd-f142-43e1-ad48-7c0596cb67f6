import React, { useState, useEffect } from "react";

// Icons
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function QR_Od_List_Detail_Product() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [orderListData, setOrderListData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const { status, data } = event.data;

      if (status === "success") {
        setOrderListData(data);
      } else {
        console.error("Failed to receive data.");
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  const totalPages = Math.ceil(orderListData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = orderListData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  // console.log("orderListData :", orderListData);

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-center items-center my-3 font-semibold text-xl">
        <span>QR_Od_List_Detail_Product</span>
      </div>
      <table className="min-w-full bg-white border border-gray-300 rounded-lg shadow-lg">
        <thead className="bg-gray-200 text-gray-700">
          <tr>
            <th className="py-2 px-4 text-left border-b">Product_Grp_CD</th>
            <th className="py-2 px-4 text-left border-b">Target_Sum</th>
          </tr>
        </thead>
        <tbody>
          {displayedData.length > 0 ? (
            displayedData.map((orders, index) => (
              <tr key={index} className="hover:bg-gray-100">
                <td className="py-2 px-4 border-b">{orders.Product_Grp_CD}</td>
                <td className="py-2 px-4 border-b">{orders.Target_Sum}</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="4" className="py-2 px-4 text-center">
                No data available
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={goToPrevPage}
          disabled={currentPage === 1}
          className={`p-2 rounded-full ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronLeft size={20} />
        </button>

        <div className="flex items-center gap-4">
          <span>
            Page {currentPage} of {totalPages || 1}
          </span>
          <select
            className="border border-gray-400 rounded px-2 py-1"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10 Rows</option>
            <option value={15}>15 Rows</option>
            <option value={20}>20 Rows</option>
            <option value={25}>25 Rows</option>
          </select>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          className={`p-2 rounded-full ${
            currentPage === totalPages || totalPages === 0
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
