// import { useState, createContext, useEffect } from "react";
// import axios from "../configs/axios";

// export const PlanContext = createContext();

// export default function PlanContextProvider({ children }) {
//   const [planData, setPlanData] = useState(null);
//   const [selectedPlanNo, setSelectedPlanNo] = useState(null);
//   const [qmprocessData, setQMprocessData] = useState(null);
//   const [processData, setProcessData] = useState(null);
//   const [plprogressData, setPlProgressData] = useState(null);
//   const [ScheduleData, setScheduleData] = useState(null);
//   const [PartsData, setPartsData] = useState(null);
//   const searchPartsData = async (orderNo) => {
//     try {
//       const response = await axios.post("/plan/search-order-plan", {
//         Order_No: orderNo,
//       });

//       if (
//         response.data &&
//         response.data.data &&
//         Array.isArray(response.data.data.partsNo)
//       ) {
//         setSelectedPlanNo(response.data.data.partsNo);
//         return true;
//       } else {
//         return false;
//       }
//     } catch (error) {
//       console.error("Error fetching order data:", error);
//       return false;
//     }
//   };

//   const selectPartsData = async (orderNo, partsNO) => {
//     try {
//       const response = await axios.post("/plan/search-part-plan", {
//         Order_No: orderNo,
//         Parts_No: partsNO,
//       });

//       if (
//         response.data &&
//         response.data.data &&
//         Array.isArray(response.data.data)
//       ) {
//         setPlanData(response.data.data);
//         return true;
//       } else {
//         return false;
//       }
//     } catch (error) {
//       console.error("Error fetching order data:", error);
//       return false;
//     }
//   };

//   const QM_Process = async () => {
//     try {
//       const response = await axios.get("/process/fetch-qmprocess");
//       console.log("Fetched Data:", response.data.data.process); // ตรวจสอบข้อมูล
//       setQMprocessData(response.data.data.process); // เข้าถึงข้อมูล process อย่างถูกต้อง
//       return response;
//     } catch (error) {
//       console.error("Error fetching process groups:", error);
//       throw error;
//     }
//   };

//   const Process = async () => {
//     try {
//       const response = await axios.get("/process/fetch-process");
//       console.log("Fetched Data:", response.data.data.process); // ตรวจสอบข้อมูล
//       setProcessData(response.data.data.process); // เข้าถึงข้อมูล process อย่างถูกต้อง
//       return response;
//     } catch (error) {
//       console.error("Error fetching process groups:", error);
//       throw error;
//     }
//   };

//   const fetchPlprogress = async () => {
//     try {
//       const response = await axios.get("/plprogress/fetch-plprogress");
//       console.log("Fetched Data:", response.data.data.plprogress);
//       setPlProgressData(response.data.data.plprogress);
//       return response;
//     } catch (error) {
//       console.error("Error fetching plprogress groups:", error);
//       throw error;
//     }
//   };

//   const fetchSchedule = async () => {
//     try {
//       const response = await axios.get("/schedule/fetch-schedule");
//       console.log("Fetched Data:", response.data.data.schedule);
//       setScheduleData(response.data.data.schedule);
//       return response;
//     } catch (error) {
//       console.error("Error fetching schedule groups:", error);
//       throw error;
//     }
//   };

//   const fetchParts = async () => {
//     try {
//       const response = await axios.get("/parts/fetch-parts");
//       console.log("Fetched Data:", response.data.data.parts);
//       setPartsData(response.data.data.parts);
//       return response;
//     } catch (error) {
//       console.error("Error fetching parts groups:", error);
//       throw error;
//     }
//   };

//   useEffect(() => {
//     QM_Process();
//     Process();
//     fetchPlprogress();
//     fetchSchedule();
//     fetchParts();
//   }, []);

//   return (
//     <PlanContext.Provider
//       value={{
//         planData,
//         setPlanData,
//         selectedPlanNo,
//         setSelectedPlanNo,
//         searchPartsData,
//         selectPartsData,
//         qmprocessData,
//         processData,
//         plprogressData,
//         setPlProgressData,
//         ScheduleData,
//         setScheduleData,
//         PartsData,
//       }}
//     >
//       {children}
//     </PlanContext.Provider>
//   );
// }

import { useState, createContext, useEffect } from "react";
import axios from "../configs/axios";
import Swal from "sweetalert2";
export const PlanContext = createContext();

export default function PlanContextProvider({ children }) {
  const [planData, setPlanData] = useState([]);
  const [selectedPlanNo, setSelectedPlanNo] = useState([]);
  const [qmprocessData, setQMprocessData] = useState(null);
  const [processData, setProcessData] = useState(null);
  const [plprogressData, setPlProgressData] = useState(null);
  const [ScheduleData, setScheduleData] = useState(null);
  const [PartsData, setPartsData] = useState(null);
  const [UnitsData, setUnitsData] = useState(null);
  const [PPData, setPPData] = useState(null);
  const [PDData, setPDData] = useState(null);
  const [RDData, setRDData] = useState(null);
  const [ProcessGData, setProcessGData] = useState(null);
  const [TMProcessData, setTMProcessData] = useState(null);
  const [graphData, setGraphData] = useState([]);
  const [ProGData, setProGData] = useState([]);
  const [BaseCopyPlanData, setBaseCopyPlanData] = useState(null);
  const [ConnectData, setConnectData] = useState([]);
  const [QuotleData, setQuotleData] = useState(null);
  const [StatusData, setStatusData] = useState(() => {
    const storedData = localStorage.getItem("StatusData");
    return storedData
      ? JSON.parse(storedData)
      : { Settles: true, Settles_Day: "", Graph: true, List: true };
  });

  const [ProcessSheetAllPlanData, setProcessSheetAllPlanData] = useState(null);
  const [ProcessSheet1PData, setProcessSheet1PData] = useState(null);

  const fetch_All_Plan = async () => {
    try {
      const response = await axios.get("/plan/fetch-all-plan");
      setPlanData(response.data.data.plan);
      return response;
    } catch (error) {
      console.error("Error fetching Plans:", error);
      throw error;
    }
  };

  const searchPartsData = async (orderNo) => {
    try {
      const response = await axios.post("/plan/search-order-plan", {
        Order_No: orderNo,
      });

      if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data.partsNo)
      ) {
        setSelectedPlanNo(response.data.data.partsNo);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error fetching order data:", error);
      return false;
    }
  };

  const selectPartsData = async (orderNo, partsNO) => {
    try {
      const trimmedOrderNo = String(orderNo).trim().slice(0, 10);
      const trimmedPartsNo = String(partsNO).trim();

      const response = await axios.post("/plan/search-part-plan", {
        Order_No: trimmedOrderNo,
        Parts_No: trimmedPartsNo,
      });

      const plan = response?.data?.data?.plan;

      if (plan) {
        setPlanData(plan);
        // return เป็น array เพื่อให้ .find() ใช้ได้
        return Array.isArray(plan) ? plan : [plan]; // เผื่อ plan เป็น object เดี่ยว
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error fetching part data:", error);
      return [];
    }
  };

  const hasPlanData = async (orderNo, partsNO) => {
    try {
      // ใช้ trim() เพื่อลบช่องว่างข้างหน้าและข้างหลัง
      const trimmedOrderNo = orderNo.trim();
      const trimmedPartsNo = partsNO.trim();

      const response = await axios.post("/plan/search-part-plan", {
        Order_No: trimmedOrderNo,
        Parts_No: trimmedPartsNo,
      });

      if (response.data && response.data.data && response.data.data.plan) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error fetching part data:", error);
      return false;
    }
  };

  const hasConnectData = async (orderNo, partsNO) => {
    try {
      // ใช้ trim() เพื่อลบช่องว่างข้างหน้าและข้างหลัง
      const trimmedOrderNo = orderNo.trim();
      const trimmedPartsNo = partsNO.trim();

      const response = await axios.post("/plan/search-part-plan", {
        Order_No: trimmedOrderNo,
        Parts_No: trimmedPartsNo,
      });

      if (response.data && response.data.data && response.data.data.plan) {
        setConnectData(response.data.data.plan);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Error fetching part data:", error);
      return false;
    }
  };

  const QM_Process = async () => {
    try {
      const response = await axios.get("/process/fetch-qmprocess");
      setQMprocessData(response.data?.data?.process || []);
    } catch (error) {
      console.error("Error fetching QM process:", error);
    }
  };

  const Process = async () => {
    try {
      const response = await axios.get("/process/fetch-process");
      setProcessData(response.data?.data?.process || []);
    } catch (error) {
      console.error("Error fetching process:", error);
    }
  };

  const fetchPlprogress = async () => {
    try {
      const response = await axios.get("/plprogress/fetch-plprogress");
      setPlProgressData(response.data?.data?.plprogress || []);
    } catch (error) {
      console.error("Error fetching progress data:", error);
    }
  };

  const fetchUnits = async () => {
    try {
      const response = await axios.get("/unit/fetch-unit");
      setUnitsData(response.data?.data?.unit || []);
    } catch (error) {
      console.error("Error fetching unit data:", error);
    }
  };

  const fetchSchedule = async () => {
    try {
      const response = await axios.get("/schedule/fetch-schedule");
      setScheduleData(response.data?.data?.schedule || []);
    } catch (error) {
      console.error("Error fetching schedule data:", error);
    }
  };

  const fetchParts = async () => {
    try {
      const response = await axios.get("/parts/fetch-parts");
      setPartsData(response.data?.data?.parts || []);
    } catch (error) {
      console.error("Error fetching parts groups:", error);
      throw error;
    }
  };

  const createResult = async () => {
    try {
      const response = await axios.post("/plan/create-result", planData);
      console.log("result created successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error creating result:",
        error.response?.data || error.message
      );
      throw new Error("Failed to create result");
    }
  };

  const createWip = async () => {
    try {
      const response = await axios.post("/plan/create-wip", planData);
      console.log("wip created successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error creating wip:",
        error.response?.data || error.message
      );
      throw new Error("Failed to create wip");
    }
  };

  const createSchedule = async () => {
    try {
      const response = await axios.post("/plan/create-schedule", planData);
      console.log("schedule created successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error creating schedule:",
        error.response?.data || error.message
      );
      throw new Error("Failed to create schedule");
    }
  };

  const createPlan = async () => {
    try {
      const response = await axios.post("/plan/create-plan", planData);
      console.log("plan created successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error creating plan:",
        error.response?.data || error.message
      );
      throw new Error("Failed to create plan");
    }
  };

  const deleteWip = async () => {
    try {
      const response = await axios.post(`/plan/delete-wip`, planData);

      console.log("WIP deleted successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Error deleting WIP:",
        error.response?.data || error.message
      );
      throw new Error("Failed to delete WIP");
    }
  };
  const deleteSchedule = async () => {
    try {
      const response = await axios.post(`/plan/delete-schedule`, planData);

      console.log("Schedule deleted successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error deleting Schedule:", error);
      throw new Error("Failed to delete Schedule");
    }
  };

  const deletePlan = async () => {
    try {
      const response = await axios.post(`/plan/delete-plan`, planData);

      console.log("Plan deleted successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error deleting Plan:", error);
      throw new Error("Failed to delete Plan");
    }
  };

  const deleteResult = async () => {
    try {
      const response = await axios.post(`/plan/delete-result`, planData);

      console.log("Result deleted successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error deleting Result:", error);
      throw new Error("Failed to delete Result");
    }
  };

  const Schedule_Calc = async () => {
    try {
      const response = await axios.post("/plan/schedule-calc", planData);

      return response.data;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred";
      console.error("Error creating Schedule_Calc:", errorMessage);
      throw new Error("Failed to create Schedule_Calc");
    }
  };

  const Schedule_Bk = async () => {
    try {
      const response = await axios.post("/plan/schedule-bk", planData);

      setPlanData(response.data.data);

      return response.data;
    } catch (error) {
      console.error("Error creating Schedule_Bk:", error);
      throw new Error("Failed to create Schedule_Bk");
    }
  };

  const Schedule_Re = async () => {
    try {
      const response = await axios.post("/plan/schedule-re", planData);

      setPlanData(response.data.data);

      return response.data;
    } catch (error) {
      console.error("Error creating Schedule_Re:", error);
      throw new Error("Failed to create Schedule_Re");
    }
  };

  const PP_View = async (orderNo, partsNO) => {
    try {
      const response = await axios.post("/plan/pp-view", {
        Order_No: orderNo,
        Parts_No: partsNO,
      });
      setPPData(response.data);

      return response;
    } catch (error) {
      console.error("Error creating Schedule_Re:", error);
      throw new Error("Failed to create Schedule_Re");
    }
  };

  const PD_View = async (orderNo, partsNO) => {
    try {
      const response = await axios.post("/plan/pd-view", {
        Order_No: orderNo,
        Parts_No: partsNO,
      });

      setPDData(response.data);

      return response;
    } catch (error) {
      console.error("Error creating Schedule_Re:", error);
      throw new Error("Failed to create Schedule_Re");
    }
  };

  const RD_View = async (orderNo, partsNO) => {
    try {
      const response = await axios.post("/plan/rd-view", {
        Order_No: orderNo,
        Parts_No: partsNO,
      });

      setRDData(response.data);

      return response;
    } catch (error) {
      console.error("Error creating Schedule_Re:", error);
      throw new Error("Failed to create Schedule_Re");
    }
  };

  const update_temdate = async (newDateValue, No) => {
    try {
      const updatedPlanData = {
        ...planData,
        [`PPD${No}`]: newDateValue,
      };

      const response = await axios.post(
        "/plan/update-temdate",
        updatedPlanData
      );

      return response.data;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred";
      console.error("Error creating Schedule_Calc:", errorMessage);
      throw new Error("Failed to create Schedule_Calc");
    }
  };

  const QR_ProG_Plan = async (statusData) => {
    try {
      const response = await axios.post("/plan/qr-prog-plan", statusData);
      setProGData(response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating QR_ProG_Plan:", error);
      throw new Error("Failed to create QR_ProG_Plan");
    }
  };

  const QR_ProG_Graph = async (statusData) => {
    try {
      const response = await axios.post("/plan/qr-prog-graph", statusData);
      setGraphData(response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating QR_ProG_Graph:", error);
      throw new Error("Failed to create QR_ProG_Graph");
    }
  };
  const Tg_ProcessG = async () => {
    try {
      const response = await axios.get("/plan/fetch-tg-processG");

      setProcessGData(response.data);

      return response.data;
    } catch (error) {
      console.error("Error creating Tg_ProcessG:", error);
      throw new Error("Failed to create Tg_ProcessG");
    }
  };

  const TM_Process = async () => {
    try {
      const response = await axios.get("/plan/fetch-tm-process");

      setTMProcessData(response.data);

      return response.data;
    } catch (error) {
      console.error("Error creating Tg_ProcessG:", error);
      throw new Error("Failed to create Tg_ProcessG");
    }
  };

  const TM_Quotle = async () => {
    try {
      const response = await axios.get("/quotle/fetch-tm-quotle");

      setQuotleData(response.data.data.quote);

      return response;
    } catch (error) {
      console.error("Error creating TM_Quotle:", error);
      throw new Error("Failed to create TM_Quotle");
    }
  };

  const fetchProcessSheetAllPlan = async (orderNo, partNo = "00") => {
    try {
      const response = await axios.post("/planinfo-report/report-p-all", {
        orderNumber: orderNo,
        partNumber: partNo,
      });

      if (response.data && response.data.data && response.data.data.Sheet24) {
        setProcessSheetAllPlanData(response.data.data.Sheet24);
        return true;
      } else {
        console.log("No valid data found.");
        return false;
      }
    } catch (error) {
      console.error("Error fetching process sheet all plan data:", error);
      return false;
    }
  };

  const fetchProcessSheet1P = async (orderNo, partNo) => {
    try {
      if (!partNo) {
        console.warn("Missing partNo, request aborted.");
        return false;
      }

      const response = await axios.post("/planinfo-report/report-p-part", {
        orderNumber: orderNo,
        partNumber: partNo,
      });

      if (response.data && response.data.data && response.data.data.Sheet24) {
        setProcessSheet1PData(response.data.data.Sheet24);
        return true;
      } else {
        console.log("No valid data found.");
        return false;
      }
    } catch (error) {
      console.error("Error fetching process sheet all plan data:", error);
      return false;
    }
  };

  const fetchBaseCopyPlanSearch = async (orderNo) => {
    try {
      const response = await axios.post("/plan/fetch-base-copy-plan-search", {
        Order_No: orderNo,
      });

      if (response.data.message === "Fetch Order and Plan data successfully.") {
        setBaseCopyPlanData(response.data.data); // เก็บข้อมูลที่ได้จาก API
      } else {
        setBaseCopyPlanData(null); // ถ้าไม่พบข้อมูลที่ถูกต้อง
      }
    } catch (error) {
      console.error("Error fetching data", error);
    }
  };

  const Quote_Info_View = async (Pl_Quote_OdPt_No) => {
    try {
      const response = await axios.post("/plan/quotle-info-view", {
        Pl_Quote_OdPt_No,
      });

      if (response.data.status === "success") {
        const updates = response.data.updateFields || [];

        setPlanData((prevPlanData) => {
          const updatedPlanData = { ...prevPlanData }; // คัดลอกค่าเดิม
          updates.forEach((field) => {
            updatedPlanData[field.field] = field.newValue; // อัปเดตค่า
            updatedPlanData[`${field.field}_bg`] = field.backgroundColor; // อัปเดตสี
          });

          return updatedPlanData;
        });
      } else if (response.data.status === "warning") {
        Swal.fire({
          icon: "warning",
          title: "Warning",
          text: response.data.message,
          confirmButtonColor: "#f39c12", // สีปุ่ม OK (ส้ม)
        });
      }

      return response.data;
    } catch (error) {
      console.error("Error fetch Quote_Info_View:", error);
      throw new Error("Failed to fetch Quote_Info_View");
    }
  };

  const Execute = async (planData, selectedId) => {
    try {
      const executeData = {
        Edit_CAT1: planData?.Edit_CAT1,
        Edit_CAT2: planData?.Edit_CAT2,
        Edit_CAT3: planData?.Edit_CAT3,
        Edit_CAT4: planData?.Edit_CAT4,
        Insert_Pr_No: planData?.Insert_Pr_No || selectedId,
        Delete_Pr_No: planData?.Delete_Pr_No || selectedId,
        Mv_Paste_Pr_No: planData?.Mv_Paste_Pr_No,
        Mv_Cut_Pr_No: planData?.Mv_Cut_Pr_No || selectedId,
        Cp_Paste_Pr_No: planData?.Cp_Paste_Pr_No,
        Cp_Copy_Pr_No: planData?.Cp_Copy_Pr_No || selectedId,
      };
      const response = await axios.post(`/plan/execute`, executeData);

      return response.data;
    } catch (error) {
      console.error("Error deleting Schedule:", error);
      throw new Error("Failed to delete Schedule");
    }
  };

  const ConfirmExecute = async (planData, selectedId) => {
    try {
      const executeData = {
        ...planData,
        Edit_CAT1: planData?.Edit_CAT1,
        Edit_CAT2: planData?.Edit_CAT2,
        Edit_CAT3: planData?.Edit_CAT3,
        Edit_CAT4: planData?.Edit_CAT4,
        Insert_Pr_No: planData?.Insert_Pr_No || selectedId,
        Delete_Pr_No: planData?.Delete_Pr_No || selectedId,
        Mv_Cut_Pr_No: planData?.Mv_Cut_Pr_No || selectedId,
        Cp_Copy_Pr_No: planData?.Cp_Copy_Pr_No || selectedId,
      };
      const response = await axios.post(`/plan/confirm-execute`, executeData);
      setPlanData(response.data.data);
      return response.data;
    } catch (error) {
      console.error("Error deleting Schedule:", error);
      throw new Error("Failed to delete Schedule");
    }
  };

  const Money_Object = async () => {
    try {
      const response = await axios.post("/plan/money-object", planData);
      return response.data;
    } catch (error) {
      console.error("Error money:", error.response?.data || error.message);
      throw new Error("Failed to money");
    }
  };

useEffect(() => {
  const fetchAll = async () => {
    try {
      await Promise.all([
        QM_Process(),
        Process(),
        fetchPlprogress(),
        fetchSchedule(),
        fetchParts(),
        fetchUnits(),
        Tg_ProcessG(),
        TM_Process(),
        TM_Quotle()
      ]);
    } catch (error) {
      console.error("เกิดข้อผิดพลาดในการดึงข้อมูล:", error);
    }
  };

  fetchAll();
}, []);
  useEffect(() => {
    if (StatusData) {
      localStorage.setItem("StatusData", JSON.stringify(StatusData));
    }
  }, [StatusData]);

  return (
    <PlanContext.Provider
      value={{
        UnitsData,
        planData,
        setPlanData,
        selectedPlanNo,
        setSelectedPlanNo,
        searchPartsData,
        selectPartsData,
        fetch_All_Plan,
        qmprocessData,
        processData,
        plprogressData,
        setPlProgressData,
        ScheduleData,
        setScheduleData,
        PartsData,
        createResult,
        createWip,
        createSchedule,
        createPlan,
        deleteResult,
        deletePlan,
        deleteSchedule,
        deleteWip,
        Schedule_Calc,
        hasPlanData,
        update_temdate,
        Schedule_Bk,
        Schedule_Re,
        PP_View,
        setPPData,
        PPData,
        PD_View,
        PDData,
        RD_View,
        RDData,
        setStatusData,
        StatusData,
        QR_ProG_Graph,
        ProcessGData,
        QR_ProG_Plan,
        TMProcessData,
        graphData,
        ProGData,
        ProcessSheetAllPlanData,
        setProcessSheetAllPlanData,
        fetchProcessSheetAllPlan,
        ProcessSheet1PData,
        setProcessSheet1PData,
        fetchProcessSheet1P,
        fetchBaseCopyPlanSearch,
        BaseCopyPlanData,
        setBaseCopyPlanData,
        hasConnectData,
        ConnectData,
        QuotleData,
        setPartsData,
        Quote_Info_View,
        Execute,
        ConfirmExecute,
        Money_Object,
      }}
    >
      {children}
    </PlanContext.Provider>
  );
}
