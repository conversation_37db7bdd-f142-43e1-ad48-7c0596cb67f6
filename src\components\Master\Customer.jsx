import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function Customer() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [selectedCustomer, setSelectedCustomer] = useState([]); // State for selected customer CDs
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const fetchCustomer = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/order/customer`);
      setData(response.data.data.customer || []);
    } catch (error) {
      console.error("Error fetching customer:", error);
    }
  };

  useEffect(() => {
    fetchCustomer();
  }, []);

  const [formData, setFormData] = useState({
    Customer_CD: "",
    Customer_Name: "",
    Customer_Name2: "",
    Customer_Abb: "",
    Customer_Add: "",
    Customer_Add2: "",
    Customer_Add3: "",
    Customer_Contact: "",
    Customer_TEL: "",
    Posting_Group: "",
    Payment_CD: "",
    Sl_Person_CD: "",
    Blocked: false,
    Customer_FAX: "",
    Branch_No: "",
    Nationality: "",
    VAT_Reg_No: "",
    Customer_Group: "",
    Customer_Remark: "",
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSaveCustomer = async (e) => {
    e.preventDefault();

    try {
      const token = localStorage.getItem("authToken");

      const response = await axios.post(
        `${apiUrl_4000}/customer/create-customer`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "Customer created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });

        // อัปเดตข้อมูลในตารางหลังจากสร้างสำเร็จ
        fetchCustomer();

        // ล้างค่าฟอร์ม
        setFormData({
          Customer_CD: "",
          Customer_Name: "",
          Customer_Name2: "",
          Customer_Abb: "",
          Customer_Add: "",
          Customer_Add2: "",
          Customer_Add3: "",
          Customer_Contact: "",
          Customer_TEL: "",
          Posting_Group: "",
          Payment_CD: "",
          Sl_Person_CD: "",
          Blocked: false,
          Customer_FAX: "",
          Branch_No: "",
          Nationality: "",
          VAT_Reg_No: "",
          Customer_Group: "",
          Customer_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        // ปิด Modal
        closeModal();
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create customer.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  // ฟังก์ชันเปิด modal
  const openModal = () => {
    if (selectedRowForCopy) {
      const { Customer_CD, ...rest } = selectedRowForCopy;
      setFormData({
        Customer_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        Customer_CD: "",
        Customer_Name: "",
        Customer_Name2: "",
        Customer_Abb: "",
        Customer_Add: "",
        Customer_Add2: "",
        Customer_Add3: "",
        Customer_Contact: "",
        Customer_TEL: "",
        Posting_Group: "",
        Payment_CD: "",
        Sl_Person_CD: "",
        Blocked: false,
        Customer_FAX: "",
        Branch_No: "",
        Nationality: "",
        VAT_Reg_No: "",
        Customer_Group: "",
        Customer_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  // ฟังก์ชันปิด modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleDeleteCustomer = async () => {
    // ตรวจสอบว่าได้เลือก customer หรือไม่
    if (selectedCustomer.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const customerList = selectedCustomer.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following customers?<br>Customer CDs: ${customerList}`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/customer/delete-customer`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedCustomer.map((cd) => ({ Customer_CD: cd })), // ส่งเป็นอาเรย์ของ { Customer_CD }
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: "The selected customers have been deleted.",
            icon: "success",
            confirmButtonText: "OK",
          });

          // รีเฟรชข้อมูล
          fetchCustomer();

          // รีเซ็ต selectedCustomer เป็นค่าว่าง
          setSelectedCustomer([]); // เคลียร์การเลือกทั้งหมด

          // รีเซ็ตสถานะ checkbox ใน UI
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ต checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteCustomer:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 1?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 1 หรือไม่?<br>データは編集されました。master 1 に戻りますか？"
          : "Do you want to go back to master 1?<br>คุณต้องการกลับไปที่หน้า master 1 หรือไม่?<br>master 1 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/master1");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleSave = async (customerCd) => {
    const payload = { Customer_CD: customerCd };
    let isEdited = false;

    Object.keys(editedData[customerCd] || {}).forEach((field) => {
      const newValue = editedData[customerCd]?.[field];
      const oldValue = data.find((row) => row.Customer_CD === customerCd)?.[
        field
      ];

      if (newValue !== oldValue) {
        payload[field] = newValue === "" ? null : newValue;
        isEdited = true;
      }
    });

    if (!isEdited) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    let isSaving = false;

    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const response = await axios.put(
        `${apiUrl_4000}/order/update-customer`,
        payload
      );

      const updatedData = [...data];
      const rowIndex = updatedData.findIndex(
        (row) => row.Customer_CD === customerCd
      );
      if (rowIndex !== -1) {
        Object.keys(payload).forEach((field) => {
          if (field !== "Customer_CD") {
            updatedData[rowIndex][field] = payload[field];
          }
        });
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "Customer data has been updated.",
      });
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const handleChange = (e, customerCd, field) => {
    setEditedData({
      ...editedData,
      [customerCd]: {
        ...editedData[customerCd],
        [field]: e.target.value,
      },
    });
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      Customer_CD: row.Customer_CD,
      Customer_Name: row.Customer_Name,
      Customer_Name2: row.Customer_Name2,
      Customer_Abb: row.Customer_Abb,
      Customer_Add: row.Customer_Add,
      Customer_Add2: row.Customer_Add2,
      Customer_Add3: row.Customer_Add3,
      Customer_Contact: row.Customer_Contact,
      Customer_TEL: row.Customer_TEL,
      Posting_Group: row.Posting_Group,
      Payment_CD: row.Payment_CD,
      Sl_Person_CD: row.Sl_Person_CD,
      Blocked: row.Blocked,
      Customer_FAX: row.Customer_FAX,
      Branch_No: row.Branch_No,
      Nationality: row.Nationality,
      Customer_Group: row.Customer_Group,
      VAT_Reg_No: row.VAT_Reg_No,
      Customer_Remark: row.Customer_Remark,
    }));

    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "Customer_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxChange = (e, customerId) => {
    if (e.target.checked) {
      setSelectedCustomer((prev) => [...prev, customerId]); // เพิ่ม Customer_CD ลงใน selectedCustomer
    } else {
      setSelectedCustomer((prev) => prev.filter((id) => id !== customerId)); // ลบ Customer_CD ออกจาก selectedCustomer
    }
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy &&
            selectedRowForCopy.Customer_CD === row.Customer_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            onChange={(e) => handleCheckboxChange(e, row.Customer_CD)} // ใช้ Customer_CD ในการจัดการ
          />
        ) : null,
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "Customer_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.Customer_CD]?.Customer_CD || row.Customer_CD || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_CD")}
          disabled
        />
      ),
      width: "190px",
    },
    {
      name: "Customer_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{ width: "fit-content", minWidth: "350px", maxWidth: "100%" }}
          value={
            editedData[row.Customer_CD]?.Customer_Name ||
            row.Customer_Name ||
            ""
          }
          disabled={!isF2Pressed}
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Name")}
        />
      ),
      width: "400px",
    },
    {
      name: "Customer_Name2",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Customer_Name2 ||
            row.Customer_Name2 ||
            ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Name2")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Customer_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Customer_Abb !== undefined
              ? editedData[row.Customer_CD]?.Customer_Abb
              : row.Customer_Abb || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "210px",
    },
    {
      name: "Customer_Add",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "350px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_Add !== undefined
              ? editedData[row.Customer_CD]?.Customer_Add
              : row.Customer_Add || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Add")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
    {
      name: "Customer_Add2",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "350px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_Add2 !== undefined
              ? editedData[row.Customer_CD]?.Customer_Add2
              : row.Customer_Add2 || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Add2")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
    {
      name: "Customer_Add3",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "350px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_Add3 !== undefined
              ? editedData[row.Customer_CD]?.Customer_Add3
              : row.Customer_Add3 || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Add3")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
    {
      name: "Customer_Contact",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "300px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_Contact !== undefined
              ? editedData[row.Customer_CD]?.Customer_Contact
              : row.Customer_Contact || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Contact")}
          onKeyDown={(e) =>
            handleKeyDown(e, row.Customer_CD, "Customer_Contact")
          }
          disabled={!isF2Pressed}
        />
      ),
      width: "350px",
    },
    {
      name: "Customer_TEL",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Customer_TEL !== undefined
              ? editedData[row.Customer_CD]?.Customer_TEL
              : row.Customer_TEL || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_TEL")}
          disabled={!isF2Pressed}
        />
      ),
      width: "200px",
    },
    {
      name: "Posting_Group",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Posting_Group !== undefined
              ? editedData[row.Customer_CD]?.Posting_Group
              : row.Posting_Group || ""
          }
          maxLength={10} // จำกัดจำนวนตัวอักษรไม่เกิน 10 ตัว
          onChange={(e) => {
            const value = e.target.value;
            // ตรวจสอบให้ใส่ได้เฉพาะตัวอักษรและตัวเลข
            if (/^[a-zA-Z0-9]*$/.test(value)) {
              handleChange(e, row.Customer_CD, "Posting_Group");
            }
          }}
          onKeyDown={(e) => handleKeyDown(e, row.Customer_CD, "Posting_Group")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },

    {
      name: "Payment_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Payment_CD !== undefined
              ? editedData[row.Customer_CD]?.Payment_CD
              : row.Payment_CD || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Payment_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Sl_Person_CD",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Sl_Person_CD !== undefined
              ? editedData[row.Customer_CD]?.Sl_Person_CD
              : row.Sl_Person_CD || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Sl_Person_CD")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Blocked",
      selector: (row, index) => (
        <input
          type="checkbox"
          checked={row.Blocked}
          onChange={(e) => handleEdit(index, "Blocked", e.target.checked)}
          style={{ pointerEvents: "none" }}
          className="mx-auto"
        />
      ),
      width: "130px",
    },
    {
      name: "Customer_FAX",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "250px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_FAX !== undefined
              ? editedData[row.Customer_CD]?.Customer_FAX
              : row.Customer_FAX || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_FAX")}
          disabled={!isF2Pressed}
        />
      ),
      width: "300px",
    },
    {
      name: "Branch_No",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Branch_No !== undefined
              ? editedData[row.Customer_CD]?.Branch_No
              : row.Branch_No || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Branch_No")}
          onInput={(e) => {
            const value = e.target.value;
            // ตรวจสอบให้กรอกแค่ตัวอักษรหรือตัวเลขหนึ่งตัว
            if (value.length > 1) {
              e.target.value = value.slice(0, 1); // เก็บแค่ตัวแรก
            }
            // ตรวจสอบให้กรอกได้แค่ตัวอักษร (A-Z, a-z) หรือ ตัวเลข (0-9)
            if (!/^[a-zA-Z0-9]*$/.test(e.target.value)) {
              e.target.value = value.slice(0, -1); // ถ้าไม่ใช่ตัวอักษรหรือตัวเลขให้ลบออก
            }
          }}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },

    {
      name: "Nationality",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Nationality !== undefined
              ? editedData[row.Customer_CD]?.Nationality
              : row.Nationality || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Nationality")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "VAT_Reg_No",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.VAT_Reg_No !== undefined
              ? editedData[row.Customer_CD]?.VAT_Reg_No
              : row.VAT_Reg_No || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "VAT_Reg_No")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Customer_Group",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.Customer_CD]?.Customer_Group !== undefined
              ? editedData[row.Customer_CD]?.Customer_Group
              : row.Customer_Group || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Group")}
          disabled={!isF2Pressed}
        />
      ),
      width: "180px",
    },
    {
      name: "Customer_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "350px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.Customer_CD]?.Customer_Remark !== undefined
              ? editedData[row.Customer_CD]?.Customer_Remark
              : row.Customer_Remark || ""
          }
          onChange={(e) => handleChange(e, row.Customer_CD, "Customer_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "400px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                Customer <br /> (受注一覧)
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New Customer</h2>
                  <form onSubmit={handleSaveCustomer}>
                    {/* Customer_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_CD
                      </label>
                      <input
                        type="text"
                        name="Customer_CD"
                        value={formData.Customer_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Name
                      </label>
                      <input
                        type="text"
                        name="Customer_Name"
                        value={formData.Customer_Name}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Name2 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Name2
                      </label>
                      <input
                        type="text"
                        name="Customer_Name2"
                        value={formData.Customer_Name2}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Abb
                      </label>
                      <input
                        type="text"
                        name="Customer_Abb"
                        value={formData.Customer_Abb}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Add */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Add
                      </label>
                      <input
                        type="text"
                        name="Customer_Add"
                        value={formData.Customer_Add}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Add2 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Add2
                      </label>
                      <input
                        type="text"
                        name="Customer_Add2"
                        value={formData.Customer_Add2}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Add3 */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Add3
                      </label>
                      <input
                        type="text"
                        name="Customer_Add3"
                        value={formData.Customer_Add3}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Contact */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Contact
                      </label>
                      <input
                        type="text"
                        name="Customer_Contact"
                        value={formData.Customer_Contact}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_TEL */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_TEL
                      </label>
                      <input
                        type="text"
                        name="Customer_TEL"
                        value={formData.Customer_TEL}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Posting_Group */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Posting_Group
                      </label>
                      <input
                        type="text"
                        name="Posting_Group"
                        value={formData.Posting_Group}
                        maxLength={10} // จำกัดจำนวนตัวอักษรไม่เกิน 10 ตัว
                        onChange={(e) => {
                          const value = e.target.value;
                          // ตรวจสอบให้ใส่ได้เฉพาะตัวอักษรและตัวเลข
                          if (/^[a-zA-Z0-9]*$/.test(value)) {
                            handleInputChange(e);
                          }
                        }}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Payment_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Payment_CD
                      </label>
                      <input
                        type="text"
                        name="Payment_CD"
                        value={formData.Payment_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Sl_Person_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Sl_Person_CD
                      </label>
                      <input
                        type="text"
                        name="Sl_Person_CD"
                        value={formData.Sl_Person_CD}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Blocked */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Blocked
                      </label>
                      <input
                        type="checkbox"
                        name="Blocked"
                        checked={formData.Blocked}
                        onChange={handleInputChange}
                        className="mx-auto"
                      />
                    </div>

                    {/* Customer_FAX */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_FAX
                      </label>
                      <input
                        type="text"
                        name="Customer_FAX"
                        value={formData.Customer_FAX}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Branch_No */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Branch_No
                      </label>
                      <input
                        type="text"
                        name="Branch_No"
                        value={formData.Branch_No}
                        maxLength={1} // จำกัดให้พิมพ์ได้เพียง 1 ตัวอักษร
                        onChange={(e) => {
                          const value = e.target.value;
                          if (/^[a-zA-Z0-9]*$/.test(value)) {
                            // ตรวจสอบให้พิมพ์ได้เฉพาะตัวอักษรหรือตัวเลข
                            setFormData((prevData) => ({
                              ...prevData,
                              Branch_No: value,
                            }));
                          }
                        }}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Nationality */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Nationality
                      </label>
                      <input
                        type="text"
                        name="Nationality"
                        value={formData.Nationality}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* VAT_Reg_No */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        VAT_Reg_No
                      </label>
                      <input
                        type="text"
                        name="VAT_Reg_No"
                        value={formData.VAT_Reg_No}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Group */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Group
                      </label>
                      <input
                        type="text"
                        name="Customer_Group"
                        value={formData.Customer_Group}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    {/* Customer_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Customer_Remark
                      </label>
                      <textarea
                        name="Customer_Remark"
                        value={formData.Customer_Remark}
                        onChange={handleInputChange}
                        className="w-full p-2 border rounded-md h-24"
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.Customer_CD]?.[field] !== undefined) {
                      handleSave(row.Customer_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteCustomer}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
