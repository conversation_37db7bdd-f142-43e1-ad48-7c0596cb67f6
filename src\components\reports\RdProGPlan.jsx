import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import html2pdf from "html2pdf.js";
import "../fonts/CODE39.ttf";
import "../fonts/Helvetica.ttf";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { useProcessGPlan } from "../../hooks/use-processgplan";
import { usePlan } from "../../hooks/use-plan";
import { useOrder } from "../../hooks/use-order";
import { font } from "../fonts/font";
export default function RdProGPlan() {
  const navigate = useNavigate();
  const reportRef = useRef();
  const location = useLocation();
  const urlParams = new URLSearchParams(window.location.search);
  const dataString = urlParams.get("data");
  const styles = {
    fontFamily: "CODE39",
  };
  const [processGCD, setProcessGCD] = useState("N/A");
  const { TTprocessGData } = useProcessGPlan();
  const {
    planData,
    StatusData,
    QR_ProG_Plan,
    ProGData,
    PartsData,
    TMProcessData,
    setStatusData,
  } = usePlan();
  const {
    CustomerData,
    DeliveryData,
    TargetData,
    WorkerData,
    CoatingData,
    Request3Data,
  } = useOrder();

  useEffect(() => {
    if (dataString) {
      try {
        const parsedData = JSON.parse(decodeURIComponent(dataString));
        setStatusData(parsedData);
      } catch (error) {
        console.error("Error parsing data:", error);
      }
    } else if (StatusData) {
      setStatusData(StatusData);
    }
  }, []);

  const getCurrentDateTime = () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, "0");
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const year = now.getFullYear();
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const updatedStatusData = {
    Mark_Days: StatusData?.Mark_Days,
    TG_ProcessG: StatusData?.TG_ProcessG,
    TG_Process: StatusData?.TG_Process,
    Result_Search: StatusData?.Result_Search,
    Print_Object: StatusData?.Print_Object,
    Tg_St_Pl_Date: StatusData?.Tg_St_Pl_Date,
    Tg_Ed_Pl_Date: StatusData?.Tg_Ed_Pl_Date,
    Tg_St_Rs_Date: StatusData?.Tg_St_Rs_Date,
    Tg_Ed_Rs_Date: StatusData?.Tg_Ed_Rs_Date,
    S_Customer_CD1: StatusData?.S_Customer_CD1,
    S_Customer_CD2: StatusData?.S_Customer_CD2,
    S_Customer_CD3: StatusData?.S_Customer_CD3,
    S_No_Customer_CD: StatusData?.S_No_Customer_CD,
    S_Customer_Name1: StatusData?.S_Customer_Name1,
    S_Customer_Name2: StatusData?.S_Customer_Name2,
    S_Customer_Name3: StatusData?.S_Customer_Name3,
    S_Item1_CD: StatusData?.S_Item1_CD,
    S_St_Od_Progress_CD: StatusData?.S_St_Od_Progress_CD,
    S_Ed_Od_Progress_CD: StatusData?.S_Ed_Od_Progress_CD,
    S_St_Pl_Progress_CD: StatusData?.S_St_Pl_Progress_CD,
    S_Ed_Pl_Progress_CD: StatusData?.S_Ed_Pl_Progress_CD,
    S_Od_Pending: StatusData?.S_Od_Pending,
    S_Parts_Pending: StatusData?.S_Parts_Pending,
    S_St_Parts_No: StatusData?.S_St_Parts_No,
    S_Ed_Parts_No: StatusData?.S_Ed_Parts_No,
  };

  const dataKeys = Object.keys(updatedStatusData);

  useEffect(() => {
    if (dataKeys.length > 0) {
      QR_ProG_Plan(updatedStatusData);
    }
  }, [dataKeys.join(",")]);

  function formatDate2(dateString) {
    if (!dateString) return ""; // กัน error กรณีไม่มีค่า
    const date = new Date(dateString);
    const month = date.getMonth() + 1; // เดือนเริ่มที่ 0 ต้องบวก 1
    const day = date.getDate();
    return `${month}/${day}`;
  }

  const uniqueProgData = Array.from(
    new Set(ProGData.map((item) => item.PPG))
  ).map((ppg) => ProGData.filter((item) => item.PPG === ppg));

  const chunkedDataByPPG = uniqueProgData.map((ppgData) => {
    const itemsPerPage = 13;
    const chunkedRows = [];
    for (let i = 0; i < ppgData.length; i += itemsPerPage) {
      chunkedRows.push(ppgData.slice(i, i + itemsPerPage));
    }

    return chunkedRows;
  });

  const pagesWithHeaders = uniqueProgData.flatMap((ppgData) => {
    const itemsPerPage = 13;
    const header = ppgData[0]?.PPG; // หัวกลุ่ม PPG จากรายการแรกใน ppgData
    const pages = [];
    const PPGAbbForRow = (TTprocessGData || [])
      .filter((PPGAbb) => PPGAbb.ProcessG_CD === header)
      .map((PPGAbb) => PPGAbb.ProcessG_Abb);

    for (let i = 0; i < ppgData.length; i += itemsPerPage) {
      pages.push({
        header: header, // เก็บ header เดียวกันสำหรับหน้าทั้งหมดของกลุ่มนี้
        pggabb: PPGAbbForRow,
        data: ppgData.slice(i, i + itemsPerPage),
      });
    }
    return pages;
  });
  let previousDate = "";
  const rows = chunkedDataByPPG.flatMap((ppgChunks) =>
    ppgChunks.map((chunk) =>
      chunk.map((item) => {
        const instructions =
          StatusData?.Info_View === true ? item.Pt_Instructions : "";
        const Information =
          StatusData?.Info_View === true ? item.Pt_Information : "";
        const formatString = (value) =>
          Array.isArray(value) ? value.join(", ") : String(value ?? "");
        const ISN = formatString(instructions) + formatString(Information);
        const DeliveryMarkForRow = (DeliveryData || [])
          .filter((Delivery) => Delivery.Delivery_CD === item.Delivery_CD)
          .map((Delivery) => Delivery.Delivery_Mark);
        const TargetSymbolForRow = (TargetData || [])
          .filter((Target) => Target.Target_CD === item.Target_CD)
          .map((Target) => Target.Target_Symbol);
        const WorkerAbbForRow = (WorkerData || [])
          .filter((Worker) => Worker.Worker_CD === item.Sales_Person_CD)
          .map((Worker) => Worker.Worker_Abb);
        const CoatingForRow = (CoatingData || [])
          .filter((Coating) => Coating.Coating_CD === item.Coating_CD)
          .map((Coating) => Coating.Coating_Symbol);
        const Request3ForRow = (Request3Data || [])
          .filter((Request3) => Request3.Request3_CD === item.Request3_CD)
          .map((Request3) => Request3.Request3_Symbol);
        const PartsForRow = (PartsData || [])
          .filter((Parts) => Parts.Parts_CD === item.Parts_CD)
          .map((Parts) => Parts.Parts_Abb);
        const ProcessForRow = (TMProcessData || [])
          .filter((Process) => Process.Process_CD === item.PPC)
          .map((Process) => Process.Process_Abb);
        const inputs = Array.from({ length: 36 }, (_, i) => i + 1);
        const ProcessForRowArray = inputs.map((id) => {
          return (TMProcessData || [])
            .filter((Process) => Process.Process_CD === item["PPC" + id])
            .map((Process) => Process.Process_Abb);
        });
        let formattedDate = formatDate2(item.PPD);
        if (formattedDate === previousDate) {
          formattedDate = "";
        } else {
          previousDate = formattedDate;
        }
        const processData = ProcessForRowArray.flatMap((processList, index) =>
          processList.map((processAbb) => ({
            process1: processAbb,
            RPD: formatDate2(item["RPD" + inputs[index]] || ""),
            PPD: formatDate2(item["PPD" + inputs[index]] || ""),
            process3: item["RPD" + inputs[index]],
          }))
        );
        return {
          Line_No: item.Line_No,
          process: item.Now_Process,
          pds: item.WorkG_Mark,
          planDate: formattedDate,
          pdsDeli: formatDate2(item.Product_Delivery),
          mark: DeliveryMarkForRow,
          orderno: item.Order_No,
          partno: item.Parts_No,
          orderPartsNo: item.OdPt_No,
          target: TargetSymbolForRow,
          coating: item.Money_Object === true ? item.Coating : "",
          no: "",
          customerProductionName1: item.Customer_Abb,
          customerProductionName2: item.Product_Name,
          cat1: WorkerAbbForRow,
          cat2: CoatingForRow,
          cat3: Request3ForRow,
          ptNameMaterial: PartsForRow,
          ptNameMaterial2: item.Pt_Material,
          planQty: item.Pt_Split
            ? `${item.Pt_Qty}/${item.Quantity}`
            : item.Pt_Qty,
          spare:
            item.Pt_Spare_Qty - item.Pt_NG_Qty === 0
              ? ""
              : item.Pt_Spare_Qty - item.Pt_NG_Qty > 0
              ? `+${item.Pt_Spare_Qty - item.Pt_NG_Qty}`
              : `${item.Pt_Spare_Qty - item.Pt_NG_Qty}`,
          thisPlan: `No${item.Pro_No}`,
          thisPlan2: item.Pro_No,
          ship: ProcessForRow,
          mSetPSet: { main: item.PML, sub: item.PPL },
          processData,
          ptNoteInfo: [ISN],
        };
      })
    )
  );

  const handleViewPDF = () => {
    const doc = new jsPDF("landscape", "mm", "a4");

    // ประมวลผลวันที่เริ่มต้นและสิ้นสุด
    const startDate = StatusData?.Tg_St_Pl_Date
      ? `${
          new Date(StatusData.Tg_St_Pl_Date).toISOString().split("T")[0]
        } ${new Date().toLocaleTimeString("en-GB")}`
      : "";

    const endDate = StatusData?.Tg_Ed_Pl_Date
      ? `${
          new Date(StatusData.Tg_Ed_Pl_Date).toISOString().split("T")[0]
        } ${new Date().toLocaleTimeString("en-GB")}`
      : "";

    // วันที่สร้าง
    const CreateDate = getCurrentDateTime();
    const dateRange = `${startDate} ~ ${endDate}`;

    // ใส่ฟอนต์ CODE39
    doc.addFileToVFS("CODE39.ttf");
    doc.addFont("CODE39.ttf", "CODE39", "normal");
    rows.map((pageRows, pageIndex) => {
      if (pageIndex > 0) {
        doc.addPage();
      }

      // PPG Data
      const PPGHead = pagesWithHeaders[pageIndex].header || "N/A";
      const PPGAbbs = pagesWithHeaders[pageIndex].pggabb || "N/A";
      const safePPGAbbs = String(PPGAbbs);
      // กำหนดฟอนต์และสี
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);

      // หัวข้อ "Target_Plan_Process_Date"
      doc.text("Target_Plan_Process_Date:", 10, 10);
      doc.setFont("CODE39", "normal");
      doc.setFontSize(8);
      doc.setTextColor(0, 0, 0);
      doc.text(dateRange, 53, 10);

      // หัวข้อ "Create_Date"
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);
      doc.text("Create_Date:", 200, 10);
      doc.setFont("CODE39", "normal");
      doc.setFontSize(8);
      doc.text(CreateDate, 222, 10);

      // เลขหน้า
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);
      doc.text(`Page: ${pageIndex + 1} / ${rows.length}`, 270, 10);

      // หัวข้อ Process_Grp
      doc.setFont("CODE39", "bold");
      doc.setFontSize(12);
      doc.setTextColor(30, 64, 175);
      doc.text("Process_Grp:", 10, 30);

      // กำหนดค่ากรอบ
      const rects = [
        { x: 37, y: 25, width: 30, height: 10, text: PPGHead },
        { x: 70, y: 25, width: 40, height: 10, text: safePPGAbbs },
      ];

      rects.forEach(({ x, y, width, height, text }) => {
        // วาดกรอบ
        doc.setDrawColor(0);
        doc.setLineWidth(0);
        doc.rect(x, y, width, height);

        // กำหนดฟอนต์และขนาด
        doc.setFont("CODE39", "bold");
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);

        // คำนวณตำแหน่ง X (ให้อยู่กลางแนวนอน)
        const textWidth = doc.getTextWidth(text);
        const textX = x + (width - textWidth) / 2;

        // คำนวณตำแหน่ง Y (ให้อยู่กลางแนวตั้ง)
        const textHeight = doc.getTextDimensions(text).h; // ค่าความสูงของข้อความ
        const textY = y + (height + textHeight) / 2 - 1;

        // วาดข้อความ
        doc.text(text, textX, textY);
      });
      doc.setFont("CODE39", "bold");
      doc.setFontSize(12);
      doc.setTextColor(30, 64, 175);
      doc.text("Process_Grp_Plan_List:", 135, 20);

      const ListG = [
        {
          x: 120,
          y: 26,
          width: 20,
          height: 8,
          text: "Self",
          color: [239, 68, 68],
        },
        {
          x: 150,
          y: 26,
          width: 25,
          height: 8,
          text: "1Before",
          color: [249, 115, 22],
        },
        {
          x: 185,
          y: 26,
          width: 25,
          height: 8,
          text: "2Before",
          color: [255, 204, 0],
        },
      ];

      ListG.forEach(({ x, y, width, height, text, color }) => {
        // กำหนดสีพื้นหลัง
        doc.setFillColor(...color);
        doc.rect(x, y, width, height, "F"); // "F" = Filled rectangle

        // กำหนดฟอนต์และขนาด
        doc.setFont("helvetica", "bold");
        doc.setFontSize(10);
        doc.setTextColor(255, 255, 255); // สีตัวอักษรเป็นขาว

        // คำนวณตำแหน่ง X (ให้อยู่กลางแนวนอน)
        const textWidth = doc.getTextWidth(text);
        const textX = x + (width - textWidth) / 2;

        // คำนวณตำแหน่ง Y (ให้อยู่กลางแนวตั้ง)
        const textHeight = doc.getTextDimensions(text).h;
        const textY = y + (height + textHeight) / 2 - 1;

        // วาดข้อความ
        doc.text(text, textX, textY);
      });

      const head = [
        [
          {
            content: "Plan_Date",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PDS_Deli",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Order_Parts_No",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          // สมมุติว่า cell นี้มี index = 3
          {
            content: "Customer/Production_Name",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PT_Name Material",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Plan Qty",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "This Plan",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "M_Set P_Set",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Process",
            colSpan: 24,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PT_Note/Info",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
        ],
        [
          ...Array.from({ length: 24 }, (_, i) => ({
            content: `${i + 1}`,
            styles: { halign: "center", valign: "middle" },
          })),
        ],
      ];

      const body = pageRows.map((row, rowIndex) => {
        // ขยาย processData ให้มีจำนวน 24 ค่า โดยถ้า row.processData มีค่าน้อยกว่านั้นจะเติมด้วย object ว่าง
        const processData = [
          ...row.processData,
          ...Array.from({ length: 24 - row.processData.length }, () => ({
            process1: "",
            process2: "",
          })),
        ].slice(0, 24);
        const targetValue = encodeURIComponent(String(row.target || ""));
        // กำหนดสีพื้นหลังสลับกัน: ถ้าเป็นแถวคู่ให้ใช้สี "#cffff9" ถ้าไม่ใช่ใช้สี "#ffffff"
        const rowColor = rowIndex % 2 === 0 ? "#cffff9" : "#ffffff";

        let ColorProcess = rowIndex % 2 === 0 ? "#cffff9" : "#ffffff";
        if (StatusData?.Info_View === true) {
          if (StatusData?.Color_Separate === true) {
            if (row.process === "2Before") {
              ColorProcess = "#FFCC00";
            } else if (row.process === "1Before") {
              ColorProcess = "#F97316";
            } else if (row.process === "0Self") {
              ColorProcess = "#EF4444";
            } else {
              ColorProcess = "#FFFFFF";
            }
          }
        }

        return [
          {
            content: ``,
            styles: {
              fillColor: rowColor,
            },
          },
          // แถวที่ 2: pdsDeli, mark
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "center", valign: "middle" },
          },

          // แถวที่ 3: orderPartsNo, target, coating
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "center", valign: "middle" },
          },

          {
            content: `${row.customerProductionName1 || ""}\n${
              row.customerProductionName2 || ""
            } `,
            styles: {
              fillColor: rowColor,
              halign: "left",
              valign: "middle",
              maxWidth: 32,
              fontSize: 5,
            },
          },

          {
            content: `${row.ptNameMaterial || ""}\n${
              row.ptNameMaterial2 || ""
            } `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },

          {
            content: `${row.planQty || ""}\n${row.spare || ""} `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 6,
            },
          },

          {
            content: `${row.thisPlan || ""}\n${row.ship || ""} `,
            styles: {
              fillColor: ColorProcess,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },
          {
            content: `${row.mSetPSet.main}\n ${row.mSetPSet.sub || 0} `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },
          ...processData.map((process, procIndex) => ({
            content: ``,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
            },
          })),
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "left", valign: "middle" },
          },
        ];
      });

      autoTable(doc, {
        head,
        body,
        startX: 0,
        startY: 40,
        tableWidth: "100%",
        margin: { left: 5, right: 5 },
        styles: {
          fontSize: 4,
          textColor: [0, 0, 0],
          lineColor: [30, 64, 175],
          lineWidth: 0.2,
          cellLineStyle: "dashed",
          cellLineWidth: 0.2,
        },
        headStyles: {
          textColor: [30, 64, 175],
          fillColor: [255, 255, 255],
        },
        tableLineWidth: 0.3,
        tableLineColor: [30, 64, 175],
        columnStyles: {
          0: { cellWidth: 12 },
          1: { cellWidth: 12 },
          2: { cellWidth: 17 },
          3: { cellWidth: 32 },
          4: { cellWidth: 15 },
          5: { cellWidth: 10 },
          6: { cellWidth: 10 },
          7: { cellWidth: 10 },
          ...Array.from({ length: 24 }, (_, i) => ({
            [8 + i]: { cellWidth: 6.5 },
          })).reduce((acc, cur) => ({ ...acc, ...cur }), {}),
        },
        didDrawCell: function (data) {
          if (data.section === "head" && data.column.index === 3) {
            const cell = data.cell;

            // กำหนดตำแหน่งของ "CAT"
            const xPos = cell.x + cell.width - 1; // ปรับ margin ด้านขวา
            const yPos = cell.y + 2; // ปรับ margin ด้านบน

            // กำหนด font size
            doc.setFontSize(cell.styles.fontSize);

            // รีเซ็ตค่าเส้นให้กลับเป็นปกติ
            doc.setLineDashPattern([], 0);

            // วาดข้อความ CAT
            doc.text("CAT", xPos, yPos, { align: "right", baseline: "top" });
          }

          if (data.section === "body" && data.column.index === 3) {
            const cell = data.cell;
            const xPos = cell.x + cell.width - 0.5; // ชิดขวาสุด
            const yPos = cell.y + 2; // ชิดด้านบน

            const rowIndex = data.row.index;

            const cat1Text = pageRows[rowIndex]?.cat1 || "";
            const cat2Text = pageRows[rowIndex]?.cat2 || "";
            const cat3Text = pageRows[rowIndex]?.cat3 || "";

            doc.text(cat1Text, xPos, yPos, {
              align: "right",
              baseline: "top",
            });
            doc.text(
              [`${cat2Text}`, `${cat3Text}`],
              cell.x + cell.width - 2,
              cell.y + 5,
              { align: "right", baseline: "middle" }
            );
          }

          if (data.section === "body" && data.column.index === 0) {
            const cell = data.cell;

            const rowIndex = data.row.index;
            const planDate = pageRows[rowIndex]?.planDate || "";
            const pds = pageRows[rowIndex]?.pds || "";

            if (planDate.trim() !== "") {
              doc.setFillColor("#FFCC00");
              doc.rect(cell.x, cell.y, cell.width, cell.height / 2, "F");
            }
            doc.setTextColor(0, 0, 0);

            if (planDate) {
              doc.setFontSize(8);
              doc.text(planDate, cell.x + 6, cell.y + 1, {
                align: "center",
                baseline: "top",
              });
            }

            if (pds) {
              doc.setFontSize(6);
              doc.text(pds, cell.x + 6, cell.y + 6.8, {
                align: "center",
                baseline: "bottom",
              });
            }
          }
          if (data.section === "body" && data.column.index === 1) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const pdsDeli = pageRows[rowIndex]?.pdsDeli || "";
            const mark = pageRows[rowIndex]?.mark || "";

            if (pdsDeli) {
              doc.setFontSize(8);
              doc.text(pdsDeli, cell.x + 6, cell.y + 2, {
                align: "center",
                baseline: "top",
              });
            }

            if (mark) {
              doc.setFontSize(6);
              doc.text(mark, cell.x + 6, cell.y + 8, {
                align: "center",
                baseline: "bottom",
              });
            }
          }

          if (data.section === "body" && data.column.index === 32) {
            const cell = data.cell;
            const rowIndex = data.row.index;

            // แปลง ptNoteInfo เป็น string เสมอ และกำหนดค่าเริ่มต้นเป็น "" (ค่าว่าง)
            const ptNoteInfo = String(pageRows[rowIndex]?.ptNoteInfo ?? "");

            if (ptNoteInfo.trim() !== "") {
              doc.setFontSize(5);
              doc.text(ptNoteInfo, cell.x - 6.5, cell.y + 8.5, {
                align: "left",
                baseline: "bottom",
              });
            }
          }

          if (data.section === "body" && data.column.index === 2) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const orderno = pageRows[rowIndex]?.orderno || "";
            const partno = pageRows[rowIndex]?.partno || "";
            const target = pageRows[rowIndex]?.target || "";
            const coating = pageRows[rowIndex]?.coating || "";

            if (orderno) {
              const combinedText = `${orderno}-${partno}`;
              doc.setFontSize(6);
              doc.text(combinedText, cell.x + 8.5, cell.y + 2, {
                align: "center",
                baseline: "top",
              });
            }
            doc.addFileToVFS("segoeuithis.ttf", font);
            doc.addFont("segoeuithis.ttf", "segoeuithis", "normal");
            doc.setFont("segoeuithis");
            doc.setFontSize(6);
            doc.text(
              `${String(target)} ${String(coating)}`,
              cell.x + 1,
              cell.y + cell.height - 2,
              { align: "left", baseline: "bottom" }
            );
          }

          if (
            data.section === "body" &&
            data.column.index >= 8 &&
            data.column.index <= 31
          ) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const thisPlan2 = pageRows[rowIndex]?.thisPlan2 || "";

            const processData = Array.isArray(pageRows[rowIndex]?.processData)
              ? pageRows[rowIndex]?.processData
              : [];
            const extendedProcessData = [
              ...processData,
              ...Array.from({ length: 24 - processData.length }, () => ({
                process1: "",
                process2: "",
              })),
            ].slice(0, 24);

            const extendedProcessData2 = [
              ...processData,
              ...Array.from({ length: 35 - processData.length }, () => ({
                process1: "",
                process2: "",
              })),
            ].slice(25, 35);

            const processIndex = data.column.index - 7;
            if (extendedProcessData.length > processIndex) {
              const PP = extendedProcessData[processIndex]?.process1 || "";
              const PG =
                extendedProcessData[processIndex]?.RPD ||
                extendedProcessData[processIndex]?.PPD;
              const RPD = extendedProcessData[processIndex]?.process3 || "";

              if (StatusData?.Info_View === true) {
                if (StatusData?.Color_Separate === true) {
                  if (PP && PP.trim() !== "") {
                    if (processIndex === parseInt(thisPlan2)) {
                      doc.setFillColor("#FFCC00"); // สีเหลืองทอง
                      doc.rect(
                        cell.x,
                        cell.y + 0.2,
                        cell.width,
                        cell.height / 3,
                        "F"
                      );
                    } else {
                      doc.setFillColor("#CCFFFF"); // สีฟ้าอ่อน
                      doc.rect(
                        cell.x,
                        cell.y + 0.2,
                        cell.width,
                        cell.height / 3,
                        "F"
                      );
                    }
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 0.5, {
                      align: "center",
                      baseline: "top",
                    });

                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 2.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                } else {
                  if (PP && PP.trim() !== "") {
                    doc.setFillColor("#CCFFFF");
                    doc.rect(
                      cell.x,
                      cell.y + 0.2,
                      cell.width,
                      cell.height / 3,
                      "F"
                    );
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 0.5, {
                      align: "center",
                      baseline: "top",
                    });
                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 2.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                }
              } else {
                if (StatusData?.Color_Separate === true) {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#FFFF99");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 2.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                      align: "center",
                      baseline: "middle",
                    });
                  }
                } else {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 2.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                      align: "center",
                      baseline: "middle",
                    });
                  }
                }
              }
            }

            // line 25-36

            if (extendedProcessData2.length > processIndex) {
              const PP = extendedProcessData2[processIndex]?.process1 || "";
              const PG =
                extendedProcessData2[processIndex]?.RPD ||
                extendedProcessData2[processIndex]?.PPD;
              const RPD = extendedProcessData2[processIndex]?.process3 || "";

              if (StatusData?.Info_View === true) {
                if (StatusData?.Color_Separate === true) {
                  if (PP && PP.trim() !== "") {
                    if (processIndex === parseInt(thisPlan2)) {
                      doc.setFillColor("#FFCC00"); // สีเหลืองทอง
                      doc.rect(
                        cell.x,
                        cell.y + 4.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                    } else {
                      doc.setFillColor("#CCFFFF"); // สีฟ้าอ่อน
                      doc.rect(
                        cell.x,
                        cell.y + 4.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                    }
                    doc.text(PP, cell.x + 3.2, cell.y + 6.5, {
                      align: "center",
                      baseline: "bottom",
                    });

                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 6.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                } else {
                  if (PP && PP.trim() !== "") {
                    doc.setFillColor("#CCFFFF");
                    doc.rect(
                      cell.x,
                      cell.y + 4.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 4.5, {
                      align: "center",
                      baseline: "top",
                    });
                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 6.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                        align: "center",
                        baseline: "bottom",
                      });
                    }
                  }
                }
              } else {
                if (StatusData?.Color_Separate === true) {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#FFFF99");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 6.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                      align: "center",
                      baseline: "bottom",
                    });
                  }
                } else {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 6.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                      align: "center",
                      baseline: "bottom",
                    });
                  }
                }
              }
            }
          }
        },
      });
    });

    // สร้าง PDF
    const pdfBlob = doc.output("blob");
    const pdfUrl = URL.createObjectURL(pdfBlob);

    // เปิด PDF ในแท็บใหม่
    window.open(pdfUrl, "_blank");
  };





  return (
    <div className="flex bg-[#E9EFEC]  w-full p-2">
      <div className="flex flex-col w-full">
        <div className="flex justify-end p-4">
          <button
            onClick={handleViewPDF}
            className="bg-blue-500 text-white px-4 py-2 rounded shadow hover:bg-blue-600"
          >
            View PDF
          </button>
        </div>

        {rows.map((pageRows, pageIndex) => (
          <div
            ref={reportRef}
            className="w-auto "
            style={{ ...styles, maxWidth: "100%", height: "108vh" }}
            id={`Head${pageIndex + 1}`}
          >
            <table
              key={pageIndex}
              className="min-w-full bg-white text-[8px] table-fixed"
            >
              <thead>
                <tr>
                  <td className="text-[12px]" colSpan="3">
                    <div className="bg-white p-2 rounded text-xs">
                      <div className="flex justify-between text-blue-800 font-bold whitespace-nowrap">
                        {/* Target_Plan_Process_Date section */}
                        <div className="flex items-center space-x-1">
                          <span className="text-xs">
                            Target_Plan_Process_Date:
                          </span>
                          <span className="font-normal text-black">
                            {StatusData?.Tg_St_Pl_Date
                              ? `${
                                  new Date(StatusData.Tg_St_Pl_Date)
                                    .toISOString()
                                    .split("T")[0]
                                } ${new Date().toLocaleTimeString("en-GB")}`
                              : ""}
                          </span>
                          <span> ~ </span>
                          <span className="font-normal text-black">
                            {StatusData?.Tg_Ed_Pl_Date
                              ? `${
                                  new Date(StatusData.Tg_Ed_Pl_Date)
                                    .toISOString()
                                    .split("T")[0]
                                } ${new Date().toLocaleTimeString("en-GB")}`
                              : ""}
                          </span>
                        </div>

                        {/* Create_Date and Page section */}
                        <div className="relative w-full">
                          <div className="absolute right-0 flex items-center space-x-2">
                            <span>Create_Date:</span>
                            <span className="font-normal">
                              {getCurrentDateTime()}
                            </span>
                            <span className="px-6">
                              Page: 1/{pageIndex + 1}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mt-4 pb-5">
                        <div className="font-bold text-blue-800 text-lg mt-1">
                          Process_Grp:
                        </div>
                        <div className="flex items-center border border-black px-4 py-2 text-base mt-5">
                          <span
                            className="font-bold text-center"
                            style={{ transform: "translateY(-10px)" }}
                            id="processGCDiv"
                          >
                            {pagesWithHeaders[pageIndex].header || "N/A"}
                            {/* เรียกใช้ฟังก์ชั่น renderHeader เพื่อแสดงหัว */}
                          </span>
                        </div>
                        <div className="flex items-center border border-black px-4 py-2 text-base mt-5">
                          <span
                            className="font-bold text-center"
                            style={{ transform: "translateY(-10px)" }}
                            id="nameDiv"
                          >
                            {pagesWithHeaders[pageIndex].pggabb || "N/A"}
                          </span>
                        </div>
                        {/* Process Group Plan List */}
                        <div className="flex flex-col items-center -mt-5 space-x-10">
                          <span className="font-bold text-lg text-blue-800 text-center">
                            Process_Grp_Plan_List
                          </span>
                          <div className="flex justify-center space-x-8 mt-3">
                            <button className="bg-red-500 text-white font-bold px-8 pt-1 pb-2 text-sm">
                              <span style={{ transform: "translateY(-7px)" }}>
                                Self
                              </span>
                            </button>
                            <button className="bg-orange-500 text-white font-bold px-8 pt-1 pb-2 text-sm">
                              <span style={{ transform: "translateY(-7px)" }}>
                                1Before
                              </span>
                            </button>
                            <button className="bg-orange-500 text-white font-bold px-8 pt-1 pb-2 text-sm">
                              <span style={{ transform: "translateY(-7px)" }}>
                                2Before
                              </span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </thead>
            </table>

            <div className="container w-full">
              <div
                className="overflow-x-auto max-h-[90vh]"
                style={{ transform: "scale(0.6)", transformOrigin: "top left" }}
              >
                <table className="table-auto bg-white border-2 border-blue-800">
                  <thead className="sticky top-0 z-10 bg-white">
                    <tr className="text-blue-800 font-bold">
                      <th
                        className="py-2 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          Plan_Date
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          PDS_Deli
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          Order_Parts_No
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed relative w-auto"
                        rowSpan="2"
                      >
                        <span className="absolute top-0 right-0 text-right border border-dashed border-blue-800 px-1 text-xs">
                          <span style={{ transform: "translateY(-9px)" }}>
                            CAT
                          </span>
                        </span>
                        <span
                          className="text-xs"
                          style={{ transform: "translateY(-10px)" }}
                        >
                          Customer/Production_Name
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          PT_Name Material
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          Plan Qty
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          This Plan
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          M_Set P_Set
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 border-blue-800 text-xs"
                        colSpan="24"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          Process
                        </span>
                      </th>
                      <th
                        className="py-1 px-1 min-w-40 border border-dashed border-blue-800 text-xs w-auto"
                        rowSpan="2"
                      >
                        <span style={{ transform: "translateY(-10px)" }}>
                          PT_Note/Info
                        </span>
                      </th>
                    </tr>
                    <tr className="text-blue-800 font-bold border-b border-blue-800 text-xs">
                      {[...Array(24)].map((_, index) => (
                        <th
                          key={index}
                          className="py-1 px-1 w-auto border border-dashed border-blue-800 text-xs"
                        >
                          <span style={{ transform: "translateY(-10px)" }}>
                            {index + 1}
                          </span>
                        </th>
                      ))}
                    </tr>
                  </thead>

                  <tbody>
                    {pageRows.map((row, rowIndex) => {
                      const processData = [
                        ...row.processData,
                        ...Array.from(
                          { length: 24 - row.processData.length },
                          () => ({ process1: "", process2: "" })
                        ),
                      ].slice(0, 24);

                      const rowColor =
                        rowIndex % 2 === 0 ? "bg-[#cffff9]" : "bg-white";

                      return (
                        <tr
                          key={rowIndex}
                          className={`border border-blue-800 border-dashed text-xs ${rowColor}`}
                        >
                          <td className="py-1 px-1 border border-blue-800 border-dashed text-center w-auto">
                            <div className="flex flex-col items-center">
                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.planDate}
                              </span>

                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.pds}
                              </span>
                            </div>
                          </td>
                          <td className="py-1 px-1 border border-blue-800 border-dashed text-center w-auto">
                            <div className="flex flex-col items-center">
                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.pdsDeli}
                              </span>
                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.mark}
                              </span>
                            </div>
                          </td>

                          <td className="py-1 px-1 border border-blue-800 border-dashed text-center w-auto">
                            <div className="flex justify-between items-start">
                              <div className="flex flex-col items-start">
                                <span>{row.orderPartsNo}</span>
                                <span className="text-black text-xs mt-1">
                                  {row.target} {row.coating}
                                </span>
                              </div>
                              <span className="text-right text-xs">
                                {row.no}
                              </span>
                            </div>
                          </td>
                          <td className="py-1 px-1 border border-blue-800 border-dashed relative text-xs w-auto">
                            <div className="absolute top-0 left-0 text-left">
                              {row.customerProductionName1}
                            </div>
                            <div className="absolute top-4 left-3 text-left">
                              {row.customerProductionName2}
                            </div>
                            <div className="absolute top-0 right-0 flex flex-col items-end">
                              <span className="border border-dashed border-blue-800 text-xs">
                                <span
                                  style={{ transform: "translateY(-10px)" }}
                                >
                                  {row.cat1}
                                </span>
                              </span>
                              <div className="flex">
                                <span className="border border-dashed border-blue-800 px-1 text-xs">
                                  <span
                                    style={{ transform: "translateY(-7px)" }}
                                  >
                                    {row.cat2}
                                  </span>
                                </span>
                                <span className="border border-dashed border-blue-800 px-1 text-xs">
                                  <span
                                    style={{ transform: "translateY(-7px)" }}
                                  >
                                    {row.cat3}
                                  </span>
                                </span>
                              </div>
                            </div>
                          </td>

                          <td className="py-1 px-1 border border-blue-800 border-dashed text-center text-xs w-auto">
                            <span style={{ transform: "translateY(-10px)" }}>
                              {row.ptNameMaterial}
                            </span>
                            <span
                              className="text-xs mt-1"
                              style={{ transform: "translateY(-10px)" }}
                            >
                              {row.ptNameMaterial2}
                            </span>
                          </td>
                          <td className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto">
                            <div className="flex flex-col items-center">
                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.planQty}
                              </span>
                              <span style={{ transform: "translateY(-10px)" }}>
                                {row.spare}
                              </span>
                            </div>
                          </td>
                          <td className="py-1 px-1 border border-blue-800 border-dashed text-center text-xs w-auto">
                            <div className="flex flex-col items-center">
                              <span style={{ transform: "translateY(-5px)" }}>
                                {row.thisPlan}
                              </span>
                              <span
                                className="text-xs mt-1"
                                style={{ transform: "translateY(-5px)" }}
                              >
                                {row.ship}
                              </span>
                            </div>
                          </td>
                          <td className="py-1 px-1 border border-blue-800 border-dashed align-top text-right text-xs w-auto">
                            <div
                              className="flex flex-col items-end"
                              style={{ transform: "translateY(-10px)" }}
                            >
                              <span>{row.mSetPSet.main}</span>
                              <span className="text-xs mt-1">
                                {row.mSetPSet.sub}
                              </span>
                            </div>
                          </td>
                          {processData.map((process, procIndex) => (
                            <td
                              key={`${rowIndex}-${procIndex}`}
                              className="border border-blue-800 border-dashed text-center text-xs align-top leading-none min-w-[47px]"
                            >
                              <div className="flex flex-col items-center">
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {process.process1}
                                </span>
                                <span style={{ transform: "translateY(-5px)" }}>
                                  {process.process2}
                                </span>
                              </div>
                            </td>
                          ))}

                          <td className="py-1 px-1 border border-blue-800 border-dashed text-xs w-auto">
                            {row.ptNoteInfo.map((info, infoIndex) => (
                              <div
                                key={`ptNote-${rowIndex}-${infoIndex}`}
                                className="flex justify-end items-end h-full text-right"
                              >
                                <span
                                  style={{ transform: "translateY(-10px)" }}
                                >
                                  {info}
                                </span>
                              </div>
                            ))}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
