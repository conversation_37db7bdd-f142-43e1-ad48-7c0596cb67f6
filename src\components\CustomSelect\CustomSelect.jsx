import Select from "react-select";

const getCellStyle = (numCols) => ({
  display: "table-cell",
  padding: "5px 5px",
  borderRight: "1px solid #ddd",
  textAlign: "left",
  verticalAlign: "middle",
  whiteSpace: "nowrap",
  overflow: "hidden",
  textOverflow: "ellipsis",
  width: `${100 / numCols}%`,
});

const CustomOption = (columns) => (props) => {
  const { data, innerRef, innerProps, isFocused, isSelected } = props;

  let style = {};
  if (isSelected) style = { backgroundColor: "#1E90FF", color: "white" };
  else if (isFocused) style = { backgroundColor: "#e0e0e0" };

  return (
    <div
      ref={innerRef}
      {...innerProps}
      style={{
        ...style,
        display: "table",
        width: "100%",
        tableLayout: "fixed",
        borderCollapse: "collapse",
      }}
    >
      <div style={{ display: "table-row", borderBottom: "1px solid #ddd" }}>
        {columns.map((col, idx) => (
          <div key={idx} style={getCellStyle(columns.length)}>
            {data[col]}
          </div>
        ))}
      </div>
    </div>
  );
};

export default function CustomSelect({
  id,
  data,
  columns,
  valueKey,
  labelKey = valueKey,
  isDisabled,
  selectedValue,
  onChange,
  placeholder = "",
  bgColor = "#ccffcc",
  displayMode = "table",
}) {
  // For simple display mode, create different options format
  const options =
    displayMode === "simple"
      ? data.map((item) => ({
          value: item[valueKey],
          label: item[labelKey] || item[valueKey],
        }))
      : [
          {
            label: (
              <div
                style={{
                  display: "table",
                  width: "100%",
                  tableLayout: "fixed",
                  borderCollapse: "collapse",
                  fontWeight: "bold",
                  background: "#f0f0f0",
                  textAlign: "center",
                  borderBottom: "1px solid #ddd",
                }}
              >
                <div style={{ display: "table-row" }}>
                  {columns.map((col, idx) => (
                    <div
                      key={idx}
                      style={{
                        ...getCellStyle(columns.length),
                        borderRight:
                          idx === columns.length - 1
                            ? "none"
                            : "1px solid #ddd",
                      }}
                    >
                      {col}
                    </div>
                  ))}
                </div>
              </div>
            ),
            options: data.map((item) => ({
              value: item[valueKey],
              label: item[valueKey],
              ...item,
            })),
          },
        ];

  // Find selected option based on display mode
  const selectedOption =
    displayMode === "simple"
      ? options.find((opt) => opt.value === selectedValue)
      : options[0]?.options?.find((opt) => opt.value === selectedValue);

  const rightAlignIds = [
    "Customer_CD",
    "Supply_CD",
    "Destination_CD",
    "Contract_Docu_CD",
    "Price_CD",
    "Od_Ctl_Person_CD",
    "Od_Reg_Person_CD",
    "Od_Upd_Person_CD",
    "Specific_CD",
    "Od_Progress_CD",
    "Delivery_CD",
    "Schedule_CD",
    "Target_CD",
  ];

  return (
    <Select
      id={id}
      isDisabled={isDisabled}
      value={selectedOption || null}
      onChange={(selected) => {
        onChange({ id, value: selected?.value || "" });
      }}
      options={options}
      components={
        displayMode === "table" ? { Option: CustomOption(columns) } : undefined
      }
      menuPlacement="auto"
      menuPosition="fixed"
      menuPortalTarget={document.body}
      placeholder={placeholder}
      styles={{
        control: (base) => ({
          ...base,
          border: isDisabled ? "2px solid #9ca3af" : "2px solid #6b7280",
          borderRadius: "0.375rem",
          backgroundColor: isDisabled ? "#e5e7eb" : bgColor,
          width: "100%",
          minHeight: "2rem",
          opacity: isDisabled ? 0.8 : 1,
        }),
        menu: (base) => ({
          ...base,
          width: displayMode === "table" ? "450px" : "auto",
          maxHeight: "200px",
          overflowY: "auto",
          padding: "0",
          ...(rightAlignIds.includes(id) ? { right: 0, left: "auto" } : {}),
        }),
        menuList: (base) => ({
          ...base,
          maxHeight: "200px",
          overflowY: "auto",
        }),
        option: (base) => ({
          ...base,
          padding: displayMode === "table" ? "0" : "8px 12px",
        }),
        placeholder: (base) => ({
          ...base,
          color: isDisabled ? "#9ca3af" : "#6b7280",
        }),
        singleValue: (base) => ({
          ...base,
          color: isDisabled ? "#4b5563" : "#000000",
        }),
      }}
    />
  );
}
