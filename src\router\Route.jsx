import { create<PERSON><PERSON>er<PERSON><PERSON>er } from "react-router-dom";
import { RouterProvider } from "react-router-dom";
import LoginPage from "../pages/LoginPage";
import { OrderListPage } from "../pages/OrderListPage";
import CostInfoPage from "../pages/CostInfoPage";
import CostMultiPage from "../pages/CostMultiPage";
import CalcCompletePage from "../pages/CalcCompletePage";
import CostListPage from "../pages/CostListPage";
import OrderInfoPage from "../pages/OrderInfoPage";
import PlanInfoPage from "../pages/PlanInfoPage";
import PlanCopyPage from "../pages/PlanCopyPage";
import PlanListPage from "../pages/PlanListPage";
import PurchaseInfoPage from "../pages/PurchaseInfoPage";
import PurchaseListPage from "../pages/PurchaseListPage";
import ResultInfoPage from "../pages/ResultInfoPage";
import ProcessG_PlanPage from "../pages/ProcessG_PlanPage";

// PlanInfo Reports
import RD_Process_Sheet_All_Plan from "../components/reports/PlanInfo_reports/RD_Process_Sheet_All_Plan";
import RD_Process_Sheet_1P from "../components/reports/PlanInfo_reports/RD_Prcess_Sheet_1P";

// OrderInfo Reports
import RD_Process_Sheet from "../components/reports/OrderInfo_reports/RD_Process_Sheet";

// PlanList Reports
import RD_Pl_Pg_None from "../components/reports/PlanList_reports/RD_Pl_Pg_None";

// OrderList Reports
import QR_Od_List_Detail_Target from "../components/reports/OrderList_repots/QR_Od_List_Detail_Target";
import QR_Od_List_Detail_Product from "../components/reports/OrderList_repots/QR_Od_List_Detail_Product";
import QE_Cals_Sum from "../components/reports/OrderList_repots/QE_Cals_Sum";
import RD_Od_Backlog_List from "../components/reports/OrderList_repots/RD_Od_Backlog_List";
import RD_Od_Final_Completed_List from "../components/reports/OrderList_repots/RD_Od_Final_Completed_List";
import QE_Od_List_Detail from "../components/reports/OrderList_repots/QE_Od_List_Detail";

import DashboardPage from "../pages/DashboardPage";
import SalesInfoPage from "../pages/SalesInfoPage";
import SalesDashboradPage from "../pages/SalesDashboradPage";
import SubConDashboradPage from "../pages/SubConDashboradPage";
import PlanDashboardPage from "../pages/PlanDashboardPage";
import ProcessDashboardPage from "../pages/ProcessDashboardPage";
import ProductionDashboardPage from "../pages/ProductionDashboardPage";
import QcDashboardPage from "../pages/QcDashboardPage";
import LinkDashborad from "../pages/LinkDashborad";
import FinishDashboardPage from "../pages/FinishDashboardPage";
import PrintDashboradPage from "../pages/PrintDashboradPage";
import ImportDashboardPage from "../pages/ImportDashboardPage";
import ExportDashboardPage from "../pages/ExportDashboardPage";
import NAV_FGPage from "../pages/Admin-Menu/NAV_FGPage";
import NAV_WIPage from "../pages/Admin-Menu/NAV_WIPage";
import DeliveryDatePage from "../pages/Admin-Menu/DeliveryDatePage";
import SystemRestorePage from "../pages/Admin-Menu/SystemRestorePage";
import None_FG_DataPage from "../pages/Admin-Menu/None_FG_DataPage";
import None_WI_DataPage from "../pages/Admin-Menu/None_WI_DataPage";
import MonthTargetPage from "../pages/Admin-Menu/MonthTargetPage";
import SystemMaintenancePage from "../pages/Admin-Menu/SystemMaintenancePage";
import None_FG_Data_TenkeiPage from "../pages/Admin-Menu/None_FG_Data_TenkeiPage";
import None_WI_Data_TenkeiPage from "../pages/Admin-Menu/None_WI_Data_TenkeiPage";
import WeekTargetSettingPage from "../pages/Admin-Menu/WeekTargetSettingPage";
import ASP_CSV1Page from "../pages/Admin-Menu/ASP_CSV1Page";
import FG_AmountPage from "../pages/Admin-Menu/FG_AmountPage";
import WI_AmountPage from "../pages/Admin-Menu/WI_AmountPage";
import SystemSetPage from "../pages/Admin-Menu/SystemSetPage";
import ASP_CSV2Page from "../pages/Admin-Menu/ASP_CSV2Page";
import Tenkei_FG_SumPage from "../pages/Admin-Menu/Tenkei_FG_SumPage";
import BlessedUpdatePage from "../pages/Admin-Menu/BlessedUpdatePage";
import { CustomerPage } from "../pages/Master/CustomerPage";
import { WorkGPage } from "../pages/Master/WorkGPage";
import { WorkerPage } from "../pages/Master/WorkerPage";
import { CoatingPage } from "../pages/Master/CoatingPage";
import { ProcessGPage } from "../pages/Master/ProcessGPage";
import { ProcessPage } from "../pages/Master/ProcessPage";
import { MachinePage } from "../pages/Master/MachinePage";
import { HolidayPage } from "../pages/Master/HolidayPage";
import { VendorPage } from "../pages/Master/VendorPage";
import SearchplanPage from "../pages/SearchplanPage";
import Tenkei_WI_SumPage from "../pages/Admin-Menu/Tenkei_WI_SumPage";
import Processg_Plan_CfmPage from "../pages/Processg_Plan_CfmPage";
import { NAV_OrderCSV_ImportPage } from "../pages/NAV_OrderCSV_ImportPage";
import { NAV_PurchaseCSV_ImportPage } from "../pages/NAV_PurchaseCSV_ImportPage";
import { Master1Page } from "../pages/Master/Master1Page";
import { Master2Page } from "../pages/Master/Master2Page";
import { Master3Page } from "../pages/Master/Master3Page";
import { StatusPage } from "../pages/StatusPage";
import { EC1Page } from "../pages/EC1Page";
import { AdminMenuDashboardPage } from "../pages/AdminMenuDashboardPage";
import { ConvertDashboardPage } from "../pages/ConvertDashboardPage";
import { Reserve1DashboardPage } from "../pages/Reserve1DashboardPage";
import { SearchDashboardPage } from "../pages/SearchDashboardPage";
import RdNavPcUpd from "../components/reports/RdNavPcUpd";
import RdNavOdUpd from "../components/reports/RD_NAV_Od_Upd";
import RdProGPlanPage from "../pages/reports/RdProGPlanPage";
import RdProgGraphPage from "../pages/reports/RdProgGraphPage";
import QE_Pl_PP_ViewPage from "../pages/reports/QE_Pl_PP_ViewPage";
import QE_Pl_PD_ViewPage from "../pages/reports/QE_Pl_PD_ViewPage";
import QE_Pl_RD_ViewPage from "../pages/reports/QE_Pl_RD_ViewPage";

// FG_SUM Import
import { Order_FG } from "../components/Admin-Menu/FG_SUM_Tables/Order_FG";
import { FG_SUM } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM";
import { FG_SUM_Date } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM_Date";
import { FG_SUM_Item1 } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM_Item1";
import { FG_SUM_Item1G } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM_Item1G";
import { FG_SUM_Type } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM_Type";
import { FG_SUM_Week } from "../components/Admin-Menu/FG_SUM_Tables/FG_SUM_Week";

// WI_SUM Import
import { Plan_WI } from "../components/Admin-Menu/WI_SUM_Table/Plan_WI";
import { WI_Sum } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum";
import { WI_Sum_Coating } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Coating";
import { WI_Sum_Item1 } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Item1";
import { WI_Sum_Item_1g } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Item_1g";
import { WI_Sum_Outside } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Outside";
import { WI_Sum_Pending } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Pending";
import { WI_Sum_Plan } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Plan";
import { WI_Sum_Progress } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Progress";
import { WI_Sum_Type } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Type";
import { WI_Sum_Week } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Week";
import { WI_Sum_Compound } from "../components/Admin-Menu/WI_SUM_Table/WI_Sum_Compound";
import QR_PI_ListPage from "../pages/reports/QR_PI_ListPage";
import QR_CS_ListPage from "../pages/reports/QR_CS_ListPage";
import ProtectedRoute from "../router/ProtectedRoute";

const router = createBrowserRouter([
  {
    path: "/",
    element: <LoginPage />,
  },
  {
    path: "/dashboard",
    element: <ProtectedRoute><DashboardPage /></ProtectedRoute>,
  },
  {
    path: "/sales-info",
    element:<ProtectedRoute> <SalesInfoPage /></ProtectedRoute>,
  },
  {
    path: "/sales",
    element: <SalesDashboradPage />,
  },
  {
    path: "/sub-con",
    element: <SubConDashboradPage />,
  },
  {
    path: "/plan",
    element: <PlanDashboardPage />,
  },
  {
    path: "/production",
    element: <ProductionDashboardPage />,
  },
  {
    path: "/qc",
    element: <QcDashboardPage />,
  },
  {
    path: "/link",
    element: <LinkDashborad />,
  },
  {
    path: "/finish",
    element: <FinishDashboardPage />,
  },
  {
    path: "/print",
    element: <PrintDashboradPage />,
  },
  {
    path: "/master1",
    element: <Master1Page />,
  },
  {
    path: "/master2",
    element: <Master2Page />,
  },
  {
    path: "/master3",
    element: <Master3Page />,
  },
  {
    path: "/import",
    element: <ImportDashboardPage />,
  },
  {
    path: "/export",
    element: <ExportDashboardPage />,
  },
  {
    path: "/order-list",
    element:<ProtectedRoute> <OrderListPage /></ProtectedRoute>,
  },
  {
    path: "/calc-complete",
    element: <CalcCompletePage />,
  },
  {
    path: "/cost-list",
    element: <ProtectedRoute><CostListPage /></ProtectedRoute>,
  },
  {
    path: "/cost-multi",
    element: <CostMultiPage />,
  },
  {
    path: "/order-info",
    element:<ProtectedRoute> <OrderInfoPage /></ProtectedRoute>,
  },
  {
    path: "/plan-info",
    element: <ProtectedRoute><PlanInfoPage /></ProtectedRoute>,
  },
  {
    path: "/plan-info-copy",
    element: <PlanCopyPage />,
  },
  {
    path: "/plan-list",
    element: <ProtectedRoute><PlanListPage /></ProtectedRoute>,
  },
  {
    path: "/purchase-info",
    element: <ProtectedRoute><PurchaseInfoPage /></ProtectedRoute>,
  },
  {
    path: "/purchase-list",
    element: <PurchaseListPage />,
  },
  {
    path: "/result-info",
    element: <ProtectedRoute><ResultInfoPage /></ProtectedRoute>,
  },
  {
    path: "/processg-plan-list",
    element: <ProcessG_PlanPage />,
  },
  // Start PlanInfo Reports
  {
    path: "/reports/RD_Process_Sheet_All_Plan/:orderNo",
    element: <RD_Process_Sheet_All_Plan />,
  },
  {
    path: "/reports/RD_Process_Sheet_1P/:orderNo/:partNo",
    element: <RD_Process_Sheet_1P />,
  },
  // End PlanInfo Reports

  // Start OrderInfo Reports
  {
    path: "/reports/RD_Process_Sheet/:orderNo",
    element: <RD_Process_Sheet />,
  },
  // End OrderInfo Reports

  // Start PlanList Reports
  {
    path: "/reports/RD_Pl_Pg_None",
    element: <RD_Pl_Pg_None />,
  },
  // End PlanList Reports

  // Start OrderList Reports
  {
    path: "QR_Od_List_Detail_Target",
    element: <QR_Od_List_Detail_Target />,
  },
  {
    path: "QR_Od_List_Detail_Product",
    element: <QR_Od_List_Detail_Product />,
  },
  {
    path: "QE_Cals_Sum",
    element: <QE_Cals_Sum />,
  },
  {
    path: "/reports/RD_Od_Backlog_List",
    element: <RD_Od_Backlog_List />,
  },
  {
    path: "/reports/RD_Od_Final_Completed_List",
    element: <RD_Od_Final_Completed_List />,
  },
  {
    path: "QE_Od_List_Detail",
    element: <QE_Od_List_Detail />,
  },
  // End OrderList Reports

  {
    path: "/cost-info",
    element:<ProtectedRoute> <CostInfoPage /></ProtectedRoute>,
  },
  {
    path: "/admin-menu",
    element: <AdminMenuDashboardPage />,
  },
  {
    path: "/nav-fg",
    element: <NAV_FGPage />,
  },
  {
    path: "/nav-wi",
    element: <NAV_WIPage />,
  },
  {
    path: "/delivery-date",
    element: <DeliveryDatePage />,
  },
  {
    path: "/system-restore",
    element: <SystemRestorePage />,
  },
  {
    path: "/none-fg-data",
    element: <None_FG_DataPage />,
  },
  {
    path: "/none-wi-data",
    element: <None_WI_DataPage />,
  },
  {
    path: "/month-target",
    element: <MonthTargetPage />,
  },
  {
    path: "/system-maintenance",
    element: <SystemMaintenancePage />,
  },
  {
    path: "/none-fg-data-tenkei",
    element: <None_FG_Data_TenkeiPage />,
  },
  {
    path: "/none-wi-data-tenkei",
    element: <None_WI_Data_TenkeiPage />,
  },
  {
    path: "/week-target-setting",
    element: <WeekTargetSettingPage />,
  },
  {
    path: "/asp-csv-1",
    element: <ASP_CSV1Page />,
  },
  {
    path: "/fg-amount",
    element: <FG_AmountPage />,
  },
  {
    path: "/wi-amount",
    element: <WI_AmountPage />,
  },
  {
    path: "/system-set",
    element: <SystemSetPage />,
  },
  {
    path: "/asp-csv-2",
    element: <ASP_CSV2Page />,
  },
  {
    path: "/tenkei-fg-sum",
    element: <Tenkei_FG_SumPage />,
  },
  {
    path: "/tenkei-wi-sum",
    element: <Tenkei_WI_SumPage />,
  },
  {
    path: "/blessed-update",
    element: <BlessedUpdatePage />,
  },
  {
    path: "/customer",
    element: <CustomerPage />,
  },
  {
    path: "/vendor",
    element: <VendorPage />,
  },
  {
    path: "/workG",
    element: <WorkGPage />,
  },
  {
    path: "/worker",
    element: <WorkerPage />,
  },
  {
    path: "/coating",
    element: <CoatingPage />,
  },
  {
    path: "/processG",
    element: <ProcessGPage />,
  },
  {
    path: "/process",
    element: <ProcessPage />,
  },
  {
    path: "/machine",
    element: <MachinePage />,
  },
  {
    path: "/holiday",
    element: <HolidayPage />,
  },
  {
    path: "/search",
    element: <SearchplanPage />,
  },
  {
    path: "/processg-plan-cfm",
    element: <Processg_Plan_CfmPage />,
  },
  {
    path: "/nav-order-csv-import",
    element: <NAV_OrderCSV_ImportPage />,
  },
  {
    path: "/nav-purchase-csv-import",
    element: <NAV_PurchaseCSV_ImportPage />,
  },
  {
    path: "/status",
    element: <StatusPage />,
  },
  {
    path: "/ec-1",
    element: <EC1Page />,
  },
  {
    path: "/convert",
    element: <ConvertDashboardPage />,
  },
  {
    path: "/reserve-1",
    element: <Reserve1DashboardPage />,
  },
  {
    path: "/search-dashboard",
    element: <SearchDashboardPage />,
  },
  {
    path: "/process-dashboard",
    element: <ProcessDashboardPage />,
  },
  {
    path: "/report-csv-purchase",
    element: <RdNavPcUpd />,
  },
  {
    path: "/report-csv-order",
    element: <RdNavOdUpd />,
  },
  {
    path: "/reports/RD_ProG_Plan",
    element: <RdProGPlanPage />,
  },
  {
    path: "/reports/RD_ProG_Graph",
    element: <RdProgGraphPage />,
  },
  {
    path: "/reports/pp-view",
    element: <QE_Pl_PP_ViewPage />,
  },
  {
    path: "/reports/pd-view",
    element: <QE_Pl_PD_ViewPage />,
  },
  {
    path: "/reports/rd-view",
    element: <QE_Pl_RD_ViewPage />,
  },
  // FG_SUM Table
  {
    path: "/Order_FG",
    element: <Order_FG />,
  },
  {
    path: "/FG_SUM",
    element: <FG_SUM />,
  },
  {
    path: "/FG_SUM_Date",
    element: <FG_SUM_Date />,
  },
  {
    path: "/FG_SUM_Item1",
    element: <FG_SUM_Item1 />,
  },
  {
    path: "/FG_SUM_Item1G",
    element: <FG_SUM_Item1G />,
  },
  {
    path: "/FG_SUM_Type",
    element: <FG_SUM_Type />,
  },
  {
    path: "/FG_SUM_Week",
    element: <FG_SUM_Week />,
  },
  // End FG_SUM Table

  // WI_SUM Table
  {
    path: "/plan_wi",
    element: <Plan_WI />,
  },
  {
    path: "/wi_sum",
    element: <WI_Sum />,
  },
  {
    path: "/wi_sum_coating",
    element: <WI_Sum_Coating />,
  },
  {
    path: "/wi_sum_compound",
    element: <WI_Sum_Compound />,
  },
  {
    path: "/wi_sum_item1",
    element: <WI_Sum_Item1 />,
  },
  {
    path: "/wi_sum_item_1g",
    element: <WI_Sum_Item_1g />,
  },
  {
    path: "/wi_sum_outside",
    element: <WI_Sum_Outside />,
  },
  {
    path: "/wi_sum_pending",
    element: <WI_Sum_Pending />,
  },
  {
    path: "/wi_sum_plan",
    element: <WI_Sum_Plan />,
  },
  {
    path: "/wi_sum_progress",
    element: <WI_Sum_Progress />,
  },
  {
    path: "/wi_sum_type",
    element: <WI_Sum_Type />,
  },
  {
    path: "/wi_sum_week",
    element: <WI_Sum_Week />,
  },
  // End WI_SUM TABLE

  {
    path: "/qr_pi_list",
    element: <QR_PI_ListPage />,
  },
    {
    path: "/qr_cs_list",
    element: <QR_CS_ListPage />,
  },
]);

export default function Route() {
  return <RouterProvider router={router} />;
}
