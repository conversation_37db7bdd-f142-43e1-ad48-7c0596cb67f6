import React, { useState, useEffect } from "react";
import { usePlan } from "../hooks/use-plan";
import Swal from "sweetalert2";
import Navbar from "./Navbar";
import { useSearchParams } from "react-router-dom";
import axios from "axios";
import Select from "react-select";
// Icons
import { FaArrowDown } from "react-icons/fa";

export default function PlanCopy() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const {
    fetchBaseCopyPlanSearch,
    BaseCopyPlanData,
    setBaseCopyPlanData,
    PartsData,
  } = usePlan();

  const [searchParams] = useSearchParams();
  const orderNo = searchParams.get("orderNo") ?? "";
  const [orderNoInput, setOrderNoInput] = useState("");
  const [targetsData, setTargetsData] = useState([]);
  const [selectedTargets, setSelectedTargets] = useState([]);
  const [isInitialSelectionDone, setIsInitialSelectionDone] = useState(false);
  const [originalPartsNo, setOriginalPartsNo] = useState({});
  const [selectedTimeOption, setSelectedTimeOption] = useState("1");

  const [buttonState, setButtonState] = useState({
    F1: true,
    F2: true,
    F3: false,
    F4: false,
    F5: true,
    F6: true,
    F7: false,
    F8: false,
    F9: true,
    F10: true,
    F11: true,
    F12: true,
  });

  const [orderData, setOrderData] = useState({
    // ข้อมูลทั่วไปของ order
    Order_No: "",
    Customer_CD: "",
    Customer_Abb: "",
    Product_Grp_CD: "",
    Od_Ctl_Person_Name: "",
    Sales_Person_Name: "",
    Specific_CD: "",
    Specific_Name: "",
    Pd_Received_Date: "",
    Request_Delivery: "",
    Product_Delivery: "",
    Product_Name: "",
    Request1_CD: "",
    Request3_CD: "",
    Material1: "",
    Coating_CD: "",
    Coating_Name: "",
    Product_Size: "",
    Quantity: "",
    Unit_Name: "",
    Material2: "",
    Material4: "",
    Product_Docu: "",
    Delivery_CD: "",
    Delivery_Name: "",
    I_Completed_Date: "",
    Confirm_Delivery: "",
    Product_Draw: "",
    Price_Name: "",
    Unit_Price: "",
    Material3: "",
    Material5: "",
    Supple_Docu: "",
    Target_CD: "",
    Target_Name: "",
    Calc_Process_Date: "",
    Pd_Complete_Date: "",
  });

  const [plansData, setPlansData] = useState([
    {
      Parts_Name: "",
      Parts_No: "",
      Reg_Person: "",
      Pt_Progress_Name: "",
      Pt_Delivery: "",
      Pl_Reg_Date: "",
      Pt_Complete_Date: "",
      Pt_Confirm_Date: "",
      Pl_Upd_Date: "",
      Connect_Od_No: "",
      Connect_Pt_No: "",
      Connect_Pr_No: "",
      PPC1: "",
      PPC2: "",
      PPC3: "",
      PPC4: "",
      PPC5: "",
      PPC6: "",
      PPC7: "",
      PPC8: "",
      PPC9: "",
      PPC10: "",
      PPC11: "",
      PPC12: "",
      PPC13: "",
      PPC14: "",
      PPC15: "",
      PPC16: "",
      PPC17: "",
      PPC18: "",
      PPC19: "",
      PPC20: "",
      PPC21: "",
      PPC22: "",
      PPC23: "",
      PPC24: "",
      PPC25: "",
      PPC26: "",
      PPC27: "",
      PPC28: "",
      PPC29: "",
      PPC30: "",
      PPC31: "",
      PPC32: "",
      PPC33: "",
      PPC34: "",
      PPC35: "",
      PPC36: "",
    },
  ]);

  const fetchOrderData = async () => {
    if (!orderNo) return;

    try {
      const response = await fetch(`${apiUrl_4000}/plan/fetch-base-copy-plan`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ Order_No: orderNo }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch order data");
      }

      const result = await response.json();
      const order = result.data.order;
      const plans = result.data.plans;

      setOrderData(Array.isArray(order) && order.length > 0 ? order[0] : {});
      setPlansData(plans ?? []);
    } catch (error) {
      console.error("Error fetching order data:", error);
    }
  };

  useEffect(() => {
    fetchOrderData();
  }, [orderNo]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setOrderNoInput(value);

    if (value === "") {
      setBaseCopyPlanData(null);
      setTargetsData([]);
    }
  };

  const handleInputChangeforOrderData = (e) => {
    const { name, value } = e.target;
    setOrderData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

const handleCheckboxChange = (index) => {
  setSelectedTargets((prevSelected) => {
    const isAlreadySelected = prevSelected.includes(index);

    if (isAlreadySelected) {
      // ถ้ายกเลิก ให้คืนค่าเดิมของ Parts_No ที่เคยบันทึกไว้
      setTargetsData((prevData) =>
        prevData.map((item, idx) =>
          idx === index
            ? { ...item, Parts_No: originalPartsNo[index] || item.Parts_No }
            : item
        )
      );

      return prevSelected.filter((item) => item !== index);
    } else {
      // ถ้ายังไม่ได้เลือก และยังไม่มีการเก็บค่าไว้ ให้เก็บ Parts_No
      setOriginalPartsNo((prevParts) => {
        if (!(index in prevParts)) {
          return {
            ...prevParts,
            [index]: targetsData[index].Parts_No,
          };
        }
        return prevParts; // ถ้ามีอยู่แล้ว ไม่เขียนทับ
      });

      return [...prevSelected, index];
    }
  });
};


  const handleRadioChange = (event) => {
    const value = event.target.value === "Plan_Time" ? "1" : "2";
    setSelectedTimeOption(value);
  };

  const options = (PartsData ?? []).map((item) => ({
    value: item.Parts_CD,
    label: item.Parts_Abb,
  }));

  const handlePartsNoChange = (e, index) => {
    if (selectedTargets.includes(index)) {
      const updatedPartsNo = e.target.value;
      setTargetsData((prevData) =>
        prevData.map((item, idx) =>
          idx === index ? { ...item, Parts_No: updatedPartsNo } : item
        )
      );
    }
  };

  const handlePartsCDChange = (e, index) => {
    if (selectedTargets.includes(index)) {
      const updatedPartsCD = e.target.value;
      console.log("เปลี่ยนค่า index", index, "เป็น", updatedPartsCD);
      setTargetsData((prevData) => {
        const newData = prevData.map((item, idx) =>
          idx === index ? { ...item, Parts_CD: updatedPartsCD } : item
        );
        console.log("อัปเดตแล้ว:", newData);
        return newData;
      });
    }
  };

  const handleEnterPress = (e) => {
    if (e.key === "Enter" && orderNoInput.trim() !== "") {
      fetchBaseCopyPlanSearch(orderNoInput);
    }
  };

  const handleBlur = () => {
    if (orderNoInput.trim() !== "") {
      fetchBaseCopyPlanSearch(orderNoInput);
    }
  };

  const handleButtonClick = () => {
    if (orderData?.Order_No) {
      setOrderNoInput(orderData.Order_No);
      fetchBaseCopyPlanSearch(orderData.Order_No);
    }
  };

  const formatDate = (date) => {
    if (!date) return "";
    const parsedDate = new Date(date);
    if (isNaN(parsedDate)) return "";
    return parsedDate.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

const handleF2Click = () => {
  const newData = BaseCopyPlanData?.plans ?? [];

  setTargetsData(newData);

  // ติ๊ก checkbox ทั้งหมด และเก็บค่า Parts_No
  const selectedIndexes = newData.map((_, idx) => idx); // [0,1,2,...]
  const partsNoMap = {};

  newData.forEach((item, idx) => {
    partsNoMap[idx] = item.Parts_No?.trim() || "";
  });

  setSelectedTargets(selectedIndexes);
  setOriginalPartsNo(partsNoMap);



  console.log("✅ Loaded และติ๊กทั้งหมด:", selectedIndexes);
  console.log("🧾 Original Parts_No map:", partsNoMap);
};

  const handleF9Click = async () => {
    if (selectedTargets.length === 0) {
      Swal.fire("Please select at least one item.");
      return;
    }

    const selectedRows = selectedTargets.map((index) => targetsData[index]);

    const hasEmptyPartsNo = selectedRows.some(
      (row) => !row.Parts_No || row.Parts_No.trim() === ""
    );

    if (hasEmptyPartsNo) {
      Swal.fire({
        icon: "error",
        title: "Parts_No is required.",
        text: "Please provide a valid Parts_No for all selected items.",
      });
      return;
    }

    const existingPartsNos = new Set(plansData.map((plan) => plan.Parts_No));

    const duplicatePartsNo = selectedRows.find((row) =>
      existingPartsNos.has(row.Parts_No)
    );

    if (duplicatePartsNo) {
      Swal.fire({
        icon: "warning",
        title: "Duplicate Parts_No",
        text: `Parts_No ${duplicatePartsNo.Parts_No} already exists.`,
      });
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to copy these plans?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, copy it!",
      cancelButtonText: "No, cancel",
    });

    if (!result.isConfirmed) {
      return;
    }

    try {
      const userData = JSON.parse(localStorage.getItem("user"));
      const workerCd = userData ? userData.Worker_CD : "";

      Swal.fire({
        title: "Copying plans...",
        html: "Please wait...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // สร้าง payload เป็น array หลายชุด
      const payload = selectedRows.map((row, index) => ({
        orderNo_Base: orderNoInput.trim(),
        partNo_Base: originalPartsNo[selectedTargets[index]]?.trim() || "",
        orderNo_Target: orderData.Order_No.trim(),
        partNo_Target: row.Parts_No.trim(),
        partCD_target: row.Parts_CD.trim(),
        Pt_delivery: orderData.Request_Delivery.trim(),
        Pt_Qty: orderData.Quantity,
        Person_cd: workerCd.trim(),
        Time_Option: selectedTimeOption,
      }));
   

        console.log("Payload being sent:", payload);

      const response = await axios.post(
        `${apiUrl_4000}/plan/create-copy-plan`,
        payload
      );

      Swal.close();

      // console.log("Server Response:", response.data);

      if (response.data.successCount > 0) {
        Swal.fire({
          icon: "success",
          title: "Copy successful",
          text: response.data.message,
        });

        if (window.opener) {
          window.opener.postMessage({ type: "REFRESH_SEARCH" }, "*");
        }
      } else {
        Swal.fire({
          icon: "error",
          title: "Some operations failed",
          text: `Completed ${response.data.successCount} operations, ${response.data.errorCount} errors.`,
        });
      }

      setSelectedTargets([]);
      setBaseCopyPlanData([]);
      setOrderNoInput("");
      fetchOrderData();
      setTargetsData([]);
    } catch (error) {
      console.error("Error:", error);

      Swal.close();

      Swal.fire("An error occurred.", "Unable to contact the server.", "error");
    }
  };

  const handleF12Click = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to close this page?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, close",
      cancelButtonText: "Cancel",
    }).then((result) => {
      if (result.isConfirmed) {
        window.close(); // ปิดหน้าต่างถ้าผู้ใช้กดยืนยัน
      }
    });
  };

  useEffect(() => {
    if (!isInitialSelectionDone && targetsData.length > 0) {
      setSelectedTargets(targetsData.map((_, index) => index)); // เลือกทุกตัว
      setIsInitialSelectionDone(true); // ทำครั้งเดียว
    }
  }, [targetsData, isInitialSelectionDone]);

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <div className="flex flex-col w-screen mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-hidden">
          <div className="bg-white grid grid-cols-1">
            <div className="flex justify-center items-center">
              <h1 className="text-2xl font-bold ">Plan Info Copy</h1>
            </div>

            <div className="w-full h-full">
              <div className="flex space-x-4 w-full">
                {/* ส่วนของ div ซ้าย */}
                <div className="p-4 space-y-4 min-w-[900px] flex-shrink-0 w-2/3">
                  <div className="flex items-center space-x-2 justify-start">
                    <label className="text-xs font-medium">
                      Order No for Paste
                    </label>
                    <input
                      type="text"
                      id="Order_No"
                      defaultValue={orderData.Order_No}
                      onChange={handleInputChangeforOrderData}
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-36 bg-[#ccffff]"
                    />
                  </div>
                </div>
              </div>
              <hr className="border-y-[1px] border-gray-300" />
            </div>

            <div className="overflow-x-auto w-full">
              <div className="grid grid-cols-12 min-w-[1730px]">
                <div className="col-span-12 me-5 mt-5 ml-14">
                  <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-12 md:col-span-9">
                      {/* Group 1 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2 w-auto">
                          <label className="text-xs font-medium">
                            Order_Info
                          </label>
                          <div className="w-24"> </div>
                        </div>

                        {/* Customer */}
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Customer</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                            name="Customer_CD"
                            value={orderData.Customer_CD ?? ""}
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-36"
                            name="Customer_Abb"
                            value={orderData.Customer_Abb ?? ""}
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>

                        {/* Product Group */}
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Product_Group
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-42"
                              name="Product_Grp_Name"
                              value={orderData.Product_Grp_CD ?? ""}
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>

                        {/* Order Control Person */}
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Order_Control_Person
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="Od_Ctl_Person_Name"
                              value={orderData.Od_Ctl_Person_Name ?? ""}
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>

                        {/* Sales Person */}
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Sales_person
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="Sales_Person_Name"
                              value={orderData.Sales_Person_Name ?? ""}
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>

                        {/* Specific */}
                        <div className="flex gap-4 w-auto ml-4">
                          <label className="w-10 text-xs mt-1">Specific</label>
                          <div className="w-auto flex gap-1">
                            <select
                              id="Specific_CD"
                              name="Specific_CD"
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                              value={orderData.Specific_CD ?? ""}
                              disabled
                            >
                              <option value={orderData.Specific_CD ?? ""}>
                                {orderData.Specific_CD
                                  ? orderData.Specific_CD
                                  : ""}
                              </option>
                            </select>
                            <input
                              name="Specific_Name"
                              value={orderData.Specific_Name ?? ""}
                              onChange={handleInputChangeforOrderData}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                              readOnly
                            />
                          </div>
                        </div>

                        {/* Pd_Receive */}
                        <div className="flex gap-1 w-auto ml-1">
                          <label className="w-auto text-xs mt-1">
                            Pd_Receive
                          </label>
                          <div className="w-auto flex gap-1 ml-1.5">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="Pd_Received_Date"
                              value={
                                orderData.Pd_Received_Date
                                  ? formatDate(orderData.Pd_Received_Date ?? "")
                                  : ""
                              }
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 2 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className="text-xs font-medium">Request</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28"
                            name="Request_Delivery"
                            value={
                              orderData.Request_Delivery
                                ? formatDate(orderData.Request_Delivery ?? "")
                                : ""
                            }
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Name</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60"
                            name="Product_Name"
                            value={orderData.Product_Name ?? ""} // ใช้ข้อมูลจาก orderData
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Req1</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16"
                              name="Request1_CD"
                              value={orderData.Request1_CD ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                            <label className="w-auto text-xs mt-1">/3</label>
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16"
                              name="Request3_CD"
                              value={orderData.Request3_CD ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_1</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Material1"
                              value={orderData.Material1 ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ">
                          <label className="w-10 text-xs mt-1">Coating</label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Coating_CD"
                              name="Coating_CD"
                              value={orderData.Coating_CD ?? ""}
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-14"
                              disabled
                            >
                              <option value={orderData.Coating_CD ?? ""}>
                                {orderData.Coating_CD
                                  ? orderData.Coating_CD
                                  : ""}
                              </option>
                            </select>

                            <input
                              name="Coating_Name"
                              value={orderData.Coating_Name ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-16"
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-1">
                          <label className="w-auto text-xs mt-1">Detail</label>
                          <div className="w-auto flex gap-1">
                            <input
                              disabled
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32"
                              name="Pd_Received_Date"
                              value={formatDate(
                                orderData.Pd_Received_Date ?? ""
                              )} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Od_Progress
                          </label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Od_Progress_CD"
                              name="Od_Progress_CD"
                              value={orderData.Od_Progress_CD ?? ""}
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                              disabled
                            >
                              <option value={orderData.Od_Progress_CD ?? ""}>
                                {orderData.Od_Progress_CD
                                  ? orderData.Od_Progress_CD
                                  : ""}
                              </option>
                            </select>
                            <input
                              name="Od_Progress_Name"
                              value={orderData.Od_Progress_Name ?? ""}
                              onChange={handleInputChangeforOrderData}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[0.5px]">
                          <label className="w-auto text-xs mt-1 mr-3">
                            Pd_Comp
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="Pd_Complete_Date"
                              value={
                                orderData.Pd_Complete_Date
                                  ? formatDate(orderData.Pd_Complete_Date ?? "")
                                  : ""
                              }
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 3 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className="text-xs font-medium">Product</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28"
                            name="Product_Delivery"
                            value={
                              orderData.Product_Delivery
                                ? formatDate(orderData.Product_Delivery ?? "")
                                : ""
                            }
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Size</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60"
                            name="Product_Size"
                            value={orderData.Product_Size ?? ""} // ใช้ข้อมูลจาก orderData
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-3 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Qty</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16"
                              name="Quantity"
                              value={orderData.Quantity ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Unit_Name"
                              value={orderData.Unit_Name ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_2</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Material2"
                              value={orderData.Material2 ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_4</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Material4"
                              value={orderData.Material4 ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Product_Docu
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32"
                              name="Product_Docu"
                              value={orderData.Product_Docu ?? ""} // ใช้ข้อมูลจาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[25px]">
                          <label className="w-auto text-xs mt-1">
                            Delivery
                          </label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Delivery_CD"
                              name="Delivery_CD"
                              value={orderData.Delivery_CD ?? ""}
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                              disabled
                            >
                              <option value={orderData.Delivery_CD ?? ""}>
                                {orderData.Delivery_CD
                                  ? orderData.Delivery_CD
                                  : ""}
                              </option>
                            </select>
                            <input
                              name="Delivery_Name"
                              value={orderData.Delivery_Name ?? ""}
                              onChange={handleInputChangeforOrderData}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[0.5px]">
                          <label className="w-auto text-xs mt-1 mr-[10px]">
                            QC_Comp
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="I_Completed_Date"
                              value={
                                orderData.I_Completed_Date
                                  ? formatDate(orderData.I_Completed_Date ?? "")
                                  : ""
                              }
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 4 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className=" text-xs font-medium">
                            Confirm
                          </label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28"
                            name="Confirm_Delivery"
                            value={
                              orderData.Confirm_Delivery
                                ? formatDate(orderData.Confirm_Delivery ?? "")
                                : ""
                            }
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Draw</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60"
                            name="Product_Draw"
                            value={orderData.Product_Draw ?? ""} // เพิ่ม value จาก orderData
                            onChange={handleInputChangeforOrderData}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-3 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Price</label>
                          <div className="w-auto flex gap-1 -ml-2">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16"
                              name="Price_Name"
                              value={orderData.Price_Name ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Unit_Price"
                              value={orderData.Unit_Price ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_3</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Material3"
                              value={orderData.Material3 ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_5</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20"
                              name="Material5"
                              value={orderData.Material5 ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-2">
                          <label className="w-auto text-xs mt-1">
                            Supple_Docu
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32"
                              name="Supple_Docu"
                              value={orderData.Supple_Docu ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-9">
                          <label className="w-auto text-xs mt-1">Target</label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Target_CD"
                              name="Target_CD"
                              value={orderData.Target_CD ?? ""} // ใช้ค่า Target_CD จาก orderData
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                              disabled
                            >
                              <option value={orderData.Target_CD ?? ""}>
                                {orderData.Target_CD ? orderData.Target_CD : ""}
                              </option>
                            </select>

                            <input
                              name="Target_Name"
                              value={orderData.Target_Name ?? ""} // เพิ่ม value จาก orderData
                              onChange={handleInputChangeforOrderData}
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24"
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto -ml-[2.5px]">
                          <label className="w-auto text-xs mt-1">
                            Calc_Process
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                              name="Calc_Process_Date"
                              value={
                                orderData.Calc_Process_Date
                                  ? formatDate(
                                      orderData.Calc_Process_Date ?? ""
                                    )
                                  : ""
                              }
                              onChange={handleInputChangeforOrderData}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr className="border-y-[1px] border-gray-300" />

            <div className="p-4 space-y-4 min-w-[900px] flex-shrink-0 w-2/3">
              <div className="flex items-center space-x-2 justify-start">
                <label className="text-xs font-medium">Now Plan List</label>
              </div>
            </div>
            <div className="flex justify-center w-full mt-2">
              <div className="w-full max-w-full">
                <div
                  className="border border-gray-300 overflow-x-auto"
                  style={{ maxWidth: "100%" }}
                >
                  <table className="w-[1800px] border-separate border-spacing-0 text-xs">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-0 bg-gray-100 z-20">
                          Order_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[51px] bg-gray-100 z-20">
                          Parts_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[107px] bg-gray-100 z-20">
                          Parts_Name
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Material
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Split
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Spare_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_NG_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Note
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Remark
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Information
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Reg_Person
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Progress_Name
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Delivery
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Reg_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Complete_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Confirm_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Upd_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Od_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pt_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pr_No
                        </th>
                        {Array.from({ length: 36 }, (_, i) => (
                          <th
                            key={`thead1-${i}`}
                            className="border border-gray-300 py-2 px-5 text-center min-w-[100px]"
                          >
                            No{i + 1}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {plansData.map((plan, index) => (
                        <tr key={`planpaste-${index}`}>
                          <td className="border border-gray-300 h-8 sticky top-0 left-0 bg-gray-100 z-20 text-center">
                            {orderData.Order_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 sticky top-0 left-[51px] bg-gray-100 z-20 text-center">
                            {plan.Parts_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 sticky top-0 left-[107px] bg-gray-100 z-20 text-center">
                            {plan.Parts_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Material ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            <input
                              type="checkbox"
                              checked={plan.Pt_Split ?? ""}
                              readOnly
                              onChange={(e) => e.preventDefault()}
                            />
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Spare_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_NG_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Instructions ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Remark ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Information ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Reg_Person_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Progress_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Delivery ?? ""
                              ? formatDate(plan.Pt_Delivery)
                              : ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Reg_Date ?? ""
                              ? formatDate(plan.Pl_Reg_Date)
                              : ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Complete_Date ?? ""
                              ? formatDate(plan.Pt_Complete_Date)
                              : ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Confirm_Date ?? ""
                              ? formatDate(plan.Pt_Confirm_Date)
                              : ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Upd_Date ?? ""
                              ? formatDate(plan.Pl_Upd_Date)
                              : ""}
                          </td>

                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Od_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Pt_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Pr_No ?? ""}
                          </td>
                          {Array.from({ length: 36 }, (_, i) => (
                            <td
                              key={`PPCtable1-${i + 1}`}
                              className="border border-gray-300 h-8 text-center"
                            >
                              {plan[`PPC${i + 1}`] ?? ""}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/*ส่วน Copy */}
            <hr className="border-y-[1px] border-gray-300" />

            <div className="w-full h-full">
              <div className="flex space-x-4 w-full">
                <div className="p-4 space-y-4 min-w-[900px] flex-shrink-0 w-2/3">
                  <div className="flex items-center space-x-2 justify-start">
                    <label className="text-xs font-medium">
                      Order No for Copy
                    </label>
                    <input
                      type="text"
                      className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-36 bg-[#ccffff]"
                      id="Order_No"
                      value={orderNoInput}
                      onChange={handleInputChange}
                      onKeyDown={handleEnterPress}
                      onBlur={handleBlur}
                    />
                    <div className="pl-7">
                      <button
                        className="bg-blue-500 px-5 py-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center"
                        onClick={handleButtonClick}
                      >
                        <FaArrowDown size={24} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* ส่วนของ div ขวา */}
              </div>
              <hr className="border-y-[1px] border-gray-300" />
            </div>

            <div className="overflow-x-auto w-full">
              <div className="grid grid-cols-12 min-w-[1730px]">
                <div className="col-span-12 me-5 mt-5 ml-14">
                  <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-12 md:col-span-9">
                      {/* Group 1 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2 w-auto ">
                          <label className=" text-xs font-medium">
                            Order_Info
                          </label>
                          <div className="w-24"> </div>
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Customer</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24"
                            value={
                              BaseCopyPlanData?.order?.[0]?.Customer_CD ?? ""
                            }
                            onChange={handleInputChange}
                            readOnly
                          />
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-36 "
                            name="Customer_Abb"
                            value={
                              BaseCopyPlanData?.order?.[0]?.Customer_Abb ?? ""
                            }
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Product_Group
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-42 "
                              name="Product_Grp_CD"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Product_Grp_CD ??
                                ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Order_Control_Person
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24 "
                              name="Od_Ctl_Person_Name"
                              value={
                                BaseCopyPlanData?.order?.[0]
                                  ?.Od_Ctl_Person_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-3">
                          <label className="w-auto text-xs mt-1">
                            Sales_person
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32"
                              name="Sales_Person_Name"
                              value={
                                BaseCopyPlanData?.order?.[0]
                                  ?.Sales_Person_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-4 w-auto ml-4">
                          <label className="w-10 text-xs mt-1">Specific</label>
                          <div className="w-auto flex gap-1 ">
                            <select
                              id="Specific_CD"
                              name="Specific_CD"
                              className="border-2 border-gray-500 rounded-md px-2  text-xs  w-20 "
                              value={
                                BaseCopyPlanData?.order?.[0]?.Specific_CD ?? ""
                              }
                              onChange={handleInputChange}
                              disabled
                            >
                              <option
                                value={
                                  BaseCopyPlanData?.order?.[0]?.Specific_CD ??
                                  ""
                                }
                              >
                                {BaseCopyPlanData?.order?.[0]?.Specific_CD ??
                                  ""}
                              </option>
                            </select>
                            <input
                              name="Specific_Name"
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 "
                              value={
                                BaseCopyPlanData?.order?.[0]?.Specific_Name ??
                                ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-1">
                          <label className="w-auto text-xs mt-1">
                            Pd_Recevive
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24  "
                              name="Pd_Received_Date"
                              value={formatDate(
                                BaseCopyPlanData?.order?.[0]
                                  ?.Pd_Received_Date ?? ""
                              )}
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 2 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className=" text-xs font-medium">
                            Request
                          </label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28 "
                            name="Request_Delivery"
                            value={formatDate(
                              BaseCopyPlanData?.order?.[0]?.Request_Delivery ??
                                ""
                            )}
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Name</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60  "
                            name="Product_Name"
                            value={
                              BaseCopyPlanData?.order?.[0]?.Product_Name ?? ""
                            }
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Req1</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16 "
                              name="Request1"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Request1_CD ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                            <label className="w-auto text-xs mt-1">/3</label>
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16 "
                              name="Request3"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Request3_CD ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_1</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Material1"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Material1 ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-1">
                          <label className="w-10 text-xs mt-1">Coating</label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Coating_CD"
                              name="Coating_CD"
                              type="text"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Coating_CD ?? ""
                              }
                              onChange={handleInputChange}
                              disabled
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                            >
                              <option
                                value={
                                  BaseCopyPlanData?.order?.[0]?.Coating_CD ?? ""
                                }
                              >
                                {BaseCopyPlanData?.order?.[0]?.Coating_CD ?? ""}
                              </option>
                            </select>
                            <input
                              name="Coating_Name"
                              type="text"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Coating_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-20"
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-">
                          <label className="w-auto text-xs mt-1">Detail</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32  "
                              name="Coating"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Coating ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">
                            Od_Progress
                          </label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              disabled
                              id="Od_Progress_CD"
                              name="Od_Progress_CD"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Od_Progress_CD ??
                                ""
                              }
                              onChange={handleInputChange}
                              className="border-2 border-gray-500 rounded-md px-2 text-xs w-20"
                            >
                              <option
                                value={
                                  BaseCopyPlanData?.order?.[0]?.[0]
                                    ?.Od_Progress_CD ?? ""
                                }
                              >
                                {BaseCopyPlanData?.order?.[0]?.Od_Progress_CD ??
                                  ""}
                              </option>
                            </select>
                            <input
                              name="Od_Progress_Name"
                              type="text"
                              value={
                                BaseCopyPlanData?.order?.[0]
                                  ?.Od_Progress_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 "
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[0.5px]">
                          <label className="w-auto text-xs mt-1 mr-3">
                            Pd_Comp
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24  "
                              name="Pd_Complete_Date"
                              value={formatDate(
                                BaseCopyPlanData?.order?.[0]
                                  ?.Pd_Complete_Date ?? ""
                              )}
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 3 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className=" text-xs font-medium">
                            Product
                          </label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28 "
                            name="Request_Delivery"
                            value={formatDate(
                              BaseCopyPlanData?.order?.[0]?.Request_Delivery ??
                                ""
                            )}
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Size</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60  "
                            name="Product_Size"
                            value={
                              BaseCopyPlanData?.order?.[0]?.Product_Size ?? ""
                            }
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-3 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Qty</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16 "
                              name="Quantity"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Quantity ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Unit_Name"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Unit_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_2</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Material2"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Material2 ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_4</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Material4"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Material4 ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-11">
                          <label className="w-auto text-xs mt-1">
                            Product_Docu
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32  "
                              name="Product_Docu"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Product_Docu ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[25px]">
                          <label className="w-auto text-xs mt-1">
                            Delivery
                          </label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Delivery_CD"
                              name="Delivery_CD"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Delivery_CD ?? ""
                              }
                              onChange={handleInputChange}
                              disabled
                              className="border-2 border-gray-500 rounded-md px-2 text-xs  w-20 "
                            >
                              <option
                                value={
                                  BaseCopyPlanData?.order?.[0]?.Delivery_CD ??
                                  ""
                                }
                              >
                                {BaseCopyPlanData?.order?.[0]?.Delivery_CD ??
                                  ""}
                              </option>
                            </select>
                            <input
                              name="Delivery_Name"
                              type="text"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Delivery_Name ??
                                ""
                              }
                              onChange={handleInputChange}
                              readOnly
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 "
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[0.5px]">
                          <label className="w-auto text-xs mt-1 mr-[10px]">
                            QC_Comp
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24  "
                              name="I_Completed_Date"
                              value={formatDate(
                                BaseCopyPlanData?.order?.[0]
                                  ?.I_Completed_Date ?? ""
                              )}
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>

                      {/* Group 4 */}
                      <div className="gap-2 flex mb-4 justify-start me-5">
                        <div className="flex gap-2">
                          <label className=" text-xs font-medium">
                            Confirm
                          </label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-28 "
                            name="Confirm_Delivery"
                            value={formatDate(
                              BaseCopyPlanData?.order?.[0]?.Confirm_Delivery ??
                                ""
                            )}
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-1 w-auto item">
                          <label className="w-16 text-xs mt-1">Pd_Draw</label>
                          <input
                            type="text"
                            className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-60  "
                            name="Product_Draw"
                            value={
                              BaseCopyPlanData?.order?.[0]?.Product_Draw ?? ""
                            }
                            onChange={handleInputChange}
                            readOnly
                          />
                        </div>
                        <div className="flex gap-3 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Price</label>
                          <div className="w-auto flex gap-1 -ml-2">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-16 "
                              name="Price_CD"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Price_CD ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Unit_Price"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Unit_Price ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_3</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Material3"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Material3 ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-2 w-auto pl-[2px]">
                          <label className="w-auto text-xs mt-1">Mate_5</label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-20 "
                              name="Material5"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Material5 ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto pl-12">
                          <label className="w-auto text-xs mt-1">
                            Supple_Docu
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32  "
                              name="Supple_Docu"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Supple_Docu ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto ml-[35px]">
                          <label className="w-auto text-xs mt-1">Target</label>
                          <div className="w-auto flex gap-1 mr-1">
                            <select
                              id="Target_CD"
                              name="Target_CD"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Target_CD ?? ""
                              }
                              onChange={handleInputChange}
                              disabled
                              className="border-2 border-gray-500 rounded-md px-2 text-xs  w-20 "
                            >
                              <option
                                value={
                                  BaseCopyPlanData?.order?.[0]?.Target_CD ?? ""
                                }
                              ></option>
                            </select>
                            <input
                              name="Target_Name"
                              value={
                                BaseCopyPlanData?.order?.[0]?.Target_Name ?? ""
                              }
                              onChange={handleInputChange}
                              readOnly
                              type="text"
                              className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-24 "
                            />
                          </div>
                        </div>
                        <div className="flex gap-1 w-auto -ml-[2.5px]">
                          <label className="w-auto text-xs mt-1 ">
                            Calc_Process
                          </label>
                          <div className="w-auto flex gap-1">
                            <input
                              type="text"
                              className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-24  "
                              name="Calc_Process_Date"
                              value={formatDate(
                                BaseCopyPlanData?.order?.[0]
                                  ?.Calc_Process_Date ?? ""
                              )}
                              onChange={handleInputChange}
                              readOnly
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <hr className="border-y-[1px] border-gray-300" />

            <div className="p-4 space-y-4 min-w-[900px] flex-shrink-0 w-2/3">
              <div className="flex items-center space-x-2 justify-start">
                <label className="text-xs font-medium">Copy Plan List</label>
              </div>
            </div>
            <div className="flex justify-center w-full mt-2">
              <div className="w-full max-w-full">
                <div
                  className="border border-gray-300 overflow-x-auto"
                  style={{ maxWidth: "100%" }}
                >
                  <table className="w-[1800px] border-separate border-spacing-0 text-xs">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-0 bg-gray-100 z-20">
                          Order_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[51px] bg-gray-100 z-20">
                          Parts_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center sticky top-0 left-[107px] bg-gray-100 z-20">
                          Parts_Name
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Material
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Split
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Spare_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_NG_Qty
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Note
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Remark
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Information
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Reg_Person
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Progress_Name
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Delivery
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Reg_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Complete_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pt_Confirm_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Pl_Upd_Date
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Od_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pt_No
                        </th>
                        <th className="border border-gray-300 py-2 px-5 text-center">
                          Connect_Pr_No
                        </th>
                        {Array.from({ length: 36 }, (_, i) => (
                          <th
                            key={`thead2-${i}`}
                            className="border border-gray-300 py-2 px-5 text-center min-w-[100px]"
                          >
                            No{i + 1}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {BaseCopyPlanData?.plans?.map((plan, index) => (
                        <tr key={`plancopy-${index}`}>
                          <td className="border border-gray-300 h-8 sticky top-0 left-0 bg-gray-100 z-20 text-center">
                            {plan.Order_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 sticky top-0 left-[51px] bg-gray-100 z-20 text-center">
                            {plan.Parts_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 sticky top-0 left-[107px] bg-gray-100 z-20 text-center">
                            {plan.Parts_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Material ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            <input
                              type="checkbox"
                              checked={plan.Pt_Split ?? ""}
                              readOnly
                              onChange={(e) => e.preventDefault()}
                            />
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Spare_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_NG_Qty ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Instructions ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Remark ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pt_Information ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Reg_Person_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Pl_Progress_CD ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {formatDate(plan.Pt_Delivery ?? "")}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {formatDate(plan.Pl_Reg_Date ?? "")}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {formatDate(plan.Pt_Complete_Date ?? "")}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {formatDate(plan.Pt_Confirm_Date ?? "")}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {formatDate(plan.Pl_Upd_Date ?? "")}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Od_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Pt_No ?? ""}
                          </td>
                          <td className="border border-gray-300 h-8 text-center">
                            {plan.Connect_Pr_No ?? ""}
                          </td>
                          {Array.from({ length: 36 }, (_, i) => (
                            <td
                              key={`PPC-${i + 1}`}
                              className="border border-gray-300 h-8 text-center"
                            >
                              {plan[`PPC${i + 1}`] ?? ""}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Copy Target List */}
            <hr className="border-y-[1px] border-gray-300" />
            <div className="p-4 space-y-4 min-w-[900px] flex-shrink-0 w-2/3">
              <div className="flex items-center space-x-2 justify-start">
                <label className="text-xs font-medium">Copy Target List</label>
              </div>
            </div>

            <hr className="border-y-[1px] border-gray-300" />

            <div className="flex flex-col md:flex-row w-full gap-4 ml-5">
              {/* Left side - Table */}
              <div className="w-full md:w-1/2 mt-2">
                <div style={{ maxWidth: "100%" }}>
                  <table
                    className="w-full border-separate border-spacing-0 text-xs"
                    style={{ maxWidth: "85%" }}
                  >
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="border border-gray-300 py-2 px-2 text-center min-w-[50px]">
                          Quote
                        </th>
                        <th className="border border-gray-300 py-2 px-2 text-center min-w-[100px]">
                          Order_No
                        </th>
                        <th className="border border-gray-300 py-2 px-2 text-center min-w-[70px]">
                          Parts_No
                        </th>
                        <th className="border border-gray-300 py-2 px-2 text-center min-w-[100px]">
                          Parts_CD
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {targetsData.length > 0
                        ? targetsData.map((target, index) => (
                            <tr key={`target-${index}`}>
                              <td className="border border-gray-300 h-8 text-center">
                                <input
                                  type="checkbox"
                                  checked={selectedTargets.includes(index)}
                                  onChange={() => handleCheckboxChange(index)}
                                />
                              </td>
                              <td className="border border-gray-300 h-8 text-center">
                                {target.Order_No ?? ""}
                              </td>
                              <td className="border border-gray-300 h-8 text-center">
                                <input
                                  type="text"
                                  value={target.Parts_No}
                                  onChange={(e) =>
                                    handlePartsNoChange(e, index)
                                  }
                                  className="p-1 text-center w-[70px]"
                                  disabled={!selectedTargets.includes(index)}
                                />
                              </td>
                              <td className="border border-gray-300 h-8 text-center">
                                <Select
                                  className="w-full text-black"
                                  options={options}
                                  value={
                                    options.find(
                                      (opt) => opt.value === target.Parts_CD
                                    ) || {
                                      label: target.Parts_CD,
                                      value: target.Parts_CD,
                                    }
                                  }
                                  onChange={(selectedOption) =>
                                    handlePartsCDChange(
                                      {
                                        target: {
                                          value: selectedOption?.value ?? "",
                                        },
                                      },
                                      index
                                    )
                                  }
                                  disabled={!selectedTargets.includes(index)}
                                />
                              </td>
                            </tr>
                          ))
                        : null}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Right side - Text content */}

              <div className="w-full md:w-1/2 p-4">
                <div
                  className="border border-gray-300 "
                  style={{ maxWidth: "50%" }}
                >
                  <div className="mb-2">
                    <label className="inline-flex items-center ml-2">
                      Quote_Time
                    </label>
                    <label className="inline-flex items-center ml-4">
                      <input
                        type="radio"
                        name="time_option"
                        value="Plan_Time"
                        checked={selectedTimeOption === "1"}
                        onChange={handleRadioChange}
                        className="mr-2"
                      />
                      <span>Plan_Time</span>
                    </label>
                    <label className="inline-flex items-center ml-4">
                      <input
                        type="radio"
                        name="time_option"
                        value="Result_Time"
                        checked={selectedTimeOption === "2"}
                        onChange={handleRadioChange}
                        className="mr-2"
                      />
                      <span>Result_time</span>
                    </label>
                  </div>
                </div>

                <div className="mb-2 mt-1">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="Product_Name_Quote"
                      className="mr-2"
                    />
                    <span>Product_Name_Quote</span>
                  </label>

                  <label className="inline-flex items-center mt-2 lg:ml-4 lg:mt-0">
                    <input
                      type="checkbox"
                      name="Supplement_Docu_Quote"
                      className="mr-2"
                    />
                    <span>Supplement_Docu_Quote</span>
                  </label>
                </div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="Product_Size_Quote"
                      className="mr-2"
                    />
                    <span>Product_Size_Quote</span>
                  </label>
                </div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="Product_Draw_Quote"
                      className="mr-2"
                    />
                    <span>Product_Draw_Quote</span>
                  </label>
                </div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="Plan_Process_Date_Quote"
                      className="mr-2"
                    />
                    <span>Plan_Process_Date_Quote</span>
                  </label>
                </div>
                <div className="mb-2">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="Supplement_Docu_Quote"
                      className="mr-2"
                    />
                    <span>Supplement_Docu_Quote</span>
                  </label>
                </div>

                <div className="w-auto flex gap-1 mr-1">
                  <select
                    id="Od_Progress_CD"
                    name="Od_Progress_CD"
                    className="border-2 border-gray-500 rounded-md px-2 text-xs w-20 bg-[#ffff99]"
                  >
                    <option></option>
                  </select>
                  <p className="mb-2">Derivry Paste on Parts Derivery</p>
                </div>
              </div>
            </div>

            {/* Start Text Footer */}
            <div className="text-red-600 font-semibold p-2">
              <span>
                Please change "Parts_No" of "Copy Target List" so that does not
                overlap with "Parts_No" of "Now Plan List"
              </span>
            </div>
            {/* End Text Footer */}
          </div>
        </div>

        <div className="flex flex-col lg:flex-row justify-between items-center mt-3 bg-white">
          <div className="p-3">
            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div className="grid grid-cols-4 gap-2">
                <button
                  id="F1"
                  disabled={!buttonState.F1}
                  className={`bg-blue-500 p- py-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  Serch <br /> (F1)
                </button>
                <button
                  id="F2"
                  disabled={!buttonState.F2}
                  onClick={handleF2Click}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  Setting <br /> (F2)
                </button>
                <button
                  id="F3"
                  disabled={!buttonState.F3}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  (F3)
                </button>
                <button
                  id="F4"
                  disabled={!buttonState.F4}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  (F4)
                </button>
              </div>
              <div className="grid grid-cols-4 gap-2">
                <button
                  id="F5"
                  disabled={!buttonState.F5}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  All Selete <br /> (F5)
                </button>
                <button
                  id="F6"
                  disabled={!buttonState.F6}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  All PS <br /> 全頁 (F6)
                </button>
                <button
                  id="F7"
                  disabled={!buttonState.F7}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  (F7)
                </button>
                <button
                  id="F8"
                  disabled={!buttonState.F8}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  (F8)
                </button>
              </div>
              <div className="grid grid-cols-4 gap-2">
                <button
                  id="F9"
                  onClick={handleF9Click}
                  disabled={!buttonState.F9}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  Action <br /> (F9)
                </button>
                <button
                  id="F10"
                  disabled={!buttonState.F10}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  AllCancel <br /> (F10)
                </button>
                <button
                  id="F11"
                  disabled={!buttonState.F11}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  Clear <br /> (F11)
                </button>
                <button
                  id="F12"
                  disabled={!buttonState.F12}
                  onClick={handleF12Click}
                  className={`bg-blue-500 p-1 rounded-lg hover:bg-blue-700 font-medium text-sm text-white w-auto text-center disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500`}
                >
                  Exit <br /> 終了 (F12)
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
