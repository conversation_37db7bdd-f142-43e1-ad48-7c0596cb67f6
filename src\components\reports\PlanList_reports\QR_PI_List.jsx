import React, { useState, useEffect } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function QR_PI_List() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [planlistData, setPlanlistData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // ฟังก์ชันในการแปลงวันที่
  const formatDate = (date) => {
    if (!date || isNaN(Date.parse(date))) return date; // ถ้าไม่ใช่วันที่ให้คืนค่าตัวแปรเดิม
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0"); // เดือนเริ่มจาก 0
    const year = d.getFullYear();
    return `${day}-${month}-${year}`;
  };
  useEffect(() => {
    const handleMessage = (event) => {
      // ตรวจสอบ origin
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const { status, data } = event.data;

      if (status === 200) {
        // ตรวจสอบว่า data.Plan เป็น array หรือไม่
        if (Array.isArray(data.Plan)) {
          setPlanlistData(data.Plan); // ใช้ data.Plan เพราะข้อมูลที่ได้รับเป็น array
        } else {
          console.error("Received data.Plan is not an array.");
        }
      } else {
        console.error("Failed to receive data.");
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  console.log("planlistData", planlistData);

  const totalPages = Math.ceil(planlistData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = planlistData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  return (
    <div className="p-8 bg-gray-50 rounded-lg shadow-lg max-w-screen-lg mx-auto">
      <h2 className="text-3xl font-semibold text-blue-600 text-center mb-6">
        QR_PI_List_View
      </h2>

      <div className="overflow-x-auto">
        <table className="max-w-[1500px] table-auto border-collapse">
          <thead>
            <tr>
              {/* หัวตาราง */}
              <th className="border px-10 py-1 text-left">GrpSI</th>
              <th className="border px-10 py-1 text-left">Order_No</th>
              <th className="border px-10 py-1 text-left">Product_Grp_CD</th>
              <th className="border px-10 py-1 text-left">Customer_CD</th>
              <th className="border px-10 py-1 text-left">NAV_Name</th>
              <th className="border px-10 py-1 text-left">Product_Name</th>
              <th className="border px-10 py-1 text-left">NAV_Size</th>
              <th className="border px-10 py-1 text-left">Product_Size</th>
              <th className="border px-10 py-1 text-left">Tolerance</th>
              <th className="border px-10 py-1 text-left">Customer_Draw</th>
              <th className="border px-10 py-1 text-left">Company_Draw</th>
              <th className="border px-10 py-1 text-left">Product_Draw</th>
              <th className="border px-10 py-1 text-left">Quantity</th>
              <th className="border px-10 py-1 text-left">Pd_Target_Qty</th>
              <th className="border px-10 py-1 text-left">Pd_Complete_Qty</th>
              <th className="border px-10 py-1 text-left">I_Complete_Qty</th>
              <th className="border px-10 py-1 text-left">Shipment_Qty</th>
              <th className="border px-10 py-1 text-left">Pd_Split_Qty</th>
              <th className="border px-10 py-1 text-left">Pd_Calc_Qty</th>
              <th className="border px-10 py-1 text-left">NG_Qty</th>
              <th className="border px-10 py-1 text-left">Unit_CD</th>
              <th className="border px-10 py-1 text-left">Sales_Grp_CD</th>
              <th className="border px-10 py-1 text-left">Sales_Person_CD</th>
              <th className="border px-10 py-1 text-left">Request1_CD</th>
              <th className="border px-10 py-1 text-left">Request2_CD</th>
              <th className="border px-10 py-1 text-left">Request3_CD</th>
              <th className="border px-10 py-1 text-left">Material1</th>
              <th className="border px-10 py-1 text-left">H_Treatment1</th>
              <th className="border px-10 py-1 text-left">Material2</th>
              <th className="border px-10 py-1 text-left">H_Treatment2</th>
              <th className="border px-10 py-1 text-left">Material3</th>
              <th className="border px-10 py-1 text-left">H_Treatment3</th>
              <th className="border px-10 py-1 text-left">Material4</th>
              <th className="border px-10 py-1 text-left">H_Treatment4</th>
              <th className="border px-10 py-1 text-left">Material5</th>
              <th className="border px-10 py-1 text-left">H_Treatment5</th>
              <th className="border px-10 py-1 text-left">Coating_CD</th>
              <th className="border px-10 py-1 text-left">Coating</th>
              <th className="border px-10 py-1 text-left">Quote_No</th>
              <th className="border px-10 py-1 text-left">Quote_CD</th>
              <th className="border px-10 py-1 text-left">Od_No_of_Pd_Split</th>
              <th className="border px-10 py-1 text-left">Item0_CD</th>
              <th className="border px-10 py-1 text-left">Item1_CD</th>
              <th className="border px-10 py-1 text-left">Item2_CD</th>
              <th className="border px-10 py-1 text-left">Item3_CD</th>
              <th className="border px-10 py-1 text-left">Item4_CD</th>
              <th className="border px-10 py-1 text-left">Custom_Material</th>
              <th className="border px-10 py-1 text-left">Od_No_of_Custom</th>
              <th className="border px-10 py-1 text-left">Supply_CD</th>
              <th className="border px-10 py-1 text-left">Destination_CD</th>
              <th className="border px-10 py-1 text-left">Contract_Docu_CD</th>
              <th className="border px-10 py-1 text-left">Price_CD</th>
              <th className="border px-10 py-1 text-left">Unit_Price</th>
              <th className="border px-10 py-1 text-left">Specific_CD</th>
              <th className="border px-10 py-1 text-left">Od_Progress_CD</th>
              <th className="border px-10 py-1 text-left">Delivery_CD</th>
              <th className="border px-10 py-1 text-left">Schedule_CD</th>
              <th className="border px-10 py-1 text-left">Target_CD</th>
              <th className="border px-10 py-1 text-left">Product_Docu</th>
              <th className="border px-10 py-1 text-left">Procure_Docu</th>
              <th className="border px-10 py-1 text-left">Outside_Docu</th>
              <th className="border px-10 py-1 text-left">Inspect_Docu</th>
              <th className="border px-10 py-1 text-left">Send_Docu</th>
              <th className="border px-10 py-1 text-left">Supple_Docu</th>
              <th className="border px-10 py-1 text-left">Sl_Instructions</th>
              <th className="border px-10 py-1 text-left">Pd_Instructions</th>
              <th className="border px-10 py-1 text-left">Pd_Remark</th>
              <th className="border px-10 py-1 text-left">I_Remark</th>
              <th className="border px-10 py-1 text-left">Od_Ctl_Person_CD</th>
              <th className="border px-10 py-1 text-left">Od_Reg_Person_CD</th>
              <th className="border px-10 py-1 text-left">Od_Upd_Person_CD</th>
              <th className="border px-10 py-1 text-left">Request_Delivery</th>
              <th className="border px-10 py-1 text-left">Product_Delivery</th>
              <th className="border px-10 py-1 text-left">Confirm_Delivery</th>
              <th className="border px-10 py-1 text-left">NAV_Delivery</th>
              <th className="border px-10 py-1 text-left">ASP_Delivery</th>
              <th className="border px-10 py-1 text-left">Order_Date</th>
              <th className="border px-10 py-1 text-left">Pd_Received_Date</th>
              <th className="border px-10 py-1 text-left">Pd_Complete_Date</th>
              <th className="border px-10 py-1 text-left">I_Completed_Date</th>
              <th className="border px-10 py-1 text-left">Shipment_Date</th>
              <th className="border px-10 py-1 text-left">Pd_Calc_Date</th>
              <th className="border px-10 py-1 text-left">Calc_Process_Date</th>
              <th className="border px-10 py-1 text-left">Rs_Confirm_Date</th>
              <th className="border px-10 py-1 text-left">Od_Reg_Date</th>
              <th className="border px-10 py-1 text-left">Od_Upd_Date</th>
              <th className="border px-10 py-1 text-left">Od_NAV_Upd_Date</th>
              <th className="border px-10 py-1 text-left">Carbide_Cost</th>
              <th className="border px-10 py-1 text-left">Steel_Cost</th>
              <th className="border px-10 py-1 text-left">Outsourcing_Cost</th>
              <th className="border px-10 py-1 text-left">H_Treatment_Cost</th>
              <th className="border px-10 py-1 text-left">Coating_Cost</th>
              <th className="border px-10 py-1 text-left">Electrode_Cost</th>
              <th className="border px-10 py-1 text-left">Electroplate_Cost</th>
              <th className="border px-10 py-1 text-left">Tooling_Cost</th>
              <th className="border px-10 py-1 text-left">Jig_Cost</th>
              <th className="border px-10 py-1 text-left">Fixtures_Cost</th>
              <th className="border px-10 py-1 text-left">Od_CAT1</th>
              <th className="border px-10 py-1 text-left">Od_CAT2</th>
              <th className="border px-10 py-1 text-left">Od_CAT3</th>
              <th className="border px-10 py-1 text-left">Od_Pending</th>
              <th className="border px-10 py-1 text-left">Temp_Shipment</th>
              <th className="border px-10 py-1 text-left">Unreceived</th>
              <th className="border px-10 py-1 text-left">Current_Order</th>
              <th className="border px-10 py-1 text-left">Month_Plan</th>
              <th className="border px-10 py-1 text-left">Week_Plan</th>
              <th className="border px-10 py-1 text-left">Today_Plan</th>
              <th className="border px-10 py-1 text-left">Must_Delivery</th>
              <th className="border px-10 py-1 text-left">Into_I</th>
              <th className="border px-10 py-1 text-left">Input_Confirm</th>
              <th className="border px-10 py-1 text-left">Pd_Confirm</th>
              <th className="border px-10 py-1 text-left">I_Confirm</th>
              <th className="border px-10 py-1 text-left">Od_Confirm</th>
              <th className="border px-10 py-1 text-left">I_Target</th>
              <th className="border px-10 py-1 text-left">Urgent_Goods</th>
              <th className="border px-10 py-1 text-left">Delivery1</th>
              <th className="border px-10 py-1 text-left">Delivery2</th>
              <th className="border px-10 py-1 text-left">Delivery3</th>
              <th className="border px-10 py-1 text-left">Item1</th>
              <th className="border px-10 py-1 text-left">Item1_Grp</th>
              <th className="border px-10 py-1 text-left">WorkG_Symbol</th>
              <th className="border px-10 py-1 text-left">Customer_Abb</th>
              <th className="border px-10 py-1 text-left">Parts_No</th>
              <th className="border px-10 py-1 text-left">OdPt_No</th>
              <th className="border px-10 py-1 text-left">Parts_CD</th>
              <th className="border px-10 py-1 text-left">Pt_Material</th>
              <th className="border px-10 py-1 text-left">Price</th>
              <th className="border px-10 py-1 text-left">Pt_Qty</th>
              <th className="border px-10 py-1 text-left">Pt_Split</th>
              <th className="border px-10 py-1 text-left">Pt_Spare_Qty</th>
              <th className="border px-10 py-1 text-left">Pt_NG_Qty</th>
              <th className="border px-10 py-1 text-left">Target_Amount</th>
              <th className="border px-10 py-1 text-left">Pl_Progress_CD</th>
              <th className="border px-10 py-1 text-left">Pl_Schedule_CD</th>
              <th className="border px-10 py-1 text-left">Pt_Instructions</th>
              <th className="border px-10 py-1 text-left">Pt_Remark</th>
              <th className="border px-10 py-1 text-left">Pt_Information</th>
              <th className="border px-10 py-1 text-left">Pl_Reg_Person_CD</th>
              <th className="border px-10 py-1 text-left">Pt_Delivery</th>
              <th className="border px-10 py-1 text-left">Pt_Complete_Date</th>
              <th className="border px-10 py-1 text-left">Money_Object</th>
              <th className="border px-10 py-1 text-left">Outside</th>
              <th className="border px-10 py-1 text-left">Pt_Pending</th>
              <th className="border px-10 py-1 text-left">Pt_CAT1</th>
              <th className="border px-10 py-1 text-left">Pt_CAT2</th>
              <th className="border px-10 py-1 text-left">Max_No</th>
              <th className="border px-10 py-1 text-left">Priority_Rate</th>
              <th className="border px-10 py-1 text-left">PP1</th>
              <th className="border px-10 py-1 text-left">PP2</th>
              <th className="border px-10 py-1 text-left">PP3</th>
              <th className="border px-10 py-1 text-left">PP4</th>
              <th className="border px-10 py-1 text-left">PP5</th>
              <th className="border px-10 py-1 text-left">PP6</th>
              <th className="border px-10 py-1 text-left">PP7</th>
              <th className="border px-10 py-1 text-left">PP8</th>
              <th className="border px-10 py-1 text-left">PP9</th>
              <th className="border px-10 py-1 text-left">PP10</th>
              <th className="border px-10 py-1 text-left">PP11</th>
              <th className="border px-10 py-1 text-left">PP12</th>
              <th className="border px-10 py-1 text-left">PP13</th>
              <th className="border px-10 py-1 text-left">PP14</th>
              <th className="border px-10 py-1 text-left">PP15</th>
              <th className="border px-10 py-1 text-left">PP16</th>
              <th className="border px-10 py-1 text-left">PP17</th>
              <th className="border px-10 py-1 text-left">PP18</th>
              <th className="border px-10 py-1 text-left">PP19</th>
              <th className="border px-10 py-1 text-left">PP20</th>
              <th className="border px-10 py-1 text-left">PP21</th>
              <th className="border px-10 py-1 text-left">PP22</th>
              <th className="border px-10 py-1 text-left">PP23</th>
              <th className="border px-10 py-1 text-left">PP24</th>
              <th className="border px-10 py-1 text-left">PP25</th>
              <th className="border px-10 py-1 text-left">PP26</th>
              <th className="border px-10 py-1 text-left">PP27</th>
              <th className="border px-10 py-1 text-left">PP28</th>
              <th className="border px-10 py-1 text-left">PP29</th>
              <th className="border px-10 py-1 text-left">PP30</th>
              <th className="border px-10 py-1 text-left">PP31</th>
              <th className="border px-10 py-1 text-left">PP32</th>
              <th className="border px-10 py-1 text-left">PP33</th>
              <th className="border px-10 py-1 text-left">PP34</th>
              <th className="border px-10 py-1 text-left">PP35</th>
              <th className="border px-10 py-1 text-left">PP36</th>
              <th className="border px-10 py-1 text-left">PPD1</th>
              <th className="border px-10 py-1 text-left">PPD2</th>
              <th className="border px-10 py-1 text-left">PPD3</th>
              <th className="border px-10 py-1 text-left">PPD4</th>
              <th className="border px-10 py-1 text-left">PPD5</th>
              <th className="border px-10 py-1 text-left">PPD6</th>
              <th className="border px-10 py-1 text-left">PPD7</th>
              <th className="border px-10 py-1 text-left">PPD8</th>
              <th className="border px-10 py-1 text-left">PPD9</th>
              <th className="border px-10 py-1 text-left">PPD10</th>
              <th className="border px-10 py-1 text-left">PPD11</th>
              <th className="border px-10 py-1 text-left">PPD12</th>
              <th className="border px-10 py-1 text-left">PPD13</th>
              <th className="border px-10 py-1 text-left">PPD14</th>
              <th className="border px-10 py-1 text-left">PPD15</th>
              <th className="border px-10 py-1 text-left">PPD16</th>
              <th className="border px-10 py-1 text-left">PPD17</th>
              <th className="border px-10 py-1 text-left">PPD18</th>
              <th className="border px-10 py-1 text-left">PPD19</th>
              <th className="border px-10 py-1 text-left">PPD20</th>
              <th className="border px-10 py-1 text-left">PPD21</th>
              <th className="border px-10 py-1 text-left">PPD22</th>
              <th className="border px-10 py-1 text-left">PPD23</th>
              <th className="border px-10 py-1 text-left">PPD24</th>
              <th className="border px-10 py-1 text-left">PPD25</th>
              <th className="border px-10 py-1 text-left">PPD26</th>
              <th className="border px-10 py-1 text-left">PPD27</th>
              <th className="border px-10 py-1 text-left">PPD28</th>
              <th className="border px-10 py-1 text-left">PPD29</th>
              <th className="border px-10 py-1 text-left">PPD30</th>
              <th className="border px-10 py-1 text-left">PPD31</th>
              <th className="border px-10 py-1 text-left">PPD32</th>
              <th className="border px-10 py-1 text-left">PPD33</th>
              <th className="border px-10 py-1 text-left">PPD34</th>
              <th className="border px-10 py-1 text-left">PPD35</th>
              <th className="border px-10 py-1 text-left">PPD36</th>
              <th className="border px-10 py-1 text-left">RPD1</th>
              <th className="border px-10 py-1 text-left">RPD2</th>
              <th className="border px-10 py-1 text-left">RPD3</th>
              <th className="border px-10 py-1 text-left">RPD4</th>
              <th className="border px-10 py-1 text-left">RPD5</th>
              <th className="border px-10 py-1 text-left">RPD6</th>
              <th className="border px-10 py-1 text-left">RPD7</th>
              <th className="border px-10 py-1 text-left">RPD8</th>
              <th className="border px-10 py-1 text-left">RPD9</th>
              <th className="border px-10 py-1 text-left">RPD10</th>
              <th className="border px-10 py-1 text-left">RPD11</th>
              <th className="border px-10 py-1 text-left">RPD12</th>
              <th className="border px-10 py-1 text-left">RPD13</th>
              <th className="border px-10 py-1 text-left">RPD14</th>
              <th className="border px-10 py-1 text-left">RPD15</th>
              <th className="border px-10 py-1 text-left">RPD16</th>
              <th className="border px-10 py-1 text-left">RPD17</th>
              <th className="border px-10 py-1 text-left">RPD18</th>
              <th className="border px-10 py-1 text-left">RPD19</th>
              <th className="border px-10 py-1 text-left">RPD20</th>
              <th className="border px-10 py-1 text-left">RPD21</th>
              <th className="border px-10 py-1 text-left">RPD22</th>
              <th className="border px-10 py-1 text-left">RPD23</th>
              <th className="border px-10 py-1 text-left">RPD24</th>
              <th className="border px-10 py-1 text-left">RPD25</th>
              <th className="border px-10 py-1 text-left">RPD26</th>
              <th className="border px-10 py-1 text-left">RPD27</th>
              <th className="border px-10 py-1 text-left">RPD28</th>
              <th className="border px-10 py-1 text-left">RPD29</th>
              <th className="border px-10 py-1 text-left">RPD30</th>
              <th className="border px-10 py-1 text-left">RPD31</th>
              <th className="border px-10 py-1 text-left">RPD32</th>
              <th className="border px-10 py-1 text-left">RPD33</th>
              <th className="border px-10 py-1 text-left">RPD34</th>
              <th className="border px-10 py-1 text-left">RPD35</th>
              <th className="border px-10 py-1 text-left">RPD36</th>

              {/* เพิ่มหัวตารางอื่นๆ ตามต้องการ */}
            </tr>
          </thead>
          <tbody>
            {displayedData.length > 0 ? (
              displayedData.map((item, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  {/* แสดงข้อมูลจากตาราง */}
                  <td className="border px-4 py-2">{formatDate(item.GrpSI)}</td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Order_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Grp_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Customer_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.NAV_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.NAV_Size)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Size)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Tolerance)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Customer_Draw)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Company_Draw)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Draw)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Quantity)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Target_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Complete_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.I_Complete_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Shipment_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Split_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Calc_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.NG_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Unit_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Sales_Grp_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Sales_Person_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Request1_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Request2_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Request3_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Material1)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment1)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Material2)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment2)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Material3)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment3)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Material4)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment4)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Material5)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment5)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Coating_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Coating)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Quote_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Quote_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_No_of_Pd_Split)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item0_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item1_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item2_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item3_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item4_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Custom_Material)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_No_of_Custom)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Supply_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Destination_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Contract_Docu_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Price_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Unit_Price)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Specific_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Progress_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Delivery_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Schedule_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Target_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Procure_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Outside_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Inspect_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Send_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Supple_Docu)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Sl_Instructions)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Instructions)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Remark)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.I_Remark)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Ctl_Person_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Reg_Person_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Upd_Person_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Request_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Product_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Confirm_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.NAV_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.ASP_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Order_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Received_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Complete_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.I_Completed_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Shipment_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Calc_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Calc_Process_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Rs_Confirm_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Reg_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Upd_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_NAV_Upd_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Carbide_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Steel_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Outsourcing_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.H_Treatment_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Coating_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Electrode_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Electroplate_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Tooling_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Jig_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Fixtures_Cost)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_CAT1)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_CAT2)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_CAT3)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Pending)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Temp_Shipment)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Unreceived)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Current_Order)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Month_Plan)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Week_Plan)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Today_Plan)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Must_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Into_I)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Input_Confirm)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pd_Confirm)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.I_Confirm)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Od_Confirm)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.I_Target)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Urgent_Goods)}
                  </td>

                  <td className="border px-4 py-2">
                    {formatDate(item.Delivery1)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Delivery2)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Delivery3)}
                  </td>
                  <td className="border px-4 py-2">{formatDate(item.Item1)}</td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Item1_Grp)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.WorkG_Symbol)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Customer_Abb)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Parts_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.OdPt_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Parts_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Material)}
                  </td>
                  <td className="border px-4 py-2">{formatDate(item.Price)}</td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Split)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Spare_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_NG_Qty)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Target_Amount)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pl_Progress_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pl_Schedule_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Instructions)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Remark)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Information)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pl_Reg_Person_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Delivery)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Complete_Date)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Money_Object)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Outside)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_Pending)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_CAT1)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Pt_CAT2)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Max_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDate(item.Priority_Rate)}
                  </td>
                  <td className="border px-4 py-2">{formatDate(item.PP1)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP2)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP3)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP4)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP5)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP6)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP7)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP8)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP9)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP10)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP11)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP12)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP13)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP14)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP15)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP16)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP17)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP18)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP19)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP20)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP21)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP22)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP23)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP24)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP25)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP26)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP27)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP28)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP29)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP30)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP31)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP32)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP33)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP34)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP35)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PP36)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD1)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD2)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD3)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD4)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD5)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD6)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD7)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD8)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD9)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD10)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD11)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD12)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD13)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD14)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD15)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD16)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD17)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD18)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD19)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD20)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD21)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD22)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD23)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD24)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD25)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD26)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD27)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD28)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD29)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD30)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD31)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD32)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD33)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD34)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD35)}</td>
                  <td className="border px-4 py-2">{formatDate(item.PPD36)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD1)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD2)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD3)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD4)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD5)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD6)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD7)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD8)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD9)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD10)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD11)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD12)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD13)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD14)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD15)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD16)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD17)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD18)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD19)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD20)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD21)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD22)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD23)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD24)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD25)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD26)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD27)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD28)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD29)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD30)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD31)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD32)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD33)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD34)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD35)}</td>
                  <td className="border px-4 py-2">{formatDate(item.RPD36)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3" className="border px-4 py-2 text-center">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={goToPrevPage}
          disabled={currentPage === 1}
          className={`p-2 rounded-full ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronLeft size={20} />
        </button>

        <div className="flex items-center gap-4">
          <span>
            Page {currentPage} of {totalPages || 1}
          </span>
          <select
            className="border border-gray-400 rounded px-2 py-1"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10 Rows</option>
            <option value={15}>15 Rows</option>
            <option value={20}>20 Rows</option>
            <option value={25}>25 Rows</option>
          </select>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          className={`p-2 rounded-full ${
            currentPage === totalPages || totalPages === 0
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
