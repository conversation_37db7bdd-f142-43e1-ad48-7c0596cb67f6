import React, { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import axios from "axios";

export function WI_Sum_Pending() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [searchTerm, setSearchTerm] = useState("");
  const [data, setData] = useState([]);

  const fetchData = async () => {
    try {
      const storedData = localStorage.getItem("wiPlanTable");
      const parsedData = storedData ? JSON.parse(storedData) : null;

      if (!parsedData || !parsedData.QD_Plan_WI) {
        console.error("Invalid data: QD_Plan_WI is missing or invalid");
        return;
      }

      const requestData = parsedData.QD_Plan_WI;

      console.log("Sending data to server:", requestData);

      if (requestData.length === 0) {
        console.log("No data to send.");
        return;
      }

      const response = await axios.post(
        `${apiUrl_4000}/wi-sum/wi-sum-pending`,
        { wiPlanTable: requestData },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log("Received response:", response.data);

      if (response.data.status === "success") {
        setData(response.data.data);
      } else {
        console.error("Error: Server returned failure status.");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const columns = [
    {
      name: "Product_Grp_CD",
      selector: (row) => row.Product_Grp_CD,
      width: "180px",
    },
    {
      name: "Od_Pending",
      selector: (row) => (
        <input type="checkbox" checked={row.Od_Pending} disabled />
      ),
      width: "180px",
    },
    {
      name: "Pt_Pending",
      selector: (row) => (
        <input type="checkbox" checked={row.Pt_Pending} disabled />
      ),
      width: "180px",
    },

    { name: "Amount_Sum", selector: (row) => row.Amount_Sum, width: "180px" },
  ];

  return (
    <>
      <div className="flex items-center justify-center py-5">
        <span className="text-xl font-semibold">WI_Sum_Pending</span>
      </div>

      <div className="ml-5 text-lg flex justify-between">
        <input
          className="border-2 border-gray-500 rounded-md w-52 h-9"
          type="text"
          placeholder=" Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <div className="flex justify-left items-center mt-5 mb-3">
        <div className="w-full sm:w-auto text-center px-5 ">
          <div className="overflow-x-auto max-w-[1500px] ">
            <DataTable
              columns={columns}
              data={filteredData}
              pagination
              paginationPerPage={5}
              paginationRowsPerPageOptions={[5, 10, 15, 20]}
              customStyles={{
                rows: {
                  style: {
                    "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                    "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                    minHeight: "50px",
                    textAlign: "center",
                    justifyContent: "center",
                    borderBottom: "1px solid #ccc",
                    borderRight: "1px solid #ccc",
                  },
                },
                headCells: {
                  style: {
                    backgroundColor: "#DCDCDC",
                    fontSize: "14px",
                    textAlign: "center",
                    justifyContent: "center",
                    border: "1px solid #ccc",
                  },
                },
                cells: {
                  style: {
                    textAlign: "center",
                    justifyContent: "center",
                    border: "1px solid #ccc",
                  },
                },
                table: {
                  style: {
                    borderCollapse: "collapse",
                  },
                },
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}
