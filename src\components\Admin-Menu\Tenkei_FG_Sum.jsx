import React, { useState, useEffect } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import Swal from "sweetalert2";
import axios from "axios";
import { useNavigate } from "react-router-dom";

export function Tenkei_FG_Sum() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [allSelected, setAllSelected] = useState(false);
  const [targetYear, setTargetYear] = useState("");
  const [targetMonth, setTargetMonth] = useState("");
  const [yearError, setYearError] = useState("");
  const [monthError, setMonthError] = useState("");
  const [data, setData] = useState(null);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();

  const [checkboxes, setCheckboxes] = useState({
    Order_FG: false,
    FG_SUM: false,
    FG_SUM_Date: false,
    FG_SUM_Item1: false,
    FG_SUM_Item1G: false,
    FG_SUM_Type: false,
    FG_SUM_Week: false,
  });

  // ฟังก์ชันลบข้อมูลเก่าและดึงข้อมูลใหม่
  const fetchData = async (year, month) => {
    if (!year || !month) {
      console.error("Missing targetYear or targetMonth");
      return;
    }

    // ลบข้อมูลเก่าออกก่อนทำการดึงข้อมูลใหม่
    localStorage.removeItem("fgSumTable");

    try {
      const response = await axios.post(
        `${apiUrl_4000}/fg-sum/order-fg`,
        {
          targetYear: year,
          targetMonth: month,
        }
      );

      if (response.data.status === "success") {
        const formattedData = { QD_Order_FG: response.data.data };
        setData(formattedData.QD_Order_FG);
        localStorage.setItem("fgSumTable", JSON.stringify(formattedData));
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      alert("Error fetching data");
    }
  };

  // เซ็ตค่าเริ่มต้น และดึงข้อมูลใหม่เมื่อเข้าเพจครั้งแรก
  useEffect(() => {
    const currentYear = new Date().getFullYear();
    const currentMonth = (new Date().getMonth() + 1)
      .toString()
      .padStart(2, "0");

    setTargetYear(currentYear.toString());
    setTargetMonth(currentMonth);

    fetchData(currentYear.toString(), currentMonth); // เรียก fetchData แค่ครั้งเดียว
  }, []);

  const handleAllSelect = () => {
    const newValue = !allSelected;
    setAllSelected(newValue);
    setCheckboxes((prev) =>
      Object.keys(prev).reduce((acc, key) => ({ ...acc, [key]: newValue }), {})
    );
  };

  const handleCheckboxChange = (key) => {
    setCheckboxes((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleExecute = () => {
    const selectedKeys = Object.keys(checkboxes).filter(
      (key) => checkboxes[key]
    );

    if (selectedKeys.length === 0) {
      Swal.fire({
        title: "Please select !",
        text: "Please select the required information.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    if (checkboxes.Order_FG && (!targetYear || !targetMonth)) {
      Swal.fire({
        title: "Missing Data!",
        text: "Please enter Target Year and Target Month.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    if (checkboxes.Order_FG && (!targetYear || targetYear.length !== 4)) {
      setYearError("Year must be 4 digits");
      return;
    } else {
      setYearError("");
    }

    if (
      checkboxes.Order_FG &&
      (targetMonth.length > 2 || parseInt(targetMonth) > 12)
    ) {
      setMonthError("Month must be 2 digits and between 01 and 12");
      return;
    } else {
      setMonthError("");
    }

    let formattedMonth =
      targetMonth.length === 1 ? `0${targetMonth}` : targetMonth;

    setTargetMonth(formattedMonth);

    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const width = Math.floor(screenWidth * 0.8);
    const height = Math.floor(screenHeight * 0.8);

    const urlMap = {
      Order_FG: `/Order_FG?year=${
        targetYear || new Date().getFullYear()
      }&month=${
        formattedMonth ||
        (new Date().getMonth() + 1).toString().padStart(2, "0")
      }`,
      FG_SUM: "/FG_SUM",
      FG_SUM_Date: "/FG_SUM_Date",
      FG_SUM_Item1: "/FG_SUM_Item1",
      FG_SUM_Item1G: "/FG_SUM_Item1G",
      FG_SUM_Type: "/FG_SUM_Type",
      FG_SUM_Week: "/FG_SUM_Week",
    };

    selectedKeys.forEach((key, index) => {
      const left = Math.floor((screenWidth - width) / 2) + index * 30;
      const top = Math.floor((screenHeight - height) / 2) + index * 30;

      window.open(
        urlMap[key],
        "_blank",
        `width=${width},height=${height},left=${left},top=${top}`
      );
    });
  };

  const handleBackToAdminClick = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to admin?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า admin หรือไม่?<br>データは編集されました。admin に戻りますか？"
          : "Do you want to go back to admin?<br>คุณต้องการกลับไปที่หน้า admin หรือไม่?<br>admin に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false); // รีเซ็ตสถานะเมื่อผู้ใช้ยืนยันปิด
        navigate("/admin-menu");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "เกิดข้อผิดพลาด",
        text: "กรุณาลองอีกครั้ง",
        icon: "error",
        confirmButtonText: "ตกลง",
      });
    }
  };

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="flex justify-between items-center mt-5 px-5 text-base">
              <h1 className="text-2xl font-bold text-center flex-1 lg:pl-32">
                TENKEI FG SUM
              </h1>
              <button
                onClick={handleBackToAdminClick}
                className="bg-red-500 text-white px-4 py-2 rounded-md shadow-md ease-in-out hover:bg-red-600"
              >
                Back to Admin
              </button>
            </div>

            <hr className="my-5 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

            {/* Input Fields for Target Year and Target Month */}
            <div className="flex flex-col sm:flex-row justify-center items-center space-x-4">
              <label className="text-sm lg:text-base">Target Year</label>
              <input
                type="text"
                className="border-2 border-gray-500 rounded-md py-1 w-32 lg:w-auto"
                value={targetYear}
                onChange={(e) =>
                  setTargetYear(e.target.value.replace(/\D/, ""))
                } // กรองให้รับเฉพาะตัวเลข
              />
              {yearError && <p className="text-red-500">{yearError}</p>}

              <label className="text-sm lg:text-base">Target Month</label>
              <input
                type="text"
                className="border-2 border-gray-500 rounded-md py-1 w-32 lg:w-auto"
                value={targetMonth}
                onChange={(e) =>
                  setTargetMonth(e.target.value.replace(/\D/, ""))
                } // กรองให้รับเฉพาะตัวเลข
              />
              {monthError && <p className="text-red-500">{monthError}</p>}
            </div>

            {/* Checkbox Filter */}
            <div className="space-x-4 p-4 bg-gray-200 flex mt-5 items-center lg:justify-center justify-center">
              <div className="flex flex-wrap space-x-4">
                <div className="flex flex-col space-y-2">
                  {[
                    { key: "Order_FG", id: "tab-order-fg" },
                    { key: "FG_SUM", id: "tab-fg-sum" },
                    { key: "FG_SUM_Date", id: "tab-fg-sum-date" },
                    { key: "FG_SUM_Item1", id: "tab-fg-sum-item1" },
                    { key: "FG_SUM_Item1G", id: "tab-fg-sum-item1g" },
                  ].map(({ key, id }) => (
                    <label key={key} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={id} // เพิ่ม id เฉพาะให้ checkbox
                        className="form-checkbox"
                        checked={checkboxes[key]}
                        onChange={() => handleCheckboxChange(key, id)}
                      />
                      <span>{key}</span>
                    </label>
                  ))}
                </div>

                <div className="flex flex-col space-y-2">
                  {[
                    { key: "FG_SUM_Type", id: "tab-fg-sum-type" },
                    { key: "FG_SUM_Week", id: "tab-fg-sum-week" },
                  ].map(({ key, id }) => (
                    <label key={key} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={id} // เพิ่ม id เฉพาะให้ checkbox
                        className="form-checkbox"
                        checked={checkboxes[key]}
                        onChange={() => handleCheckboxChange(key, id)}
                      />
                      <span>{key}</span>
                    </label>
                  ))}
                </div>

                <div className="flex flex-col space-y-2 pl-5">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="form-checkbox"
                      checked={allSelected}
                      onChange={handleAllSelect}
                    />
                    <span>All_Select</span>
                  </label>
                  <button
                    onClick={handleExecute}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                  >
                    Execute
                  </button>
                  <button
                    onClick={() => {
                      setCheckboxes({
                        Order_FG: false,
                        FG_SUM: false,
                        FG_SUM_Date: false,
                        FG_SUM_Item1: false,
                        FG_SUM_Item1G: false,
                        FG_SUM_Type: false,
                        FG_SUM_Week: false,
                      });
                      setAllSelected(false);
                      const currentYear = new Date().getFullYear();
                      const currentMonth = new Date().getMonth() + 1; // getMonth() returns 0-indexed, so add 1
                      setTargetYear(currentYear.toString());
                      setTargetMonth(
                        currentMonth < 10
                          ? `0${currentMonth}`
                          : currentMonth.toString()
                      );
                      setYearError("");
                      setMonthError("");
                    }}
                    className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
