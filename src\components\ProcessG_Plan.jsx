import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import Swal from "sweetalert2";
import "./fonts/CODE39.ttf";
import "./fonts/Helvetica.ttf";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { useOrder } from "../hooks/use-order";
import { usePlan } from "../hooks/use-plan";
import { useProcessGPlan } from "../hooks/use-processgplan";
import { font } from "./fonts/font";
import { THSarabunNew } from "./fonts/THSarabunNew-normal";
export default function ProcessG_Plan() {
  const navigate = useNavigate();
  const [checkOnly, setCheckOnly] = useState(true);
  const [isChecked, setIsChecked] = useState(false);

  const [buttonState, setButtonState] = useState({
    F1: false,
    F2: false,
    F3: false,
    F4: false,
    F5: false,
    F6: false,
    F7: false,
    F8: false,
    F9: true,
    F10: false,
    F11: false,
    F12: true,
  });

  const {
    CustomerData,
    Item1Data,
    setCustomerData,
    OdProgressData,
    DeliveryData,
    TargetData,
    WorkerData,
    CoatingData,
    Request3Data,
  } = useOrder();

  const {
    processGPlanData,
    setProcessGPlanData,
    processGData,
    TTprocessGData,
    setTTProcessGData,
  } = useProcessGPlan();

  const {
    planData,
    plprogressData,
    processData,
    setStatusData,
    StatusData,
    QR_ProG_Plan,
    ProGData,
    PartsData,
    TMProcessData,
  } = usePlan();

  const handleInputChange = (event) => {
    const { id, value, type, checked } = event.target;
    let formattedValue = value;
    if (type === "datetime-local" && value) {
      const dateWithCurrentTime = new Date(value);
      const year = dateWithCurrentTime.getFullYear();
      const month = String(dateWithCurrentTime.getMonth() + 1).padStart(2, "0");
      const day = String(dateWithCurrentTime.getDate()).padStart(2, "0");
      const hours = String(dateWithCurrentTime.getHours()).padStart(2, "0");
      const minutes = String(dateWithCurrentTime.getMinutes()).padStart(2, "0");
      const seconds = String(dateWithCurrentTime.getSeconds()).padStart(2, "0");
      formattedValue = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    setStatusData((prevStatusData) => ({
      ...prevStatusData,
      [id]:
        type === "checkbox"
          ? checked
          : type === "datetime-local" && value
          ? formattedValue
          : type === "date" && value !== ""
          ? new Date(`${value}`).toISOString()
          : value === ""
          ? null
          : value,
    }));
    setProcessGPlanData((prev) => ({
      ...prev,
      [id]: type === "checkbox" ? checked : value === "" ? null : value,
    }));
  };

  const handleCheckboxChange = (index) => {
    const updatedData = [...TTprocessGData];
    updatedData[index].List = !updatedData[index].List;
    setTTProcessGData(updatedData);
  };

  const handleCheckOnlyChange = () => {
    setCheckOnly(!checkOnly);
  };

  const handleCheckboxResultChange = () => {
    setIsChecked(!isChecked);
  };

  const filteredData = checkOnly
    ? TTprocessGData?.filter((item) => Boolean(item.List)) || []
    : TTprocessGData || [];

  const getCurrentDateTime = () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, "0");
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const year = now.getFullYear();
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const getFormattedDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `RD_ProG_Plan_${year}${month}${day}_${hours}${minutes}${seconds}.pdf`;
  };

  useEffect(() => {
    if (StatusData.Result_Search === null) {
      StatusData.Result_Search = false;
    }
  }, []);

  function formatDate2(dateString) {
    if (!dateString) return ""; // กัน error กรณีไม่มีค่า
    const date = new Date(dateString);
    const month = date.getMonth() + 1; // เดือนเริ่มที่ 0 ต้องบวก 1
    const day = date.getDate();
    return `${day}/${month}`;
  }

  const uniqueProgData = Array.from(
    new Set(ProGData.map((item) => item.PPG))
  ).map((ppg) => ProGData.filter((item) => item.PPG === ppg));

const chunkedDataByPPG = uniqueProgData.map((ppgData) => {
  const itemsPerPage = 13;

  // ✅ เรียงก่อนแบ่งหน้า
  const sortedPPGData = [...ppgData].sort((a, b) => {
    if (StatusData?.Sort === "Plan_Date") {
      const ppdA = new Date(a.PPD || "1900-01-01");
      const ppdB = new Date(b.PPD || "1900-01-01");
      if (ppdA - ppdB !== 0) return ppdA - ppdB;

      const deliA = new Date(a.Product_Delivery || "1900-01-01");
      const deliB = new Date(b.Product_Delivery || "1900-01-01");
      if (deliA - deliB !== 0) return deliA - deliB;

      return (a.OdPt_No || "").localeCompare(b.OdPt_No || "");
    } else if (StatusData?.Sort === "Product_Delivery") {
      const deliA = new Date(a.Product_Delivery || "1900-01-01");
      const deliB = new Date(b.Product_Delivery || "1900-01-01");
      if (deliA - deliB !== 0) return deliA - deliB;

      const ppdA = new Date(a.PPD || "1900-01-01");
      const ppdB = new Date(b.PPD || "1900-01-01");
      if (ppdA - ppdB !== 0) return ppdA - ppdB;

      return (a.OdPt_No || "").localeCompare(b.OdPt_No || "");
    } else if (StatusData?.Sort === "Result_Date") {
      const rpdA = new Date(a.RPD || "1900-01-01");
      const rpdB = new Date(b.RPD || "1900-01-01");
      if (rpdA - rpdB !== 0) return rpdA - rpdB;

      return (a.OdPt_No || "").localeCompare(b.OdPt_No || "");
    }
    return 0;
  });

  // ✅ ตัดหน้าเฉพาะจากที่เรียงแล้ว
  const chunkedRows = [];
  for (let i = 0; i < sortedPPGData.length; i += itemsPerPage) {
    chunkedRows.push(sortedPPGData.slice(i, i + itemsPerPage));
  }

  return chunkedRows;
});

  const pagesWithHeaders = uniqueProgData.flatMap((ppgData) => {
    const itemsPerPage = 13;
    const header = ppgData[0]?.PPG; // หัวกลุ่ม PPG จากรายการแรกใน ppgData
    const pages = [];
    const PPGAbbForRow = (TTprocessGData || [])
      .filter((PPGAbb) => PPGAbb.ProcessG_CD === header)
      .map((PPGAbb) => PPGAbb.ProcessG_Abb);

    for (let i = 0; i < ppgData.length; i += itemsPerPage) {
      pages.push({
        header: header, // เก็บ header เดียวกันสำหรับหน้าทั้งหมดของกลุ่มนี้
        pggabb: PPGAbbForRow,
        data: ppgData.slice(i, i + itemsPerPage),
      });
    }
    return pages;
  });
  let previousDate = "";
  const rows = chunkedDataByPPG.flatMap((ppgChunks) =>
    ppgChunks.map((chunk) =>
      chunk.map((item) => {
        const instructions =
          StatusData?.Info_View === true ? item.Pt_Instructions : "";
        const Information =
          StatusData?.Info_View === true ? item.Pt_Information : "";
        const formatString = (value) =>
          Array.isArray(value) ? value.join(", ") : String(value ?? "");
        const ISN = formatString(instructions) + formatString(Information);

        const DeliveryMarkForRow = (DeliveryData || [])
          .filter((Delivery) => Delivery.Delivery_CD === item.Delivery_CD)
          .map((Delivery) => Delivery.Delivery_Mark);
        const TargetSymbolForRow = (TargetData || [])
          .filter((Target) => Target.Target_CD === item.Target_CD)
          .map((Target) => Target.Target_Symbol);
        const WorkerAbbForRow = (WorkerData || [])
          .filter((Worker) => Worker.Worker_CD === item.Sales_Person_CD)
          .map((Worker) => Worker.Worker_Abb);
        const CoatingForRow = (CoatingData || [])
          .filter((Coating) => Coating.Coating_CD === item.Coating_CD)
          .map((Coating) => Coating.Coating_Symbol);
        const Request3ForRow = (Request3Data || [])
          .filter((Request3) => Request3.Request3_CD === item.Request3_CD)
          .map((Request3) => Request3.Request3_Symbol);
        const PartsForRow = (PartsData || [])
          .filter((Parts) => Parts.Parts_CD === item.Parts_CD)
          .map((Parts) => Parts.Parts_Abb);
        const ProcessForRow = (TMProcessData || [])
          .filter((Process) => Process.Process_CD === item.PPC)
          .map((Process) => Process.Process_Abb);
        const inputs = Array.from({ length: 36 }, (_, i) => i + 1);
        const ProcessForRowArray = inputs.map((id) => {
          return (TMProcessData || [])
            .filter((Process) => Process.Process_CD === item["PPC" + id])
            .map((Process) => Process.Process_Abb);
        });
        let formattedDate = formatDate2(item.PPD);
        if (formattedDate === previousDate) {
          formattedDate = "";
        } else {
          previousDate = formattedDate;
        }
        const processData = ProcessForRowArray.flatMap((processList, index) =>
          processList.map((processAbb) => ({
            process1: processAbb,
            RPD: formatDate2(item["RPD" + inputs[index]] || ""),
            PPD: formatDate2(item["PPD" + inputs[index]] || ""),
            process3: item["RPD" + inputs[index]],
          }))
        );
        return {
          Line_No: item.Line_No,
          process: item.Now_Process,
          pds: item.WorkG_Mark,
          planDate: formattedDate,
          planDate2: formatDate2(item.PPD),
          resultDate: formatDate2(item.RPD),
          pdsDeli: formatDate2(item.Product_Delivery),
          mark: DeliveryMarkForRow,
          orderno: item.Order_No,
          partno: item.Parts_No,
          orderPartsNo: item.OdPt_No,
          target: TargetSymbolForRow,
          coating: item.Money_Object === true ? item.Coating : "",
          no: "",
          customerProductionName1: item.Customer_Abb,
          customerProductionName2: item.Product_Name,
          cat1: WorkerAbbForRow,
          cat2: CoatingForRow,
          cat3: Request3ForRow,
          ptNameMaterial: PartsForRow,
          ptNameMaterial2: item.Pt_Material,
          planQty:
            item.Pt_Split === true
              ? `${item.Pt_Qty}/${item.Quantity}`
              : item.Pt_Qty,
          spare:
            item.Pt_Spare_Qty - item.Pt_NG_Qty === 0
              ? ""
              : item.Pt_Spare_Qty - item.Pt_NG_Qty > 0
              ? `+${item.Pt_Spare_Qty - item.Pt_NG_Qty}`
              : `${item.Pt_Spare_Qty - item.Pt_NG_Qty}`,
          thisPlan: `No${item.Pro_No}`,
          thisPlan2: item.Pro_No,
          ship: ProcessForRow,
          mSetPSet: { main: item.PML, sub: item.PPL },
          processData,
          ptNoteInfo: [ISN],
        };
      })
    )
  );

  const handleViewPDF = () => {
    const doc = new jsPDF("landscape", "mm", "a4");

    // ประมวลผลวันที่เริ่มต้นและสิ้นสุด
    function toThaiDateTimeString(date) {
      const thaiDate = new Date(date.getTime());
      const dateStr = thaiDate.toISOString().split("T")[0];
      const timeStr = thaiDate.toTimeString().split(" ")[0];
      return `${dateStr} ${timeStr}`;
    }

    const now = new Date(); // เวลาปัจจุบัน (UTC หรือเครื่อง)

    const startDate = StatusData?.Tg_St_Pl_Date
      ? `${new Date(StatusData.Tg_St_Pl_Date).toISOString().split("T")[0]} ${
          toThaiDateTimeString(now).split(" ")[1]
        }`
      : "";

    const endDate = StatusData?.Tg_Ed_Pl_Date
      ? `${new Date(StatusData.Tg_Ed_Pl_Date).toISOString().split("T")[0]} ${
          toThaiDateTimeString(now).split(" ")[1]
        }`
      : "";

    // วันที่สร้าง
    const CreateDate = toThaiDateTimeString(now); // เวลาปัจจุบันเป็นเวลาไทย
    const dateRange = `${startDate} ~ ${endDate}`;

    // ใส่ฟอนต์ CODE39
    doc.addFileToVFS("CODE39.ttf");
    doc.addFont("CODE39.ttf", "CODE39", "normal");
    rows.map((pageRows, pageIndex) => {
      if (pageIndex > 0) {
        doc.addPage();
      }

      // PPG Data
      const PPGHead = pagesWithHeaders[pageIndex].header || "N/A";
      const PPGAbbs = pagesWithHeaders[pageIndex].pggabb || "N/A";
      const safePPGAbbs = String(PPGAbbs);
      // กำหนดฟอนต์และสี
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);

      // หัวข้อ "Target_Plan_Process_Date"
      doc.text("Target_Plan_Process_Date:", 10, 10);
      doc.setFont("CODE39", "normal");
      doc.setFontSize(8);
      doc.setTextColor(0, 0, 0);
      doc.text(dateRange, 53, 10);

      // หัวข้อ "Create_Date"
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);
      doc.text("Create_Date:", 200, 10);
      doc.setFont("CODE39", "normal");
      doc.setFontSize(8);
      doc.text(CreateDate, 222, 10);

      // เลขหน้า
      doc.setFont("CODE39", "bold");
      doc.setFontSize(10);
      doc.setTextColor(30, 64, 175);
      doc.text(`Page: ${pageIndex + 1} / ${rows.length}`, 270, 10);

      // หัวข้อ Process_Grp
      doc.setFont("CODE39", "bold");
      doc.setFontSize(12);
      doc.setTextColor(30, 64, 175);
      doc.text("Process_Grp:", 10, 30);

      // กำหนดค่ากรอบ
      const rects = [
        { x: 37, y: 25, width: 30, height: 10, text: PPGHead },
        { x: 70, y: 25, width: 40, height: 10, text: safePPGAbbs },
      ];

      rects.forEach(({ x, y, width, height, text }) => {
        // วาดกรอบ
        doc.setDrawColor(0);
        doc.setLineWidth(0);
        doc.rect(x, y, width, height);

        // กำหนดฟอนต์และขนาด
        doc.setFont("CODE39", "bold");
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);

        // คำนวณตำแหน่ง X (ให้อยู่กลางแนวนอน)
        const textWidth = doc.getTextWidth(text);
        const textX = x + (width - textWidth) / 2;

        // คำนวณตำแหน่ง Y (ให้อยู่กลางแนวตั้ง)
        const textHeight = doc.getTextDimensions(text).h; // ค่าความสูงของข้อความ
        const textY = y + (height + textHeight) / 2 - 1;

        // วาดข้อความ
        doc.text(text, textX, textY);
      });
      doc.setFont("CODE39", "bold");
      doc.setFontSize(12);
      doc.setTextColor(30, 64, 175);
      doc.text("Process_Grp_Plan_List:", 135, 20);

      const ListG = [
        {
          x: 120,
          y: 26,
          width: 20,
          height: 8,
          text: "Self",
          color: [239, 68, 68],
        },
        {
          x: 150,
          y: 26,
          width: 25,
          height: 8,
          text: "1Before",
          color: [249, 115, 22],
        },
        {
          x: 185,
          y: 26,
          width: 25,
          height: 8,
          text: "2Before",
          color: [255, 204, 0],
        },
      ];

      ListG.forEach(({ x, y, width, height, text, color }) => {
        // กำหนดสีพื้นหลัง
        doc.setFillColor(...color);
        doc.rect(x, y, width, height, "F"); // "F" = Filled rectangle

        // กำหนดฟอนต์และขนาด
        doc.setFont("helvetica", "bold");
        doc.setFontSize(10);
        doc.setTextColor(255, 255, 255); // สีตัวอักษรเป็นขาว

        // คำนวณตำแหน่ง X (ให้อยู่กลางแนวนอน)
        const textWidth = doc.getTextWidth(text);
        const textX = x + (width - textWidth) / 2;

        // คำนวณตำแหน่ง Y (ให้อยู่กลางแนวตั้ง)
        const textHeight = doc.getTextDimensions(text).h;
        const textY = y + (height + textHeight) / 2 - 1;

        // วาดข้อความ
        doc.text(text, textX, textY);
      });

      const head = [
        [
          {
            content: "Plan_Date",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PDS_Deli",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Order_Parts_No",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          // สมมุติว่า cell นี้มี index = 3
          {
            content: "Customer/Production_Name",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PT_Name Material",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Plan Qty",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "This Plan",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "M_Set P_Set",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "Process",
            colSpan: 24,
            styles: { halign: "center", valign: "middle" },
          },
          {
            content: "PT_Note/Info",
            rowSpan: 2,
            styles: { halign: "center", valign: "middle" },
          },
        ],
        [
          ...Array.from({ length: 24 }, (_, i) => ({
            content: `${i + 1}`,
            styles: { halign: "center", valign: "middle" },
          })),
        ],
      ];

      const body = pageRows.map((row, rowIndex) => {
        // ขยาย processData ให้มีจำนวน 24 ค่า โดยถ้า row.processData มีค่าน้อยกว่านั้นจะเติมด้วย object ว่าง
        const processData = [
          ...row.processData,
          ...Array.from({ length: 24 - row.processData.length }, () => ({
            process1: "",
            process2: "",
          })),
        ].slice(0, 24);
        const targetValue = encodeURIComponent(String(row.target || ""));
        // กำหนดสีพื้นหลังสลับกัน: ถ้าเป็นแถวคู่ให้ใช้สี "#cffff9" ถ้าไม่ใช่ใช้สี "#ffffff"
        const rowColor = rowIndex % 2 === 0 ? "#cffff9" : "#ffffff";

        let ColorProcess = rowIndex % 2 === 0 ? "#cffff9" : "#ffffff";
        if (StatusData?.Info_View === true) {
          if (StatusData?.Color_Separate === true) {
            if (row.process === "2Before") {
              ColorProcess = "#FFCC00";
            } else if (row.process === "1Before") {
              ColorProcess = "#F97316";
            } else if (row.process === "0Self") {
              ColorProcess = "#EF4444";
            } else {
              ColorProcess = "#FFFFFF";
            }
          }
        }

        return [
          {
            content: ``,
            styles: {
              fillColor: rowColor,
            },
          },
          // แถวที่ 2: pdsDeli, mark
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "center", valign: "middle" },
          },

          // แถวที่ 3: orderPartsNo, target, coating
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "center", valign: "middle" },
          },

          {
            content: `${row.customerProductionName1 || ""}\n${
              row.customerProductionName2 || ""
            } `,
            styles: {
              fillColor: rowColor,
              halign: "left",
              valign: "middle",
              maxWidth: 32,
              fontSize: 5,
            },
          },

          {
            content: `${row.ptNameMaterial || ""}\n${
              row.ptNameMaterial2 || ""
            } `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },

          {
            content: `${row.planQty || ""}\n${row.spare || ""} `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 6,
            },
          },

          {
            content: `${row.thisPlan || ""}\n${row.ship || ""} `,
            styles: {
              fillColor: ColorProcess,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },
          {
            content: `${row.mSetPSet.main}\n ${row.mSetPSet.sub || 0} `,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
              fontSize: 5,
            },
          },
          ...processData.map((process, procIndex) => ({
            content: ``,
            styles: {
              fillColor: rowColor,
              halign: "center",
              valign: "middle",
            },
          })),
          {
            content: ``,
            styles: { fillColor: rowColor, halign: "left", valign: "middle" },
          },
        ];
      });
 
      autoTable(doc, {
        head,
        body,
        startX: 0,
        startY: 40,
        tableWidth: "100%",
        margin: { left: 5, right: 5 },
        styles: {
          fontSize: 4,
          textColor: [0, 0, 0],
          lineColor: [30, 64, 175],
          lineWidth: 0.2,
          cellLineStyle: "dashed",
          cellLineWidth: 0.2,
        },
        headStyles: {
          textColor: [30, 64, 175],
          fillColor: [255, 255, 255],
        },
        tableLineWidth: 0.3,
        tableLineColor: [30, 64, 175],
        columnStyles: {
          0: { cellWidth: 12 },
          1: { cellWidth: 12 },
          2: { cellWidth: 17 },
          3: { cellWidth: 32 },
          4: { cellWidth: 15 },
          5: { cellWidth: 10 },
          6: { cellWidth: 10 },
          7: { cellWidth: 10 },
          ...Array.from({ length: 24 }, (_, i) => ({
            [8 + i]: { cellWidth: 6.5 },
          })).reduce((acc, cur) => ({ ...acc, ...cur }), {}),
        },
        willDrawCell: (data) => {
          // ถ้าเป็นคอลัมน์ index ตั้งแต่ 8 ขึ้นไป ให้ปิดเส้นขอบ
          if (data.column.index >= 8) {
            data.cell.styles.lineWidth = 0;
          }
        },
        didDrawCell: function (data) {
          if (data.section === "head" && data.column.index === 3) {
            const cell = data.cell;

            // กำหนดตำแหน่งของ "CAT"
            const xPos = cell.x + cell.width - 1; // ปรับ margin ด้านขวา
            const yPos = cell.y + 2; // ปรับ margin ด้านบน

            // กำหนด font size
            doc.setFontSize(cell.styles.fontSize);

            // รีเซ็ตค่าเส้นให้กลับเป็นปกติ
            doc.setLineDashPattern([], 0);

            // วาดข้อความ CAT
            doc.text("CAT", xPos, yPos, { align: "right", baseline: "top" });
          }

          if (data.section === "body" && data.column.index === 3) {
            const cell = data.cell;
            const xPos = cell.x + cell.width - 0.5; // ชิดขวาสุด
            const yPos = cell.y + 2; // ชิดด้านบน

            const rowIndex = data.row.index;

            const cat1Text = pageRows[rowIndex]?.cat1 || "";
            const cat2Text = pageRows[rowIndex]?.cat2 || "";
            const cat3Text = pageRows[rowIndex]?.cat3 || "";

            doc.text(cat1Text, xPos, yPos, {
              align: "right",
              baseline: "top",
            });
            doc.text(
              [`${cat2Text}`, `${cat3Text}`],
              cell.x + cell.width - 2,
              cell.y + 5,
              { align: "right", baseline: "middle" }
            );
          }

          if (data.section === "body" && data.column.index === 0) {
            const cell = data.cell;

            const rowIndex = data.row.index;
            const planDate = pageRows[rowIndex]?.planDate || "";
            const pds = pageRows[rowIndex]?.pds || "";

            if (planDate.trim() !== "") {
              doc.setFillColor("#FFCC00");
              doc.rect(cell.x, cell.y, cell.width, cell.height / 2, "F");
            }
            doc.setTextColor(0, 0, 0);

            if (planDate) {
              doc.setFontSize(8);
              doc.text(planDate, cell.x + 6, cell.y + 1, {
                align: "center",
                baseline: "top",
              });
            }

            if (pds) {
              doc.setFontSize(6);
              doc.text(pds, cell.x + 6, cell.y + 6.8, {
                align: "center",
                baseline: "bottom",
              });
            }
          }
          if (data.section === "body" && data.column.index === 1) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const pdsDeli = pageRows[rowIndex]?.pdsDeli || "";
            const mark = pageRows[rowIndex]?.mark || "";

            if (pdsDeli) {
              doc.setFontSize(8);
              doc.text(pdsDeli, cell.x + 6, cell.y + 2, {
                align: "center",
                baseline: "top",
              });
            }

            if (mark) {
              doc.setFontSize(6);
              doc.text(mark, cell.x + 6, cell.y + 8, {
                align: "center",
                baseline: "bottom",
              });
            }
          }

          if (data.section === "body" && data.column.index === 32) {
            const cell = data.cell;
            const rowIndex = data.row.index;

            let ptNoteInfo = String(pageRows[rowIndex]?.ptNoteInfo ?? "");
            ptNoteInfo = ptNoteInfo.replace(/[\r\n]+/g, "");
            ptNoteInfo = ptNoteInfo.replace(/\s+/g, " ");

            if (ptNoteInfo.trim() !== "") {
              let offsetX = 11; // ค่า default
              doc.addFileToVFS("Sarabun-normal.ttf", THSarabunNew);
              doc.addFont("Sarabun-normal.ttf", "Sarabun-normal", "normal");
              doc.setFont("Sarabun-normal");
              doc.setFontSize(5);
              doc.text(ptNoteInfo, cell.x + offsetX, cell.y + 8.0, {
                align: "right",
                baseline: "bottom",
              });
            }
          }

          if (data.section === "body" && data.column.index === 2) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const orderno = pageRows[rowIndex]?.orderno || "";
            const partno = pageRows[rowIndex]?.partno || "";
            const target = pageRows[rowIndex]?.target || "";
            const coating = pageRows[rowIndex]?.coating || "";

            if (orderno) {
              const combinedText = `${orderno}-${partno}`;
              doc.setFontSize(6);
              doc.text(combinedText, cell.x + 8.5, cell.y + 2, {
                align: "center",
                baseline: "top",
              });
            }
            doc.addFileToVFS("segoeuithis.ttf", font);
            doc.addFont("segoeuithis.ttf", "segoeuithis", "normal");
            doc.setFont("segoeuithis");
            doc.setFontSize(6);
            doc.text(
              `${String(target)} ${String(coating)}`,
              cell.x + 1,
              cell.y + cell.height - 2,
              { align: "left", baseline: "bottom" }
            );
          }

          if (
            data.section === "body" &&
            data.column.index >= 8 &&
            data.column.index <= 31
          ) {
            const cell = data.cell;
            const rowIndex = data.row.index;
            const thisPlan2 = pageRows[rowIndex]?.thisPlan2 || "";

            const processData = Array.isArray(pageRows[rowIndex]?.processData)
              ? pageRows[rowIndex]?.processData
              : [];
            const extendedProcessData = [
              ...processData,
              ...Array.from({ length: 24 - processData.length }, () => ({
                process1: "",
                process2: "",
              })),
            ].slice(0, 24);

            const extendedProcessData2 = [
              ...processData,
              ...Array.from({ length: 35 - processData.length }, () => ({
                process1: "",
                process2: "",
              })),
            ].slice(25, 35);

            const processIndex = data.column.index - 8;
            if (extendedProcessData.length > processIndex) {
              const PP = extendedProcessData[processIndex]?.process1 || "";
              const PG =
                extendedProcessData[processIndex]?.RPD ||
                extendedProcessData[processIndex]?.PPD;
              const RPD = extendedProcessData[processIndex]?.process3 || "";

              if (StatusData?.Info_View === true) {
                if (StatusData?.Color_Separate === true) {
                  if (PP && PP.trim() !== "") {
                    if (processIndex === parseInt(thisPlan2) - 1) {
                      doc.setFillColor("#FFCC00"); // สีเหลืองทอง
                      doc.rect(
                        cell.x,
                        cell.y + 0.2,
                        cell.width,
                        cell.height / 3,
                        "F"
                      );
                    } else {
                      doc.setFillColor("#CCFFFF"); // สีฟ้าอ่อน
                      doc.rect(
                        cell.x,
                        cell.y + 0.2,
                        cell.width,
                        cell.height / 3,
                        "F"
                      );
                    }
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 0.5, {
                      align: "center",
                      baseline: "top",
                    });

                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 2.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                } else {
                  if (PP && PP.trim() !== "") {
                    doc.setFillColor("#CCFFFF");
                    doc.rect(
                      cell.x,
                      cell.y + 0.2,
                      cell.width,
                      cell.height / 3,
                      "F"
                    );
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 0.5, {
                      align: "center",
                      baseline: "top",
                    });
                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 2.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                }
              } else {
                if (StatusData?.Color_Separate === true) {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#FFFF99");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 2.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                      align: "center",
                      baseline: "middle",
                    });
                  }
                } else {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 2.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                          align: "center",
                          baseline: "middle",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 2.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 3.4, {
                      align: "center",
                      baseline: "middle",
                    });
                  }
                }
              }
            }

            // line 25-36

            if (extendedProcessData2.length > processIndex) {
              const PP = extendedProcessData2[processIndex]?.process1 || "";
              const PG =
                extendedProcessData2[processIndex]?.RPD ||
                extendedProcessData2[processIndex]?.PPD;
              const RPD = extendedProcessData2[processIndex]?.process3 || "";

              if (StatusData?.Info_View === true) {
                if (StatusData?.Color_Separate === true) {
                  if (PP && PP.trim() !== "") {
                    if (processIndex === parseInt(thisPlan2) - 1) {
                      doc.setFillColor("#FFCC00"); // สีเหลืองทอง
                      doc.rect(
                        cell.x,
                        cell.y + 4.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                    } else {
                      doc.setFillColor("#CCFFFF"); // สีฟ้าอ่อน
                      doc.rect(
                        cell.x,
                        cell.y + 4.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                    }
                    doc.text(PP, cell.x + 3.2, cell.y + 6.5, {
                      align: "center",
                      baseline: "bottom",
                    });

                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 6.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                        align: "center",
                        baseline: "middle",
                      });
                    }
                  }
                } else {
                  if (PP && PP.trim() !== "") {
                    doc.setFillColor("#CCFFFF");
                    doc.rect(
                      cell.x,
                      cell.y + 4.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.setFontSize(5);
                    doc.text(PP, cell.x + 3.2, cell.y + 4.5, {
                      align: "center",
                      baseline: "top",
                    });
                    if (StatusData?.RPD_View === true) {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    } else {
                      if (PG <= formatDate2(StatusData?.Mark_Days)) {
                        doc.setFillColor("#FFCC00");
                      } else {
                        doc.setFillColor("#CCFFFF");
                      }
                      doc.rect(
                        cell.x,
                        cell.y + 6.5,
                        cell.width,
                        cell.height / 4,
                        "F"
                      );
                      doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                        align: "center",
                        baseline: "bottom",
                      });
                    }
                  }
                }
              } else {
                if (StatusData?.Color_Separate === true) {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#FFFF99");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 6.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                      align: "center",
                      baseline: "bottom",
                    });
                  }
                } else {
                  if (StatusData?.RPD_View === true) {
                    if (PP && PP.trim() !== "") {
                      if (RPD && RPD.trim() !== "") {
                        doc.setTextColor(255, 255, 255);
                        doc.setFillColor("#000000");
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });

                        doc.setTextColor(0, 0, 0);
                      } else {
                        if (PG <= formatDate2(StatusData?.Mark_Days)) {
                          doc.setFillColor("#FFCC00");
                        } else {
                          doc.setFillColor("#CCFFFF");
                        }
                        doc.rect(
                          cell.x,
                          cell.y + 6.5,
                          cell.width,
                          cell.height / 4,
                          "F"
                        );
                        doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                          align: "center",
                          baseline: "bottom",
                        });
                      }
                    }
                  } else {
                    if (PG <= formatDate2(StatusData?.Mark_Days)) {
                      doc.setFillColor("#FFCC00");
                    } else {
                      doc.setFillColor("#CCFFFF");
                    }
                    doc.rect(
                      cell.x,
                      cell.y + 6.5,
                      cell.width,
                      cell.height / 4,
                      "F"
                    );
                    doc.text(PG, cell.x + 3.4, cell.y + 8.5, {
                      align: "center",
                      baseline: "bottom",
                    });
                  }
                }
              }
            }
          }
        },
      });
    });

    const pdfBlob = doc.output("blob");
    const pdfUrl = URL.createObjectURL(pdfBlob);

    const newTab = window.open(pdfUrl, "_blank");

    if (newTab) {
      setTimeout(() => {
        newTab.document.title = getFormattedDateTime();
      }, 500);
    } else {
      Swal.fire({
        title: "ไม่สามารถเปิดแท็บใหม่",
        text: "กรุณาอนุญาตให้เบราว์เซอร์เปิดแท็บใหม่",
        icon: "warning",
        confirmButtonText: "ตกลง",
      });
    }
  };

  const handleF9Click = async () => {
    const Tg_St_Pl_Date = StatusData?.Tg_St_Pl_Date;
    const Tg_Ed_Pl_Date = StatusData?.Tg_Ed_Pl_Date;
    const data = await QR_ProG_Plan(StatusData);
    const startDate = new Date(Tg_St_Pl_Date);
    const endDate = new Date(Tg_Ed_Pl_Date);

    if (startDate > endDate) {
      Swal.fire({
        icon: "warning",
        title: "Invalid date",
        text: "The start date must not be greater than the end date.",
        confirmButtonText: "OK",
      });
      return;
    }

    const listViewChecked = StatusData?.List;
    const GraphViewChecked = StatusData?.Graph;

    if (listViewChecked && GraphViewChecked) {
      // เปิดแทบใหม่สำหรับ List
      if (data && data.length > 0) {
        handleViewPDF();
      } else {
        Swal.fire({
          icon: "warning",
          title: "No data!",
          text: "There is no data available.",
          confirmButtonText: "OK",
        });
      }

      // เปิดแทบใหม่สำหรับ Graph
      const updatedStatusDataForGraph = {
        TG_ProcessG: StatusData?.TG_ProcessG,
        Tg_St_Pl_Progress: StatusData?.Tg_St_Pl_Progress,
        Print_Object: StatusData?.Print_Object,
        Tg_Ed_Pl_Date: StatusData?.Tg_Ed_Pl_Date,
        S_Customer_CD1: StatusData?.S_Customer_CD1,
        S_Customer_CD2: StatusData?.S_Customer_CD2,
        S_Customer_CD3: StatusData?.S_Customer_CD3,
        S_No_Customer_CD: StatusData?.S_No_Customer_CD,
        S_Customer_Name1: StatusData?.S_Customer_Name1,
        S_Customer_Name2: StatusData?.S_Customer_Name2,
        S_Customer_Name3: StatusData?.S_Customer_Name3,
        S_Item1_CD: StatusData?.S_Item1_CD,
        S_St_Od_Progress_CD: StatusData?.S_St_Od_Progress_CD,
        S_Ed_Od_Progress_CD: StatusData?.S_Ed_Od_Progress_CD,
        S_St_Pl_Progress_CD: StatusData?.S_St_Pl_Progress_CD,
        S_Ed_Pl_Progress_CD: StatusData?.S_Ed_Pl_Progress_CD,
        S_Od_Pending: StatusData?.S_Od_Pending,
        S_Parts_Pending: StatusData?.S_Parts_Pending,
      };

      const queryStringForGraph = encodeURIComponent(
        JSON.stringify(updatedStatusDataForGraph)
      );
      window.open(
        `/reports/RD_ProG_Graph?data=${queryStringForGraph}`,
        "_blank"
      );
    } else if (listViewChecked) {
      if (data && data.length > 0) {
        handleViewPDF();
      } else {
        Swal.fire({
          icon: "warning",
          title: "No data!",
          text: "There is no data available.",
          confirmButtonText: "OK",
        });
      }
    } else if (GraphViewChecked) {
      // เปิดแทบใหม่แค่หน้า Graph
      const updatedStatusDataForGraph = {
        TG_ProcessG: StatusData?.TG_ProcessG,
        Tg_St_Pl_Progress: StatusData?.Tg_St_Pl_Progress,
        Print_Object: StatusData?.Print_Object,
        Tg_Ed_Pl_Date: StatusData?.Tg_Ed_Pl_Date,
        S_Customer_CD1: StatusData?.S_Customer_CD1,
        S_Customer_CD2: StatusData?.S_Customer_CD2,
        S_Customer_CD3: StatusData?.S_Customer_CD3,
        S_No_Customer_CD: StatusData?.S_No_Customer_CD,
        S_Customer_Name1: StatusData?.S_Customer_Name1,
        S_Customer_Name2: StatusData?.S_Customer_Name2,
        S_Customer_Name3: StatusData?.S_Customer_Name3,
        S_Item1_CD: StatusData?.S_Item1_CD,
        S_St_Od_Progress_CD: StatusData?.S_St_Od_Progress_CD,
        S_Ed_Od_Progress_CD: StatusData?.S_Ed_Od_Progress_CD,
        S_St_Pl_Progress_CD: StatusData?.S_St_Pl_Progress_CD,
        S_Ed_Pl_Progress_CD: StatusData?.S_Ed_Pl_Progress_CD,
        S_Od_Pending: StatusData?.S_Od_Pending,
        S_Parts_Pending: StatusData?.S_Parts_Pending,
      };
      const queryStringForGraph = encodeURIComponent(
        JSON.stringify(updatedStatusDataForGraph)
      );
      window.open(
        `/reports/RD_ProG_Graph?data=${queryStringForGraph}`,
        "_blank"
      );
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: "Do you want to close this window?<br>คุณต้องการปิดหน้าต่างนี้หรือไม่?<br>このウィンドウを閉じますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setProcessGPlanData("");
        navigate("/dashboard");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const customerAbbKey1 = processGPlanData?.S_Customer_CD1;
  const customerAbbForRow1 = (CustomerData || [])
    .filter((customer) => customer.Customer_CD === customerAbbKey1)
    .map((customer) => customer.Customer_Abb);

  const customerAbbKey2 = processGPlanData?.S_Customer_CD2;
  const customerAbbForRow2 = (CustomerData || [])
    .filter((customer) => customer.Customer_CD === customerAbbKey2)
    .map((customer) => customer.Customer_Abb);

  const customerAbbKey3 = processGPlanData?.S_Customer_CD3;
  const customerAbbForRow3 = (CustomerData || [])
    .filter((customer) => customer.Customer_CD === customerAbbKey3)
    .map((customer) => customer.Customer_Abb);

  const nocustomerAbbKey = processGPlanData?.S_No_Customer_CD;
  const nocustomerAbbForRow = (CustomerData || [])
    .filter((customer) => customer.Customer_CD === nocustomerAbbKey)
    .map((customer) => customer.Customer_Abb);

  const item1AbbKey = processGPlanData?.S_Item1_CD;
  const item1AbbForRow = (Item1Data || [])
    .filter((item1) => item1.Item1_CD === item1AbbKey)
    .map((item1) => item1.Item1_Abb);

  useEffect(() => {
    if (StatusData.Settles) {
      const today = new Date().toISOString().split("T")[0];
      setStatusData((prev) => ({
        ...prev,
        Settles_Day: today,
      }));
    } else {
      setStatusData((prev) => ({
        ...prev,
        Settles_Day: null,
      }));
    }
  }, [StatusData.Settles]);

  useEffect(() => {
    if (StatusData.After_Days) {
      const daysToAdd = parseInt(StatusData.After_Days, 10);
      if (!isNaN(daysToAdd)) {
        const newDate = new Date();
        newDate.setDate(newDate.getDate() + daysToAdd);
        const newTgEdPl_Date = newDate.toISOString().split("T")[0];

        if (newTgEdPl_Date !== StatusData.Tg_Ed_Pl_Date) {
          setStatusData((prev) => ({
            ...prev,
            Tg_Ed_Pl_Date: newTgEdPl_Date,
          }));
        }
      }
    }
    if (StatusData.Before_Days) {
      const daysToSubtract = parseInt(StatusData.Before_Days, 10);
      if (!isNaN(daysToSubtract)) {
        const newDate = new Date();
        newDate.setDate(newDate.getDate() - daysToSubtract);
        const newTgStPl_Date = newDate.toISOString().split("T")[0];
        if (newTgStPl_Date !== StatusData.Tg_St_Pl_Date) {
          setStatusData((prev) => ({
            ...prev,
            Tg_St_Pl_Date: newTgStPl_Date,
          }));
        }
      }
    }
  }, [StatusData.Before_Days, StatusData.After_Days]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (StatusData.Tg_St_Pl_Date) {
        const currentDate = new Date();
        const tgStPlDate = new Date(StatusData.Tg_St_Pl_Date);
        const diffTime = currentDate - tgStPlDate;
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays !== StatusData.Before_Days) {
          setStatusData((prev) => ({
            ...prev,
            Before_Days: diffDays,
          }));
        }
      }

      if (StatusData.Tg_Ed_Pl_Date) {
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        const tgEdPlDate = new Date(StatusData.Tg_Ed_Pl_Date);
        const diffTime = tgEdPlDate - currentDate;
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays !== StatusData.After_Days) {
          setStatusData((prev) => ({
            ...prev,
            After_Days: diffDays,
          }));
        }
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [StatusData.Tg_St_Pl_Date, StatusData.Tg_Ed_Pl_Date]);

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-screen mr-2 ml-2">
        <Navbar />

        <div className="flex-1 flex-col overflow-x-auto flex-grow p-2 bg-white">
          <div className="flex items-center justify-between w-full px-4 pb-3">
            <h1 className="text-2xl font-bold text-center flex-grow">
              ProcessG_Plan
            </h1>
            <div className="flex items-center space-x-2 py-2 pt-2">
              <label className="text-xs font-medium">Date : </label>
              <input
                type="text"
                className="border-2 border-gray-500 rounded-md px-2 py-1 text-sm w-32 text-center"
                value={new Date().toLocaleDateString("th-TH", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })}
                readOnly
              />
            </div>
          </div>
          <hr />

          <div className="mx-5 p-4 grid grid-cols-1 lg:grid-cols-2 gap-2">
            <div className="w-full ">
              <div>
                <label className="font-medium text-xs">
                  Target_ProG_Setting
                </label>
              </div>
              <div className="flex gap-2 items-center mb-3">
                <label className="font-medium text-xs">
                  Please Select ProcessG
                </label>
                <div className="flex gap-2 items-center">
                  <input
                    id="check-only"
                    type="checkbox"
                    checked={checkOnly}
                    onChange={handleCheckOnlyChange}
                    className="h-4 w-4"
                  />
                  <label className="font-medium text-xs">Check_Only</label>
                </div>
              </div>

              <div
                className="sm:overflow-x-auto"
                style={{ maxHeight: "600px", overflowY: "auto" }}
              >
                <table className="border-2 border-black w-full">
                  <thead className="sticky top-0">
                    <tr>
                      <th className="py-3 bg-white border-2 border-black">
                        List
                      </th>
                      <th className="py-3 bg-white border-2 border-black">
                        ProcessG_CD
                      </th>
                      <th className="py-3 bg-white border-2 border-black">
                        ProcessG_Mark
                      </th>
                      <th className="py-3 bg-white border-2 border-black">
                        ProcessG_Name
                      </th>
                    </tr>
                  </thead>
                  <tbody className="text-center">
                    {filteredData && filteredData.length > 0 ? (
                      filteredData.map((item, index) => (
                        <tr key={index}>
                          <td className="py-3 bg-white border-2 border-black">
                            <input
                              type="checkbox"
                              className="w-4 h-4"
                              checked={item.List}
                              onChange={() => handleCheckboxChange(index)}
                            />
                          </td>
                          <td className="py-3 bg-white border-2 border-black">
                            {item.ProcessG_CD}
                          </td>
                          <td className="py-3 bg-white border-2 border-black">
                            {item.ProcessG_Mark}
                          </td>
                          <td className="py-3 bg-white border-2 border-black">
                            {item.ProcessG_Name}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan="4"
                          className="py-3 bg-white border-2 border-black text-center"
                        >
                          No data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              <div className="grid grid-cols-2 mt-4 ">
                <div className="flex justify-center">
                  <button className="bg-blue-500 hover:bg-blue-700 w-28 h-10 rounded-lg">
                    <label className="text-white font-semibold">Default</label>
                  </button>
                </div>
                <div className="flex justify-center">
                  <button className="bg-blue-500 hover:bg-blue-700 w-28 h-10 rounded-lg">
                    <label className="text-white font-semibold">Clear</label>
                  </button>
                </div>
              </div>
            </div>
            <div className="w-full">
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Sort
                  </label>
                  <div className="w-3/5">
                    <select
                      id="Sort"
                      value={StatusData?.Sort || ""}
                      onChange={handleInputChange}
                      defaultValue="Plan_Date"
                      className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full"
                    >
                      <option value=""></option>
                      <option value="Plan_Date">Plan_Date</option>
                      <option value="Product_Delivery">Product_Delivery</option>
                      <option value="Result_Date">Result_Date</option>
                    </select>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <div className="w-auto items-center flex">
                    <input
                      id="Info_View"
                      checked={StatusData?.Info_View === true}
                      onChange={handleInputChange}
                      type="checkbox"
                      defaultChecked
                      className="w-4 h-4"
                    />
                  </div>
                  <div className="w-full items-center">
                    <input
                      type="text"
                      readOnly
                      defaultValue="Info_View"
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Print_Object
                  </label>
                  <div className="w-3/5">
                    <select
                      id="Print_Object"
                      value={StatusData?.Print_Object || ""}
                      onChange={handleInputChange}
                      defaultValue="Yes"
                      className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full"
                    >
                      <option value=""></option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <div className="w-auto items-center flex">
                    <input
                      id="Color_Separate"
                      checked={StatusData?.Color_Separate === true}
                      onChange={handleInputChange}
                      type="checkbox"
                      defaultChecked
                      className="w-4 h-4"
                    />
                  </div>
                  <div className="w-full items-center">
                    <input
                      type="text"
                      readOnly
                      defaultValue="Color_Separate"
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Mrk_Days
                  </label>
                  <div className="w-3/5">
                    <input
                      id="Mark_Days"
                      value={
                        StatusData?.[`Mark_Days`]
                          ? new Date(StatusData[`Mark_Days`])
                              .toISOString()
                              .split("T")[0]
                          : ""
                      }
                      onChange={handleInputChange}
                      type="date"
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <div className="w-auto items-center flex">
                    <input
                      id="RPD_View"
                      checked={StatusData?.RPD_View === true}
                      onChange={handleInputChange}
                      type="checkbox"
                      defaultChecked
                      className="w-4 h-4"
                    />
                  </div>
                  <div className="w-full items-center">
                    <input
                      type="text"
                      defaultValue="Result_Date_View"
                      readOnly
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Od_Pending
                  </label>
                  <div className="w-3/5">
                    <select
                      id="S_Od_Pending"
                      value={StatusData?.S_Od_Pending || ""}
                      onChange={handleInputChange}
                      defaultValue="No"
                      className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                    >
                      <option value=""></option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Pt_Pending
                  </label>
                  <div className="w-3/5">
                    <select
                      id="S_Parts_Pending"
                      value={StatusData?.S_Parts_Pending || ""}
                      onChange={handleInputChange}
                      defaultValue="No"
                      className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                    >
                      <option value=""></option>
                      <option value="Yes">Yes</option>
                      <option value="No">No</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Customer1
                  </label>
                  <div className="w-3/5 flex gap-2">
                    <div className="w-1/2">
                      <select
                        id="S_Customer_CD1"
                        value={StatusData?.S_Customer_CD1 || ""}
                        onChange={handleInputChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                      >
                        <option value={processGPlanData?.S_Customer_CD1}>
                          {processGPlanData?.S_Customer_CD1}
                        </option>
                        {Array.isArray(CustomerData) &&
                        CustomerData.length > 0 ? (
                          <>
                            <option disabled>
                              Customer_CD | Customer_Abb | Customer_Name |
                              Customer_Remark
                            </option>
                            {CustomerData.map((item, index) => (
                              <option key={index} value={item.Customer_CD}>
                                {item.Customer_CD} | {item.Customer_Abb} |{" "}
                                {item.Customer_Name} | {item.Customer_Remark}
                              </option>
                            ))}
                          </>
                        ) : (
                          <option value="">No information</option>
                        )}
                      </select>
                    </div>
                    <div className="w-1/2">
                      <input
                        readOnly
                        id="S_Customer_Abb1"
                        value={customerAbbForRow1}
                        onChange={handleInputChange}
                        type="text"
                        className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Cus_Name1
                  </label>
                  <div className="w-3/5">
                    <input
                      id="S_Customer_Name1"
                      value={StatusData?.S_Customer_Name1 || ""}
                      onChange={handleInputChange}
                      type="text"
                      className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Customer2
                  </label>
                  <div className="w-3/5 flex gap-2">
                    <div className="w-1/2">
                      <select
                        id="S_Customer_CD2"
                        value={StatusData?.S_Customer_CD2 || ""}
                        onChange={handleInputChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                      >
                        <option value={processGPlanData?.S_Customer_CD2}>
                          {processGPlanData?.S_Customer_CD2}
                        </option>
                        {Array.isArray(CustomerData) &&
                        CustomerData.length > 0 ? (
                          <>
                            <option disabled>
                              Customer_CD | Customer_Abb | Customer_Name |
                              Customer_Remark
                            </option>
                            {CustomerData.map((item, index) => (
                              <option key={index} value={item.Customer_CD}>
                                {item.Customer_CD} | {item.Customer_Abb} |{" "}
                                {item.Customer_Name} | {item.Customer_Remark}
                              </option>
                            ))}
                          </>
                        ) : (
                          <option value="">No information</option>
                        )}
                      </select>
                    </div>
                    <div className="w-1/2">
                      <input
                        readOnly
                        id="S_Customer_Abb2"
                        value={customerAbbForRow2}
                        onChange={handleInputChange}
                        type="text"
                        className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Cus_Name2
                  </label>
                  <div className="w-3/5">
                    <input
                      id="S_Customer_Name2"
                      value={StatusData?.S_Customer_Name2 || ""}
                      onChange={handleInputChange}
                      type="text"
                      className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Customer3
                  </label>
                  <div className="w-3/5 flex gap-2">
                    <div className="w-1/2">
                      <select
                        id="S_Customer_CD3"
                        value={StatusData?.S_Customer_CD3 || ""}
                        onChange={handleInputChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                      >
                        <option value={processGPlanData?.S_Customer_CD3}>
                          {processGPlanData?.S_Customer_CD3}
                        </option>
                        {Array.isArray(CustomerData) &&
                        CustomerData.length > 0 ? (
                          <>
                            <option disabled>
                              Customer_CD | Customer_Abb | Customer_Name |
                              Customer_Remark
                            </option>
                            {CustomerData.map((item, index) => (
                              <option key={index} value={item.Customer_CD}>
                                {item.Customer_CD} | {item.Customer_Abb} |{" "}
                                {item.Customer_Name} | {item.Customer_Remark}
                              </option>
                            ))}
                          </>
                        ) : (
                          <option value="">No information</option>
                        )}
                      </select>
                    </div>
                    <div className="w-1/2">
                      <input
                        readOnly
                        id="S_Customer_Abb3"
                        value={customerAbbForRow3}
                        onChange={handleInputChange}
                        type="text"
                        className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Cus_Name3
                  </label>
                  <div className="w-3/5">
                    <input
                      id="S_Customer_Name3"
                      value={StatusData?.S_Customer_Name3 || ""}
                      type="text"
                      onChange={handleInputChange}
                      className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 items-center mb-3">
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Not_Customer
                  </label>
                  <div className="w-3/5 flex gap-2">
                    <div className="w-1/2">
                      <select
                        id="S_No_Customer_CD"
                        value={StatusData?.S_No_Customer_CD?.not || ""}
                        onChange={(e) => {
                          const selectedValue = e.target.value;
                          handleInputChange({
                            target: {
                              id: "S_No_Customer_CD",
                              value: { not: selectedValue },
                            },
                          });
                        }}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full"
                      >
                        <option value={processGPlanData?.S_No_Customer_CD?.not}>
                          {processGPlanData?.S_No_Customer_CD?.not}
                        </option>
                        {Array.isArray(CustomerData) &&
                        CustomerData.length > 0 ? (
                          <>
                            <option disabled>
                              Customer_CD | Customer_Abb | Customer_Name |
                              Customer_Remark
                            </option>
                            {CustomerData.map((item, index) => (
                              <option key={index} value={item.Customer_CD}>
                                {item.Customer_CD} | {item.Customer_Abb} |{" "}
                                {item.Customer_Name} | {item.Customer_Remark}
                              </option>
                            ))}
                          </>
                        ) : (
                          <option value="">No information</option>
                        )}
                      </select>
                    </div>
                    <div className="w-1/2">
                      <input
                        readOnly
                        id="S_No_Customer_Abb"
                        value={nocustomerAbbForRow}
                        onChange={(event) => setCustomerData(event)}
                        type="text"
                        className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <label className="font-medium text-xs w-2/5 text-end">
                    Item1
                  </label>
                  <div className="w-3/5 flex gap-2">
                    <div className="w-1/2">
                      <select
                        id="S_Item1_CD"
                        value={StatusData?.S_Item1_CD || ""}
                        onChange={handleInputChange}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                      >
                        <option value={processGPlanData?.S_Item1_CD}>
                          {processGPlanData?.S_Item1_CD}
                        </option>
                        {Array.isArray(Item1Data) && Item1Data.length > 0 ? (
                          <>
                            <option disabled>
                              Item1_CD | Item1_Abb |Item1_Remark
                            </option>
                            {Item1Data.map((item, index) => (
                              <option key={index} value={item.Item1_CD}>
                                {item.Item1_CD} | {item.Item1_Abb} |{" "}
                                {item.Item1_Remark}
                              </option>
                            ))}
                          </>
                        ) : (
                          <option value="">No information</option>
                        )}
                      </select>
                    </div>
                    <div className="w-1/2">
                      <input
                        readOnly
                        id="S_Item1_Abb2"
                        value={item1AbbForRow}
                        onChange={handleInputChange}
                        type="text"
                        className="bg-white border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-9 gap-2 mb-3">
                <div className="col-span-2 text-end">
                  <label className="font-medium text-xs">Order_Progress</label>
                </div>
                <div className="col-span-3">
                  <select
                    id="S_St_Od_Progress_CD"
                    value={StatusData?.S_St_Od_Progress_CD || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(OdProgressData) &&
                    OdProgressData.length > 0 ? (
                      <>
                        <option disabled>Od_Progress_Symbol</option>
                        {OdProgressData.map((item, index) => (
                          <option key={index} value={item.Od_Progress_CD}>
                            {item.Od_Progress_Symbol}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold">~</label>
                </div>
                <div className="col-span-3">
                  <select
                    id="S_Ed_Od_Progress_CD"
                    value={StatusData?.S_Ed_Od_Progress_CD || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(OdProgressData) &&
                    OdProgressData.length > 0 ? (
                      <>
                        <option disabled>Od_Progress_Symbol</option>
                        {OdProgressData.map((item, index) => (
                          <option key={index} value={item.Od_Progress_CD}>
                            {item.Od_Progress_Symbol}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-9 gap-2 mb-3">
                <div className="col-span-2 text-end">
                  <label className="font-medium text-xs">Plan_Progress</label>
                </div>
                <div className="col-span-3">
                  <select
                    id="S_St_Pl_Progress_CD"
                    value={StatusData?.S_St_Pl_Progress_CD || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(plprogressData) &&
                    plprogressData.length > 0 ? (
                      <>
                        <option disabled>Pl_Progress_Symbol</option>
                        {plprogressData.map((item, index) => (
                          <option key={index} value={item.Pl_Progress_CD}>
                            {item.Pl_Progress_Symbol}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold">~</label>
                </div>
                <div className="col-span-3">
                  <select
                    id="S_Ed_Pl_Progress_CD"
                    value={StatusData?.S_Ed_Pl_Progress_CD || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(plprogressData) &&
                    plprogressData.length > 0 ? (
                      <>
                        <option disabled>Pl_Progress_Symbol</option>
                        {plprogressData.map((item, index) => (
                          <option key={index} value={item.Pl_Progress_CD}>
                            {item.Pl_Progress_Symbol}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-4 text-end">
                  <label className="font-medium text-xs">Parts_No</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="S_St_Parts_No"
                    value={StatusData?.S_St_Parts_No || ""}
                    onChange={handleInputChange}
                    type="text"
                    className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                  />
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold text-base">~</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="S_Ed_Parts_No"
                    value={StatusData?.S_Ed_Parts_No || ""}
                    type="text"
                    onChange={handleInputChange}
                    className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                  />
                </div>
                <div className="col-span-1"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-4"></div>
                <div className="col-span-3 flex items-center gap-2">
                  <div>
                    <input
                      id="Before_Days"
                      value={StatusData?.Before_Days}
                      type="text"
                      onChange={handleInputChange}
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                  <label className="font-medium text-xs">Days_Before</label>
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold text-base">~</label>
                </div>
                <div className="col-span-3 flex items-center gap-2">
                  <div>
                    <input
                      id="After_Days"
                      value={StatusData?.After_Days}
                      type="text"
                      onChange={handleInputChange}
                      className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    />
                  </div>
                  <label className="font-medium text-xs">Days_After</label>
                </div>
                <div className="col-span-1"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-4 text-end">
                  <label className="font-medium text-xs">Plan_Date</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="Tg_St_Pl_Date"
                    value={
                      StatusData?.[`Tg_St_Pl_Date`]
                        ? new Date(StatusData[`Tg_St_Pl_Date`])
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    type="date"
                    onChange={handleInputChange}
                    className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                  />
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold text-base">~</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="Tg_Ed_Pl_Date"
                    value={
                      StatusData?.[`Tg_Ed_Pl_Date`]
                        ? new Date(StatusData[`Tg_Ed_Pl_Date`])
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    type="date"
                    onChange={handleInputChange}
                    className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                  />
                </div>
                <div className="col-span-1"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-3"></div>
                <div className="col-span-1 flex justify-end items-center">
                  <input
                    id="Result_Search"
                    checked={StatusData?.Result_Search ?? false}
                    type="checkbox"
                    className="w-4 h-4"
                    onChange={handleInputChange}
                  />
                </div>
                <div className="col-span-3">
                  <input
                    type="text"
                    defaultValue="Result_Search"
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-full"
                    readOnly
                  />
                </div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-4 text-end">
                  <label className="font-medium text-xs">Result_Date</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="Tg_St_Rs_Date"
                    value={
                      StatusData?.[`Tg_St_Rs_Date`]
                        ? new Date(StatusData[`Tg_St_Rs_Date`])
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    onChange={handleInputChange}
                    type="date"
                    className={`bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full ${
                      !isChecked ? "bg-gray-300 cursor-not-allowed" : ""
                    }`}
                    disabled={!isChecked}
                  />
                </div>
                <div className="col-span-1 text-center">
                  <label className="font-bold text-base">~</label>
                </div>
                <div className="col-span-3">
                  <input
                    id="Tg_Ed_Rs_Date"
                    value={
                      StatusData?.[`Tg_Ed_Rs_Date`]
                        ? new Date(StatusData[`Tg_Ed_Rs_Date`])
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    onChange={handleInputChange}
                    type="date"
                    className={`bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-1 w-full ${
                      !isChecked ? "bg-gray-300 cursor-not-allowed" : ""
                    }`}
                    disabled={!isChecked}
                  />
                </div>
                <div className="col-span-1"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-3 text-end">
                  <label className="font-medium text-xs">TG_ProcessG</label>
                </div>
                <div className="col-span-9">
                  <select
                    id="TG_ProcessG"
                    value={StatusData?.TG_ProcessG || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(processGData) && processGData.length > 0 ? (
                      <>
                        <option disabled>ProcessG_Mark</option>
                        {processGData.map((item, index) => (
                          <option key={index} value={item.ProcessG_CD}>
                            {item.ProcessG_Mark}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-12 gap-2 mb-3">
                <div className="col-span-3 text-end">
                  <label className="font-medium text-xs">TG_Process</label>
                </div>
                <div className="col-span-9">
                  <select
                    id="TG_Process"
                    value={StatusData?.TG_Process || ""}
                    onChange={handleInputChange}
                    className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full"
                  >
                    <option value=""></option>
                    {Array.isArray(processData) && processData.length > 0 ? (
                      <>
                        <option disabled>ProcessG_Abb</option>
                        {processData.map((item, index) => (
                          <option key={index} value={item.Process_CD}>
                            {item.Process_Abb}
                          </option>
                        ))}
                      </>
                    ) : (
                      <option value="">No information</option>
                    )}
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-12 gap-2 items-center mb-3">
                <div className="col-span-6"></div>
                <div className="col-span-3">
                  <div className="flex gap-2 items-center">
                    <input
                      id="List"
                      checked={StatusData.List ?? true}
                      onChange={handleInputChange}
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4"
                    />
                    <label className="font-medium text-xs">
                      ProcessG List View
                    </label>
                  </div>
                </div>
                <div className="col-span-3"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 items-center mb-3">
                <div className="col-span-6"></div>
                <div className="col-span-3">
                  <div className="flex gap-2 items-center">
                    <input
                      id="Graph"
                      checked={StatusData.Graph}
                      onChange={handleInputChange}
                      type="checkbox"
                      className="h-4 w-4"
                    />
                    <label className="font-medium text-xs">
                      ProcessG Graph View
                    </label>
                  </div>
                </div>
                <div className="col-span-3"></div>
              </div>
              <div className="grid grid-cols-12 gap-2 xl:ml-40 mt-5">
                <div className="flex justify-items-end items-end gap-2">
                  <input
                    id="Settles"
                    checked={StatusData.Settles}
                    onChange={handleInputChange}
                    type="checkbox"
                    defaultChecked
                    className="h-6 w-6"
                  />
                  <input
                    id="Settles_Day"
                    value={StatusData?.Settles_Day || ""}
                    onChange={handleInputChange}
                    type="text"
                    className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-1 w-32"
                  />
                  <label className="font-normal text-lg">Settles_Day</label>
                </div>
                <div className="col-span-3"></div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 gap-4">
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F1"
                disabled={!buttonState.F1}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Search <br />
                検索 (F1)
              </button>
              <button
                id="F2"
                disabled={!buttonState.F2}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                設定 <br />
                (F2)
              </button>
              <button
                id="F3"
                disabled={!buttonState.F3}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Show <br />
                照会 (F3)
              </button>
              <button
                id="F4"
                disabled={!buttonState.F4}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                検索リア <br />
                (F4)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F5"
                disabled={!buttonState.F5}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                追加 <br />
                (F5)
              </button>
              <button
                id="F6"
                disabled={!buttonState.F6}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                (F6)
              </button>
              <button
                id="F7"
                disabled={!buttonState.F7}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                List <br />一 覽 (F7)
              </button>
              <button
                id="F8"
                disabled={!buttonState.F8}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                (F8)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                id="F9"
                disabled={!buttonState.F9}
                onClick={handleF9Click}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Action <br />
                実行 (F9)
              </button>
              <button
                id="F10"
                disabled={!buttonState.F10}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                削除 <br />
                (F10)
              </button>
              <button
                id="F11"
                disabled={!buttonState.F11}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                次入力 <br />
                (F11)
              </button>
              <button
                id="F12"
                disabled={!buttonState.F12}
                onClick={handleF12Click}
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              >
                Exit <br />
                終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
