import React, { useState, useEffect } from "react";
import Navbar from "./Navbar";
import Sidebar from "./Sidebar";

export function EC1() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [orderNo, setOrderNo] = useState("");
  const [S_NAV_Name, setS_NAV_Name] = useState("");
  const [selectedSearchType, setSelectedSearchType] = useState("");
  const [selectedD1Type, setSelectedD1Type] = useState("");
  const [selectedD2Type, setSelectedD2Type] = useState("");
  const [selectedD3Type, setSelectedD3Type] = useState("");
  const [viewSchedule, setViewSchedule] = useState("");
  const [planTarget, setPlanTarget] = useState("");
  const [format, setFormat] = useState("");
  const [changePage, setChangePage] = useState("");
  const [target, setTarget] = useState("");
  const [markDays, setMarkDays] = useState("");
  const [checkboxGroupState, setCheckboxGroupState] = useState("");
  const [planListData, setPlanListData] = useState([]);
  const [destinationName, setDestinationName] = useState("");
  const [destinationName2, setDestinationName2] = useState("");
  const [selectedSalesGrpAbb, setSelectedSalesGrpAbb] = useState("");
  const [destinationName5, setDestinationName5] = useState("");
  const [PriceName, setPriceName] = useState("");
  const [selectedSalesGrpAbb2, setSelectedSalesGrpAbb2] = useState("");

  useEffect(() => {
    const handleMessage = (event) => {
      // Ensure the message is received from the correct source
      if (event.origin !== `${apiUrl_5173}`) return;

      // Retrieve and set the order number
      const {
        S_Order_No,
        S_NAV_Name,
        selectedSearchType,
        selectedD1Type,
        selectedD2Type,
        selectedD3Type,
        viewSchedule,
        planTarget,
        format,
        changePage,
        target,
        markDays,
        checkboxGroupState,
        planListData,
        destinationName,
        destinationName2,
        selectedSalesGrpAbb,
        destinationName5,
        selectedSalesGrpAbb2,
        PriceName,
      } = event.data;

      setOrderNo(S_Order_No || "");
      setSelectedSearchType(selectedSearchType || "");
      setSelectedD1Type(selectedD1Type || "");
      setSelectedD2Type(selectedD2Type || "");
      setSelectedD3Type(selectedD3Type || "");
      setViewSchedule(viewSchedule || "");
      setPlanTarget(planTarget || "");
      setFormat(format || "");
      setChangePage(changePage || "");
      setTarget(target || "");
      setMarkDays(markDays || "");
      setCheckboxGroupState(checkboxGroupState || "");
      setS_NAV_Name(S_NAV_Name || "");
      setPlanListData(planListData || []);
      setDestinationName(destinationName || "");
      setDestinationName2(destinationName2 || "");
      setSelectedSalesGrpAbb(selectedSalesGrpAbb || "");
      setDestinationName5(destinationName5 || "");
      setSelectedSalesGrpAbb2(selectedSalesGrpAbb2 || "");
      setPriceName(PriceName || "");
    };

    // Listen for messages
    window.addEventListener("message", handleMessage);

    // Clean up listener on unmount
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);
  return (
    <div>
      <div className="flex bg-[#E9EFEC] h-[100vh]">
        <Sidebar />
        <div className="flex flex-col w-screen mr-2 ml-2">
          <Navbar />
          <div className="flex-1 flex-col overflow-x-hidden flex-grow p-2 bg-white mt-2 rounded-md">
            <div className="grid grid-cols-1">
              <h1 className="text-2xl font-bold mt-3 text-center">EC 1</h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full mt-5 overflow-x-auto pr-10">
                <div className="min-w-[1800px] w-full mb-7">
                  {/* Start Group 1 */}
                  <div className="flex pl-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center">
                      <label className="font-bold text-xs">Search_Type</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={selectedSearchType} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={selectedSearchType}>
                          {selectedSearchType}
                        </option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Delivery1</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={selectedD1Type} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={selectedD1Type}>{selectedD1Type}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Delivery2</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={selectedD2Type} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={selectedD2Type}>{selectedD2Type}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Delivery3</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={selectedD3Type} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={selectedD3Type}>{selectedD3Type}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">View_Schedule</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={viewSchedule} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={viewSchedule}>{viewSchedule}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Plan_Target
                        </label>
                        <select
                          className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                          value={planTarget} // Bind the dropdown to the selectedSearchType state
                          readOnly // Optional: Make the dropdown non-editable
                        >
                          <option value={planTarget}>
                            {planTarget === "true"
                              ? "Yes"
                              : planTarget === "false"
                              ? "No"
                              : ""}
                          </option>
                        </select>
                        {/* <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div> */}
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 1 */}

                  {/* Start Group 2 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-[52px]">
                      <label className="font-bold text-xs">
                        [Order_Info_Search]
                      </label>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center">
                      <label className="font-bold text-xs">Format</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={format} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={format}>{format}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-20">
                      <label className="font-bold text-xs">Change_Page</label>
                    </div>
                    <div className="relative w-40 lg:w-56">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={changePage} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={changePage}>{changePage}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-12">
                      <label className="font-bold text-xs">Target</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <select
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8"
                        value={target} // Bind the dropdown to the selectedSearchType state
                        readOnly // Optional: Make the dropdown non-editable
                      >
                        <option value={target}>{target}</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[50px]">
                      <label className="font-bold text-xs">Mark_days</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        id="Mark_days"
                        type="date"
                        className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-40"
                        value={markDays} // Bind to the prop value
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Order_Progress
                        </label>
                        <select
                          value={planListData?.S_St_Od_Progress_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option
                            value={planListData?.S_St_Od_Progress_CD || ""}
                          >
                            {
                              planListData?.S_St_Od_Progress_CD === "1"
                                ? "PL"
                                : planListData?.S_St_Od_Progress_CD === "0"
                                ? "RO"
                                : planListData?.S_St_Od_Progress_CD === "2"
                                ? "MI"
                                : planListData?.S_St_Od_Progress_CD === "3"
                                ? "WI"
                                : planListData?.S_St_Od_Progress_CD === "4"
                                ? "PC"
                                : planListData?.S_St_Od_Progress_CD === "5"
                                ? "FG"
                                : planListData?.S_St_Od_Progress_CD === "6"
                                ? "DL"
                                : planListData?.S_St_Od_Progress_CD === "7"
                                ? "TA"
                                : planListData?.S_St_Od_Progress_CD === "8"
                                ? "CF"
                                : planListData?.S_St_Od_Progress_CD === "9"
                                ? "CC"
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <select
                          value={planListData?.S_Ed_Od_Progress_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option
                            value={planListData?.S_Ed_Od_Progress_CD || ""}
                          >
                            {
                              planListData?.S_Ed_Od_Progress_CD === "1"
                                ? "PL"
                                : planListData?.S_Ed_Od_Progress_CD === "0"
                                ? "RO"
                                : planListData?.S_Ed_Od_Progress_CD === "2"
                                ? "MI"
                                : planListData?.S_Ed_Od_Progress_CD === "3"
                                ? "WI"
                                : planListData?.S_Ed_Od_Progress_CD === "4"
                                ? "PC"
                                : planListData?.S_Ed_Od_Progress_CD === "5"
                                ? "FG"
                                : planListData?.S_Ed_Od_Progress_CD === "6"
                                ? "DL"
                                : planListData?.S_Ed_Od_Progress_CD === "7"
                                ? "TA"
                                : planListData?.S_Ed_Od_Progress_CD === "8"
                                ? "CF"
                                : planListData?.S_Ed_Od_Progress_CD === "9"
                                ? "CC"
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 1 */}

                  {/* Start Group 2 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-[38px]">
                      <label className="font-bold text-xs">Order_No</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        type="text"
                        value={orderNo}
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-5 pl-10">
                      <input
                        type="checkbox"
                        id="checkbox1"
                        checked={checkboxGroupState.checkbox1}
                        className="w-5 h-5 rounded-full"
                      />
                      <label
                        htmlFor="checkbox1"
                        className="text-sm bg-[#ffff99]"
                      >
                        Info_View
                      </label>

                      {/* Checkbox 2 */}
                      <input
                        type="checkbox"
                        id="checkbox2"
                        checked={checkboxGroupState.checkbox2}
                        className="w-5 h-5 rounded-full"
                      />
                      <label
                        htmlFor="checkbox2"
                        className="text-sm bg-[#ffff99]"
                      >
                        color_View
                      </label>

                      {/* Checkbox 3 */}
                      <input
                        type="checkbox"
                        id="checkbox3"
                        checked={checkboxGroupState.checkbox3}
                        className="w-5 h-5 rounded-full"
                      />
                      <label
                        htmlFor="checkbox3"
                        className="text-sm bg-[#ffff99]"
                      >
                        Result_Date_View
                      </label>

                      {/* Checkbox 4 */}
                      <input
                        type="checkbox"
                        id="checkbox4"
                        checked={checkboxGroupState.checkbox4}
                        className="w-5 h-5 rounded-full"
                      />
                      <label
                        htmlFor="checkbox4"
                        className="text-sm bg-[#ffff99]"
                      >
                        CT_View
                      </label>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-44">
                      <label className="font-bold text-xs">Ctl_Person</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData.S_Od_Ctl_Person_CD}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData.S_Od_Ctl_Person_CD}>
                          {planListData.S_Od_Ctl_Person_CD}
                        </option>
                      </select>
                    </div>
                    <input
                      value={selectedSalesGrpAbb}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Delivery_CAT
                        </label>
                        <select
                          value={planListData?.S_St_Delivery_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option value={planListData?.S_St_Delivery_CD || ""}>
                            {" "}
                            {
                              planListData?.S_St_Delivery_CD === "0"
                                ? "Not-Del(C_Ree)"
                                : planListData?.S_St_Delivery_CD === "1"
                                ? "S_TOP"
                                : planListData?.S_St_Delivery_CD === "2"
                                ? "Ship_Air"
                                : planListData?.S_St_Delivery_CD === "3"
                                ? "TOP"
                                : planListData?.S_St_Delivery_CD === "4"
                                ? "TOP_FG"
                                : planListData?.S_St_Delivery_CD === "5"
                                ? "Cust.Ree"
                                : planListData?.S_St_Delivery_CD === "6"
                                ? "Delivery"
                                : planListData?.S_St_Delivery_CD === "7"
                                ? "Air_Ship"
                                : planListData?.S_St_Delivery_CD === "8"
                                ? "Q_Ship"
                                : planListData?.S_St_Delivery_CD === "9"
                                ? "Q_FG"
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <select
                          value={planListData?.S_Ed_Delivery_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option value={planListData?.S_Ed_Delivery_CD || ""}>
                            {" "}
                            {
                              planListData?.S_Ed_Delivery_CD === "0"
                                ? "Not-Del(C_Ree)"
                                : planListData?.S_Ed_Delivery_CD === "1"
                                ? "S_TOP"
                                : planListData?.S_Ed_Delivery_CD === "2"
                                ? "Ship_Air"
                                : planListData?.S_Ed_Delivery_CD === "3"
                                ? "TOP"
                                : planListData?.S_Ed_Delivery_CD === "4"
                                ? "TOP_FG"
                                : planListData?.S_Ed_Delivery_CD === "5"
                                ? "Cust.Ree"
                                : planListData?.S_Ed_Delivery_CD === "6"
                                ? "Delivery"
                                : planListData?.S_Ed_Delivery_CD === "7"
                                ? "Air_Ship"
                                : planListData?.S_Ed_Delivery_CD === "8"
                                ? "Q_Ship"
                                : planListData?.S_Ed_Delivery_CD === "9"
                                ? "Q_FG"
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 2 */}

                  {/* Start Group 3 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-7">
                      <label className="font-bold text-xs">NAV_Name</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        readOnly
                        value={S_NAV_Name}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Product_Grp</label>
                    </div>
                    <div className="relative w-24 ml-1">
                      <select
                        readOnly
                        value={planListData.S_St_Pd_Grp_CD}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData.S_St_Pd_Grp_CD}>
                          {planListData.S_St_Pd_Grp_CD}
                        </option>
                      </select>
                    </div>
                    <input
                      value={destinationName}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    <span className="text-lg mx-3">~</span>
                    {/* Start */}
                    <div className="relative w-24">
                      <select
                        readOnly
                        value={planListData.S_Ed_Pd_Grp_CD}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData.S_Ed_Pd_Grp_CD}>
                          {planListData.S_Ed_Pd_Grp_CD}
                        </option>
                      </select>
                    </div>
                    <input
                      value={destinationName2}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[123px]">
                      <label className="font-bold text-xs">Sales_Grp</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData.S_Sl_Grp_CD}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData.S_Sl_Grp_CD}>
                          {planListData.S_Sl_Grp_CD}
                        </option>
                      </select>
                    </div>
                    <input
                      value={destinationName5}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Schedule_CAT
                        </label>
                        <select
                          value={planListData?.S_St_Schedule_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option value={planListData?.S_St_Schedule_CD || ""}>
                            {
                              planListData?.S_St_Schedule_CD === "1"
                                ? "Super Top"
                                : planListData?.S_St_Schedule_CD === "2"
                                ? "Top"
                                : planListData?.S_St_Schedule_CD === "3"
                                ? "Urgent"
                                : planListData?.S_St_Schedule_CD === "4"
                                ? "Normal"
                                : planListData?.S_St_Schedule_CD === "5"
                                ? ""
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <select
                          value={planListData?.S_Ed_Schedule_CD || ""}
                          readOnly
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20"
                        >
                          <option value={planListData?.S_Ed_Schedule_CD || ""}>
                            {" "}
                            {
                              planListData?.S_Ed_Schedule_CD === "1"
                                ? "Super Top"
                                : planListData?.S_Ed_Schedule_CD === "2"
                                ? "Top"
                                : planListData?.S_Ed_Schedule_CD === "3"
                                ? "Urgent"
                                : planListData?.S_Ed_Schedule_CD === "4"
                                ? "Normal"
                                : planListData?.S_Ed_Schedule_CD === "5"
                                ? ""
                                : "" // Default value if no match
                            }
                          </option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 3 */}

                  {/* Start Group 4 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center">
                      <label className="font-bold text-xs">Product_Name</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Product_Name}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Not_Pd_Grp1</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData?.S_No_Pd_Grp_CD1?.not || ""}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8"
                      >
                        <option
                          value={planListData?.S_No_Pd_Grp_CD1?.not || ""}
                        >
                          {planListData?.S_No_Pd_Grp_CD1?.not || ""}
                        </option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Price_CAT</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData?.S_Price_CD || ""}
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData?.S_Price_CD || ""}>
                          {planListData?.S_Price_CD || ""}
                        </option>
                      </select>
                    </div>
                    <input
                      value={PriceName || ""}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[54px]">
                      <label className="font-bold text-xs">Sales_Person</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData.S_Sl_Person_CD}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData.S_Sl_Person_CD}>
                          {planListData.S_Sl_Person_CD}
                        </option>
                      </select>
                    </div>
                    <input
                      value={selectedSalesGrpAbb2}
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Target__CAT
                        </label>
                        <select className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20">
                          <option value=""></option>
                          <option value="0">0</option>
                          <option value="1">1</option>
                          <option value="2">2</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <select className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20">
                          <option value=""></option>
                          <option value="0">0</option>
                          <option value="1">1</option>
                          <option value="2">2</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 4 */}

                  {/* Start Group 5 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-10">
                      <label className="font-bold text-xs">NAV_Size</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        type="text"
                        value={planListData.S_NAV_Size}
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Not_Pd_Grp2</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData?.S_No_Pd_Grp_CD2?.not || ""}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8"
                      >
                        <option
                          value={planListData?.S_No_Pd_Grp_CD2?.not || ""}
                        >
                          {planListData?.S_No_Pd_Grp_CD2?.not || ""}
                        </option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Request_CAT</label>
                    </div>
                    <div className="relative w-24">
                      <select
                        value={planListData?.S_Request1_CD || ""}
                        readOnly
                        className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8"
                      >
                        <option value={planListData?.S_Request1_CD || ""}>
                          {planListData?.S_Request1_CD || ""}
                        </option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />

                    <div className="relative w-24 ml-1">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />

                    <div className="relative w-24 ml-1">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Request_Delivery
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 5 */}

                  {/* Start Group 6 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-5">
                      <label className="font-bold text-xs">Product_Size</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Product_Size}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Customer1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[265px]">
                      <label className="font-bold text-xs">
                        Od_No_of_Customer
                      </label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-56 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Mate1</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          NAV_Delivery
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 6 */}

                  {/* Start Group 7 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-4">
                      <label className="font-bold text-xs">Cus_Draw_No</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Customer_Draw}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Customer2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Cus_Name1</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-52 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Item1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Mate2</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Confirm_Delivery
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 7 */}

                  {/* Start Group 8 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-3">
                      <label className="font-bold text-xs">Com_Draw_No</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        type="text"
                        value={planListData.S_Company_Draw}
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Customer3</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Cus_Name2</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-52 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Item2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Mate3</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Product_Received
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 8 */}

                  {/* Start Group 9 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-6">
                      <label className="font-bold text-xs">Pd_Draw_No</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Product_Draw}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-5">
                      <label className="font-bold text-xs">Not_Customer</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Cus_Name3</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-52 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Item3</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Mate4</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Product_Received
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 9 */}

                  {/* Start Group 10 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-8">
                      <label className="font-bold text-xs">Sales_Note</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Sl_instructions}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[52px]">
                      <label className="font-bold text-xs">Specific1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-12">
                      <label className="font-bold text-xs">Coating1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Item4</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Mate5</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Product_Complete
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 10 */}

                  {/* Start Group 11 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-[45px]">
                      <label className="font-bold text-xs">Pd_Note</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Pd_instructions}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[52px]">
                      <label className="font-bold text-xs">Specific2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-12">
                      <label className="font-bold text-xs">Coating2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-20">
                      <label className="font-bold text-xs">Od_Pent</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[83px]">
                      <label className="font-bold text-xs">Od_CAT1</label>
                    </div>
                    <div className="relative w-28">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          QC_Complete
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 11 */}

                  {/* Start Group 12 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-[30px]">
                      <label className="font-bold text-xs">Pd_Remark</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_Pd_Remark}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-7">
                      <label className="font-bold text-xs">Not_Specific1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-12">
                      <label className="font-bold text-xs">Coating3</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[69px]">
                      <label className="font-bold text-xs">TempShip</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[83px]">
                      <label className="font-bold text-xs">Od_CAT2</label>
                    </div>
                    <div className="relative w-28">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Shipment_Date
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 12 */}

                  {/* Start Group 13 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-7">
                      <label className="font-bold text-xs">QC_Remark</label>
                    </div>
                    <div className="relative w-40 lg:w-44">
                      <input
                        value={planListData.S_I_Remark}
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[28px]">
                      <label className="font-bold text-xs">Not_Specific2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[46px]">
                      <label className="font-bold text-xs">Not_Coat</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ff99cc] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-28 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[75px]">
                      <label className="font-bold text-xs">Unrecive</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[83px]">
                      <label className="font-bold text-xs">Od_CAT3</label>
                    </div>
                    <div className="relative w-28">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Calc_Date
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 13 */}

                  {/* Start Group 14 */}
                  <div className="flex pl-5 mt-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center">
                      <label className="font-bold text-xs">EXT_Date_Time</label>
                    </div>
                    <div className="relative w-40 lg:w-60">
                      <input
                        type="text"
                        className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[587px]">
                      <label className="font-bold text-xs">Target_Year</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ffff99] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[83px]">
                      <label className="font-bold text-xs">Target_Mount</label>
                    </div>
                    <div className="relative w-28">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Calc_Process
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 14 */}
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="flex items-center font-bold pl-2">
                <label className="mr-2">Plan_Info_Search</label>
              </div>

              <div className="w-full mt-5 overflow-x-auto pr-10">
                <div className="min-w-[1800px] w-full mb-7">
                  {/* Start Group 1 */}
                  <div className="flex pl-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-3">
                      <label className="font-bold text-xs">Parts_No</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-7"
                    />
                    {/* End */}
                    <span className="text-lg mx-3">~</span>
                    {/* Start */}
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-16">
                      <label className="font-bold text-xs">Pt_Qty</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-4"
                    />
                    {/* End */}
                    <span className="text-lg mx-3">~</span>
                    {/* Start */}
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-20">
                      <label className="font-bold text-xs">Money_Obj</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-52">
                      <label className="font-bold text-xs">Pt_CAT1</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Plan_Progress
                        </label>
                        <select className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20">
                          <option value=""></option>
                          <option value="0">0</option>
                          <option value="1">1</option>
                          <option value="2">2</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <select className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 appearance-none w-20">
                          <option value=""></option>
                          <option value="0">0</option>
                          <option value="1">1</option>
                          <option value="2">2</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 1 */}

                  {/* Start Group 2 */}
                  <div className="flex pl-5 mt-3">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-3">
                      <label className="font-bold text-xs">Pt_Name</label>
                    </div>
                    <div className="relative w-40 ml-7">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[181px]">
                      <label className="font-bold text-xs">Pt_Sp_Qty</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-4"
                    />
                    {/* End */}
                    <span className="text-lg mx-3">~</span>
                    {/* Start */}
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[100px]">
                      <label className="font-bold text-xs">Outside</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-52">
                      <label className="font-bold text-xs">Pt_CAT2</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Parts_Delivery
                        </label>
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#ccffff] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 2 */}

                  {/* Start Group 3 */}
                  <div className="flex pl-5 mt-3">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-3">
                      <label className="font-bold text-xs">Req_Person</label>
                    </div>
                    <div className="relative w-32 ml-4">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <input
                      type="text"
                      className="bg-white border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Pt_Mate</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-4"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-16">
                      <label className="font-bold text-xs">Pt_NG_Qty</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}
                    <span className="text-lg mx-3">~</span>
                    {/* Start */}
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Pt_Pend</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[52px]">
                      <label className="font-bold text-xs">Pt_CAT3</label>
                    </div>
                    <div className="relative w-24">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ccffff] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">
                          Pl_Process_Date
                        </label>
                        <input
                          type="text"
                          className="bg-[#4fe2e2] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>

                      <span className="text-lg">~</span>

                      <div className="relative">
                        <input
                          type="text"
                          className="bg-[#4fe2e2] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-20"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 3 */}

                  {/* Start Group 4 */}
                  <div className="flex pl-5 mt-3">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-3">
                      <label className="font-bold text-xs">Part_Note</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-6"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[158px]">
                      <label className="font-bold text-xs">Pt_Remark</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-32 ml-4"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-[67px]">
                      <label className="font-bold text-xs">Parts_Info</label>
                    </div>
                    <input
                      type="text"
                      className="bg-[#ccffff] border-2 border-gray-500 rounded-md w-40 ml-1"
                    />
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Sort1</label>
                    </div>
                    <div className="relative w-32">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Sort2</label>
                    </div>
                    <div className="relative w-32">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}

                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-8">
                      <label className="font-bold text-xs">Sort3</label>
                    </div>
                    <div className="relative w-32">
                      <select className="border-gray-500 border-solid border-2 rounded-md bg-[#ffff99] w-full h-8">
                        <option value=""></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="flex items-center space-x-2 ml-auto">
                      <div className="flex items-center relative">
                        <label className="text-xs font-bold mr-1">Sort4</label>
                        <input
                          type="text"
                          className="bg-[#ffff99] border-solid border-2 border-gray-500 rounded-md px-2 py-0.5 w-36"
                        />
                      </div>
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 4 */}
                </div>
              </div>

              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="w-full mt-5 overflow-x-auto pr-10">
                <div className="min-w-[1000px] w-full mb-7">
                  {/* Start Group 1 */}
                  <div className="flex pl-5">
                    {/* Start */}
                    <div className="px-2 w-auto text-center pr-[52px]">
                      <label className="font-bold text-xs">[List]</label>
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center">
                      <label className="font-bold text-xs">Select_Od_No</label>
                    </div>
                    <div className="relative w-40 lg:w-56">
                      <input
                        type="text"
                        className="bg-[#cc99ff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                    {/* Start */}
                    <div className="px-2 w-auto text-center pl-10">
                      <label className="font-bold text-xs">Select_Pt_No</label>
                    </div>
                    <div className="relative w-40 lg:w-56">
                      <input
                        type="text"
                        className="bg-[#cc99ff] border-solid border-2 border-gray-500 rounded-md py-0.5 w-full"
                      />
                    </div>
                    {/* End */}
                  </div>
                  {/* End Group 1 */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
