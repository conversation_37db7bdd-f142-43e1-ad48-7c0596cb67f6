import React, { useState, useEffect } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

export default function QR_CS_List() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [planlistData, setPlanlistData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // ฟังก์ชันในการแปลงวันที่
const formatDateUTC = (date) => {
  if (!date || isNaN(Date.parse(date))) return date;

  const d = new Date(date);
  const day = String(d.getUTCDate()).padStart(2, "0");
  const month = String(d.getUTCMonth() + 1).padStart(2, "0");
  const year = d.getUTCFullYear();
  const hours = String(d.getUTCHours()).padStart(2, "0");
  const minutes = String(d.getUTCMinutes()).padStart(2, "0");
  const seconds = String(d.getUTCSeconds()).padStart(2, "0");

  return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
};
  useEffect(() => {
    const handleMessage = (event) => {
      // ตรวจสอบ origin
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }

      const { status, data } = event.data;

     
if (status === 200) {
  if (Array.isArray(data)) {
    setPlanlistData(data); // ใช้ data ตรงๆ เพราะคือ array แล้ว
  } else {
    console.error("Received data is not an array.");
  }
} else {
  console.error("Failed to receive data.");
}
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  // console.log("planlistData", planlistData);

  const totalPages = Math.ceil(planlistData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const displayedData = planlistData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const goToPrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  return (
    <div className="p-8 bg-gray-50 rounded-lg shadow-lg max-w-screen-lg mx-auto">
      <h2 className="text-3xl font-semibold text-blue-600 text-center mb-6">
        QR_CS_List_view
      </h2>

      <div className="overflow-x-auto">
        <table className="max-w-[1500px] table-auto border-collapse">
          <thead>
            <tr>
              {/* หัวตาราง */}
              <th className="border px-10 py-1 text-left">Order_No</th>
              <th className="border px-10 py-1 text-left">Parts_No</th>
              <th className="border px-10 py-1 text-left">Process_No</th>
              <th className="border px-10 py-1 text-left">Process_Name</th>
              <th className="border px-10 py-1 text-left">ProcessG_Name</th>
              <th className="border px-10 py-1 text-left">CostG_CD</th>
              <th className="border px-10 py-1 text-left">Machine_Cost_Time</th>
              <th className="border px-10 py-1 text-left">Person_Time</th>
              <th className="border px-10 py-1 text-left">P_Coefficient</th>
              <th className="border px-10 py-1 text-left">Person_Cost_Time</th>
              <th className="border px-10 py-1 text-left">Cs_Complete_Date</th>
              {/* เพิ่มหัวตารางอื่นๆ ตามต้องการ */}
            </tr>
          </thead>
          <tbody>
            {displayedData.length > 0 ? (
              displayedData.map((item, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  {/* แสดงข้อมูลจากตาราง */}
                  <td className="border px-4 py-2">
                    {(item.Order_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Parts_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Process_No)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Process_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.ProcessG_Name)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.CostG_CD)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Machine_Cost_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Person_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.P_Coefficient)}
                  </td>
                  <td className="border px-4 py-2">
                    {(item.Person_Cost_Time)}
                  </td>
                  <td className="border px-4 py-2">
                    {formatDateUTC(item.Cs_Complete_Date)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3" className="border px-4 py-2 text-center">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={goToPrevPage}
          disabled={currentPage === 1}
          className={`p-2 rounded-full ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronLeft size={20} />
        </button>

        <div className="flex items-center gap-4">
          <span>
            Page {currentPage} of {totalPages || 1}
          </span>
          <select
            className="border border-gray-400 rounded px-2 py-1"
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10 Rows</option>
            <option value={15}>15 Rows</option>
            <option value={20}>20 Rows</option>
            <option value={25}>25 Rows</option>
          </select>
        </div>

        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          className={`p-2 rounded-full ${
            currentPage === totalPages || totalPages === 0
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <HiChevronRight size={20} />
        </button>
      </div>
    </div>
  );
}
