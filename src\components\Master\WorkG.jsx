import React, { useState, useEffect, useRef } from "react";
import Navbar from "../Navbar";
import Sidebar from "../Sidebar";
import DataTable from "react-data-table-component";
import axios from "axios";
import Papa from "papaparse";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";

export function WorkG() {
  const apiUrl_4000 = import.meta.env.VITE_BACKEND_URL;

  const [isF2Pressed, setIsF2Pressed] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const navigate = useNavigate();
  const [showCheckbox, setShowCheckbox] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [data, setData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [editedData, setEditedData] = useState({});
  const [isChanged, setIsChanged] = useState(false);
  const [selectedWorkGs, setSelectedWorkGs] = useState([]);
  const [selectedRowForCopy, setSelectedRowForCopy] = useState(null); // For radio selection

  const editedDataRef = useRef(editedData);

  const fetchWorkG = async () => {
    try {
      const response = await axios.get(`${apiUrl_4000}/workg/fetch-workg`);
      // console.log("Fetched data:", response.data);
      setData(response.data.data.workg || []);
    } catch (error) {
      // console.error("Error fetching workg:", error);
    }
  };

  useEffect(() => {
    fetchWorkG();
  }, []);

  const openModal = () => {
    if (selectedRowForCopy) {
      const { WorkG_CD, ...rest } = selectedRowForCopy;
      setFormData({
        WorkG_CD: "",
        ...rest,
      });
    } else {
      setFormData({
        WorkG_CD: "",
        WorkG_Name: "",
        WorkG_Abb: "",
        WorkG_Symbol: "",
        WorkG_Mark: "",
        Pl_Object_Grp: "",
        Pl_Object: false,
        Target_Amount: "",
        WorkG_Remark: "",
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const [formData, setFormData] = useState({
    WorkG_CD: "",
    WorkG_Name: "",
    WorkG_Abb: "",
    WorkG_Symbol: "",
    WorkG_Mark: "",
    Pl_Object_Grp: "",
    Pl_Object: false,
    Target_Amount: "",
    WorkG_Remark: "",
  });

  // ฟังก์ชันการจัดการการเปลี่ยนแปลงของฟอร์ม
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSaveWorkG = async (e) => {
    e.preventDefault();

    // ตรวจสอบว่า WorkG_Remark มีคำที่ไม่อนุญาตหรือไม่
    if (
      formData.WorkG_Remark &&
      formData.WorkG_Remark.includes("not allowed")
    ) {
      Swal.fire({
        title: "Error",
        text: "This remark contains an invalid word.",
        icon: "error",
        confirmButtonText: "OK",
      });
      return; // หยุดการทำงานหากพบคำที่ไม่อนุญาต
    }

    try {
      const token = localStorage.getItem("authToken");
      const response = await axios.post(
        `${apiUrl_4000}/workg/create-workg`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Swal.fire({
          title: "Success",
          text: "WorkG created successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });
        fetchWorkG();
        setFormData({
          WorkG_CD: "",
          WorkG_Name: "",
          WorkG_Abb: "",
          WorkG_Symbol: "",
          WorkG_Mark: "",
          Pl_Object_Grp: "",
          Pl_Object: false,
          Target_Amount: "",
          WorkG_Remark: "",
        });

        // Clear the selected radio button by setting selectedRowForCopy to null
        setSelectedRowForCopy(null);

        closeModal();
      }
    } catch (error) {
      console.error("Error creating WorkG:", error);
      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to create WorkG.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleSelectClick = () => {
    document.getElementById("F1").disabled = true;
    document.getElementById("F2").disabled = true;
    document.getElementById("F3").disabled = true;
    document.getElementById("F4").disabled = true;
    document.getElementById("F5").disabled = true;
    document.getElementById("F6").disabled = true;
    document.getElementById("F7").disabled = true;
    document.getElementById("F8").disabled = true;
    document.getElementById("F9").disabled = true;
    document.getElementById("F10").disabled = false;
    document.getElementById("F11").disabled = false;
    document.getElementById("F12").disabled = false;

    setIsButtonDisabled(false);
    handleCheckboxToggle();
  };

  const handleCheckboxChange = (e, workGCd) => {
    if (e.target.checked) {
      setSelectedWorkGs([...selectedWorkGs, workGCd]);
    } else {
      setSelectedWorkGs(selectedWorkGs.filter((cd) => cd !== workGCd));
    }
  };

  const handleDeleteClick = async () => {
    if (selectedWorkGs.length === 0) {
      Swal.fire({
        title: "No Selection",
        text: "Please select at least one item to delete.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    const workGList = selectedWorkGs.join(", ");

    try {
      const confirmResult = await Swal.fire({
        title: "Confirm Delete",
        html: `Are you sure you want to delete the following items?<br>WorkG CDs: <b>${workGList}</b>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        const token = localStorage.getItem("authToken");

        const response = await axios.delete(
          `${apiUrl_4000}/workg/delete-workg`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            data: selectedWorkGs.map((workG) => ({ WorkG_CD: workG })), // ส่งข้อมูล WorkG_CD
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: "Deleted!",
            text: `The selected WorkG CDs have been deleted.`,
            icon: "success",
            confirmButtonText: "OK",
          });

          fetchWorkG(); // รีเฟรชข้อมูล
          setSelectedWorkGs([]); // เคลียร์การเลือก
          document
            .querySelectorAll('input[type="checkbox"]')
            .forEach((checkbox) => {
              checkbox.checked = false; // รีเซ็ต checkbox ทั้งหมด
            });
        } else {
          Swal.fire({
            title: "Error",
            text: response.data.message || "An error occurred while deleting.",
            icon: "error",
            confirmButtonText: "OK",
          });
        }
      }
    } catch (error) {
      console.error("Error in handleDeleteClick:", error);
      Swal.fire({
        title: "Error",
        text: "Something went wrong while deleting.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF12Click = async () => {
    try {
      const confirmResult = await Swal.fire({
        title: "Confirm",
        html: isModified
          ? "The data has been edited. Do you want to go back to master 1?<br>ข้อมูลถูกแก้ไขต้องการกลับไปที่หน้า master 1 หรือไม่?<br>データは編集されました。master 1 に戻りますか？"
          : "Do you want to go back to master 1?<br>คุณต้องการกลับไปที่หน้า master 1 หรือไม่?<br>master 1 に戻りますか？",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });

      if (confirmResult.isConfirmed) {
        setIsModified(false);
        navigate("/master1");
      }
    } catch (error) {
      console.error("Error in handleF12Click:", error);
      Swal.fire({
        title: "Error",
        text: "Try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  useEffect(() => {
    const initialEditedData = data.reduce((acc, row, index) => {
      if (!editedData[index]) {
        acc[index] = { ...row };
      }
      return acc;
    }, {});

    if (Object.keys(initialEditedData).length > 0) {
      setEditedData(initialEditedData);
    }
  }, [data]);

  const handleChange = (e, workgCd, field) => {
    const newValue = e.target.value;

    if (editedDataRef.current[workgCd]?.[field] !== newValue) {
      setIsChanged(true);

      const updatedData = { ...editedDataRef.current };

      updatedData[workgCd] = updatedData[workgCd] || {};
      updatedData[workgCd][field] = newValue;

      setEditedData(updatedData);
      editedDataRef.current = updatedData;
    }
  };

  const handleSave = async (workgCd, field) => {
    const newValue = editedData[workgCd]?.[field];
    const oldValue = data.find((row) => row.WorkG_CD === workgCd)?.[field];

    // ตรวจสอบว่ามีการแก้ไขค่าหรือไม่
    if (newValue === oldValue) {
      Swal.fire({
        icon: "info",
        title: "No changes made!",
        text: "Please make sure to edit something before saving.",
      });
      return;
    }

    // ป้องกันการบันทึกหลายครั้ง
    let isSaving = false;
    if (isSaving) {
      return;
    }

    try {
      isSaving = true;

      const payload = {
        WorkG_CD: workgCd,
        [field]: newValue === "" ? null : newValue,
      };

      const response = await axios.put(
        `${apiUrl_4000}/workg/update-workg`,
        payload
      );

      // อัพเดทข้อมูลใน state
      const updatedData = [...data];
      const rowIndex = updatedData.findIndex((row) => row.WorkG_CD === workgCd);
      if (rowIndex !== -1) {
        updatedData[rowIndex][field] = newValue;
        setData(updatedData);
      }

      Swal.fire({
        icon: "success",
        title: "Edit Successfully!",
        text: "WorkG data has been updated.",
      });
      setIsChanged(false); // รีเซ็ตสถานะการเปลี่ยนแปลง
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Something went wrong!",
        text: "Please try again later.",
      });
      console.error(error);
    } finally {
      isSaving = false;
    }
  };

  const filteredData = data.filter((row) => {
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // ฟังก์ชันสำหรับ Export ข้อมูลเป็น CSV
  const exportToCsv = () => {
    const csvData = data.map((row) => ({
      WorkG_CD: row.WorkG_CD,
      WorkG_Name: row.WorkG_Name,
      WorkG_Abb: row.WorkG_Abb,
      WorkG_Symbol: row.WorkG_Symbol,
      WorkG_Mark: row.WorkG_Mark,
      Pl_Object_Grp: row.Pl_Object_Grp,
      Pl_Object: row.Pl_Object,
      Target_Amount: row.Target_Amount,
      WorkG_Remark: row.WorkG_Remark,
    }));

    const csv = Papa.unparse(csvData); // แปลง JSON เป็น CSV
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

    // ดาวน์โหลดไฟล์ CSV
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "WorkG_data.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCheckboxToggle = () => {
    setShowCheckbox(!showCheckbox);
  };

  const columns = [
    {
      name: "SelectForCopy",
      selector: (row) => (
        <input
          type="radio"
          name="row-radio"
          checked={
            selectedRowForCopy && selectedRowForCopy.WorkG_CD === row.WorkG_CD
          }
          onChange={() => setSelectedRowForCopy(row)}
        />
      ),
      width: "150px",
      omit: false,
    },
    {
      name: "SelectForDelete",
      selector: (row) =>
        showCheckbox ? (
          <input
            type="checkbox"
            className="checkbox"
            onChange={(e) => handleCheckboxChange(e, row.WorkG_CD)}
          />
        ) : null, // ซ่อน checkbox หาก showCheckbox เป็น false
      width: "150px",
      omit: !showCheckbox,
    },
    {
      name: "WorkG_CD",
      selector: (row) => (
        <input
          className="w-full p-2 bg-transparent"
          type="text"
          value={
            editedData[row.WorkG_CD]?.WorkG_CD !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_CD
              : row.WorkG_CD || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_CD")}
          disabled
        />
      ),
      width: "170px",
    },
    {
      name: "WorkG_Name",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "260px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.WorkG_CD]?.WorkG_Name !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_Name
              : row.WorkG_Name || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_Name")}
          disabled={!isF2Pressed}
        />
      ),
      width: "310px",
    },
    {
      name: "WorkG_Abb",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.WorkG_CD]?.WorkG_Abb !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_Abb
              : row.WorkG_Abb || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_Abb")}
          disabled={!isF2Pressed}
        />
      ),
      width: "200px",
    },
    {
      name: "WorkG_Symbol",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.WorkG_CD]?.WorkG_Symbol !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_Symbol
              : row.WorkG_Symbol || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_Symbol")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "WorkG_Mark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.WorkG_CD]?.WorkG_Mark !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_Mark
              : row.WorkG_Mark || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_Mark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "220px",
    },
    {
      name: "Pl_Object_Grp",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          value={
            editedData[row.WorkG_CD]?.Pl_Object_Grp !== undefined
              ? editedData[row.WorkG_CD]?.Pl_Object_Grp
              : row.Pl_Object_Grp || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "Pl_Object_Grp")}
          disabled={!isF2Pressed}
        />
      ),
      width: "190px",
    },
    {
      name: "Pl_Object",
      cell: (row) => (
        <input
          type="checkbox"
          checked={row.Pl_Object}
          style={{ pointerEvents: "none" }}
          onChange={(e) => handleCheckboxChange(e, row, "Pl_Object")}
          className="mx-auto"
        />
      ),
      width: "180px",
    },
    {
      name: "Target_Amount",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="number"
          value={
            editedData[row.WorkG_CD]?.Target_Amount !== undefined
              ? editedData[row.WorkG_CD]?.Target_Amount
              : row.Target_Amount ?? ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "Target_Amount")}
          disabled={!isF2Pressed}
        />
      ),
      width: "200px",
    },
    {
      name: "WorkG_Remark",
      selector: (row) => (
        <input
          className="w-full p-2 rounded-md bg-transparent focus:outline-none focus:ring-0 focus:border-2 focus:border-blue-500"
          type="text"
          style={{
            width: "fit-content",
            minWidth: "240px",
            maxWidth: "100%",
          }}
          value={
            editedData[row.WorkG_CD]?.WorkG_Remark !== undefined
              ? editedData[row.WorkG_CD]?.WorkG_Remark
              : row.WorkG_Remark || ""
          }
          onChange={(e) => handleChange(e, row.WorkG_CD, "WorkG_Remark")}
          disabled={!isF2Pressed}
        />
      ),
      width: "280px",
    },
  ];

  return (
    <div className="flex bg-[#E9EFEC] h-[100vh]">
      <Sidebar />
      <div className="flex flex-col w-full mr-2 ml-2">
        <Navbar />
        <div className="flex-1 flex-col p-2 overflow-x-auto">
          <div className="bg-white grid grid-cols-1">
            <div className="bg-white grid grid-cols-1">
              <h1 className="text-2xl font-bold text-center mt-3">
                WorkG <br /> 部門マスタ
              </h1>
              <hr className="my-6 h-0.5 bg-gray-500 opacity-100 dark:opacity-50 border-y-[1px] border-gray-300" />

              <div className="ml-5 text-lg flex justify-between">
                <input
                  className="border-2 border-gray-500 rounded-md w-52 h-9"
                  type="text"
                  placeholder=" Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={exportToCsv}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md mr-5"
                >
                  Export to CSV
                </button>
              </div>

              <div className="flex justify-center items-center mt-5">
                <div className="w-full text-center px-5">
                  <DataTable
                    columns={columns}
                    data={filteredData}
                    pagination
                    paginationPerPage={10}
                    paginationRowsPerPageOptions={[10, 15, 20, 25, 30]}
                    customStyles={{
                      rows: {
                        style: {
                          "&:nth-of-type(odd)": { backgroundColor: "#ffffff" },
                          "&:nth-of-type(even)": { backgroundColor: "#F5F5F5" },
                          minHeight: "50px",
                          textAlign: "center",
                          justifyContent: "center",
                          borderBottom: "1px solid #ccc",
                          borderRight: "1px solid #ccc",
                        },
                      },
                      headCells: {
                        style: {
                          backgroundColor: "#DCDCDC",
                          fontSize: "14px",
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      cells: {
                        style: {
                          textAlign: "center",
                          justifyContent: "center",
                          border: "1px solid #ccc",
                        },
                      },
                      table: {
                        style: {
                          borderCollapse: "collapse",
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-3 mt-5">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 lg:grid-cols-12 gap-4">
            {/* First button group */}
            <button
              id="F1"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F1)
            </button>
            <button
              id="F2"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                setIsF2Pressed(true); // เปลี่ยนสถานะเมื่อกด F2
              }}
              disabled={isF2Pressed}
            >
              Edit <br />
              編集 (F2)
            </button>
            <button
              id="F3"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={openModal}
              disabled={isF2Pressed}
            >
              New Add <br />
              新規追加 (F3)
            </button>

            {/* Modal */}
            {isModalOpen && (
              <div className="fixed inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50">
                <div
                  className="bg-white p-6 rounded-lg w-1/2 max-h-[80vh] overflow-y-auto"
                  onClick={(e) => e.stopPropagation()}
                >
                  <h2 className="text-xl mb-4">Add New WorkG</h2>
                  <form onSubmit={handleSaveWorkG}>
                    {/* WorkG_CD */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_CD
                      </label>
                      <input
                        type="text"
                        name="WorkG_CD"
                        className="w-full p-2 border rounded-md"
                        value={formData.WorkG_CD}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* WorkG_Name */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_Name
                      </label>
                      <input
                        type="text"
                        name="WorkG_Name"
                        className="w-full p-2 border rounded-md"
                        value={formData.WorkG_Name}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* WorkG_Abb */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_Abb
                      </label>
                      <input
                        type="text"
                        name="WorkG_Abb"
                        className="w-full p-2 border rounded-md"
                        value={formData.WorkG_Abb}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* WorkG_Symbol */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_Symbol
                      </label>
                      <input
                        type="text"
                        name="WorkG_Symbol"
                        className="w-full p-2 border rounded-md"
                        value={formData.WorkG_Symbol}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* WorkG_Mark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_Mark
                      </label>
                      <input
                        type="text"
                        name="WorkG_Mark"
                        className="w-full p-2 border rounded-md"
                        value={formData.WorkG_Mark}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Pl_Object_Grp */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Pl_Object_Grp
                      </label>
                      <input
                        type="text"
                        name="Pl_Object_Grp"
                        className="w-full p-2 border rounded-md"
                        value={formData.Pl_Object_Grp}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Pl_Object */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Pl_Object
                      </label>

                      <input
                        type="checkbox"
                        name="Pl_Object"
                        checked={formData.Pl_Object}
                        onChange={handleInputChange}
                        className="mx-auto"
                      />
                    </div>

                    {/* Target_Amount */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Target_Amount
                      </label>
                      <input
                        type="number"
                        name="Target_Amount"
                        className="w-full p-2 border rounded-md"
                        value={formData.Target_Amount}
                        onChange={handleInputChange}
                        min="0" // รับแค่ตัวเลขที่ไม่เป็นลบ
                        step="any" // รับตัวเลขทศนิยมได้
                      />
                    </div>

                    {/* WorkG_Remark */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        WorkG_Remark
                      </label>
                      <textarea
                        name="WorkG_Remark"
                        className="w-full p-2 border rounded-md h-24"
                        value={formData.WorkG_Remark}
                        onChange={handleInputChange}
                      />
                    </div>

                    {/* Save and Close Buttons */}
                    <div className="mt-4 flex justify-end gap-4">
                      <button
                        type="submit"
                        className="bg-blue-500 p-3 rounded-lg text-white"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={closeModal}
                        className="bg-gray-500 p-3 rounded-lg text-white"
                      >
                        Close
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            <button
              id="F4"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleSelectClick}
              disabled={isF2Pressed}
            >
              Select <br />
              選択 (F4)
            </button>

            <button
              id="F5"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F5)
            </button>
            <button
              id="F6"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F6)
            </button>
            <button
              id="F7"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F7)
            </button>
            <button
              id="F8"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              disabled
            >
              (F8)
            </button>
            <button
              id="F9"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                data.forEach((row) => {
                  Object.keys(row).forEach((field) => {
                    if (editedData[row.WorkG_CD]?.[field] !== undefined) {
                      handleSave(row.WorkG_CD, field);
                    }
                  });
                });
              }}
              disabled={!isF2Pressed} // จะเปิดใช้งาน F9 เมื่อกด F2 แล้ว
            >
              Save <br />
              保存 (F9)
            </button>
            <button
              id="F10"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={handleDeleteClick}
              disabled={isButtonDisabled} // ตั้งค่าปุ่มให้ disabled ตามสถานะ
            >
              Delete <br />
              消去 (F10)
            </button>
            <button
              id="F11"
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
              onClick={() => {
                window.location.reload(); // รีเฟรชหน้า
              }}
              disabled={false} // ตั้งค่า disabled เป็น false ถ้าคุณต้องการให้ปุ่มทำงาน
            >
              Clear <br />
              クリア (F11)
            </button>

            <button
              id="F12"
              onClick={handleF12Click}
              className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
            >
              Back <br />
              戻る (F12)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
