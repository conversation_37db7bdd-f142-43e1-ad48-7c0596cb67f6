import { useState, createContext, useEffect } from "react";
import axios from "../configs/axios";

export const ResultContext = createContext();

export default function ResultContextProvider({ children }) {
  const [ResultData, setResultData] = useState(null);
  const [autoFinish, setAutoFinish] = useState(false);

  const SearchResultData = async (orderNo, partsNO) => {
    try {
      const response = await axios.post("/result/result-plan", {
        Order_No: orderNo,
        Parts_No: partsNO,
      });

      if (response.data && response.data.data && response.data.data.result) {
        const result = response.data.data.result;
        setResultData(result); 
        return result;
      } else {
        return null;
      }
    } catch (error) {
      console.error("Error fetching Result data:", error);
      return null;
    }
  };

  const fetchSet = async () => {
    try {
      const response = await axios.get("/set/fetch-set");

      // ค้นหา Rs_Auto_Finish จากข้อมูลทั้งหมดในชุด response
      const autoFinishValue =
        response.data?.data?.set?.find((item) => item.ID === 1)
          ?.Rs_Auto_Finish ?? true;

      setAutoFinish(autoFinishValue);
    } catch (error) {
      console.error("Error fetching set data:", error);
      setAutoFinish(-1); // กรณี Error ให้ค่าเป็น -1
    }
  };

  const Update_Result = async (requestData, orderNo, partsNo) => {
    try {
      // เพิ่ม Order_No และ Parts_No ใน requestData
      const updatedRequestData = {
        ...requestData,
        Order_No: orderNo,
        Parts_No: partsNo,
      };

      // ส่งข้อมูลที่อัพเดทแล้วไปยัง API
      const response = await axios.post(
        "/result/result-update",
        updatedRequestData
      );
      console.log("Result updated successfully:", response);
      return response.data;
    } catch (error) {
      console.error(
        "Error updating result:",
        error.response?.data || error.message
      );
      throw new Error("Failed to update result");
    }
  };

useEffect(() => {
  const fetchAll = async () => {
    try {
      await fetchSet();
    } catch (error) {
      console.error("เกิดข้อผิดพลาดในการโหลดข้อมูล:", error);
    }
  };

  fetchAll();
}, []);

  return (
    <ResultContext.Provider
      value={{
        ResultData,
        setResultData,
        SearchResultData,
        autoFinish,
        Update_Result,
      }}
    >
      {children}
    </ResultContext.Provider>
  );
}
