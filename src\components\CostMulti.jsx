import React, { useState, useEffect, useRef } from "react";
import Swal from "sweetalert2";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Select from "react-select";

// Import Context
import { useCost } from "../hooks/use-cost";
import { usePlan } from "../hooks/use-plan";
import { useOrderV2 } from "../hooks/use-order";
import { useResult } from "../hooks/use-result";
// Icons
import { AiTwotoneCalendar } from "react-icons/ai";

export default function CostMulti() {
  const currentDate = new Date().toISOString().split("T")[0];
  const [buttonState, setButtonState] = useState({
    F1: true,
    F2: true,
    F3: true,
    F4: true,
    F5: true,
    F6: true,
    F7: true,
    F8: true,
    F9: false,
    F10: true,
    F11: false,
    F12: false,
  });

  const {
    SearchCostData,
    CostData,
    setCostData,
    Act_ProcessData,
    cost_process,
    searchPartData,
    Act_PartData,
    ResourceData,
    CsProgressData,
    ProcessCData,
    Search_Parts_No_AfterUpdate,
    Plan_MultiData,
    Order_MultiData,
    Cs_Progress_CD_AfterUpdate2,
    UpdateCost2,
    UpdateCostConfirms2,
    PlanppcData,
  } = useCost();
  const { WorkerData } = useOrderV2();
  const { StatusData } = usePlan();
  const { searchOrderData } = useOrderV2();
  const { ResultData, SearchResultData } = useResult();
  const [costStatus, setCostStatus] = useState({});
  const partRefs = useRef([]);
  const processRefs = useRef([]);
  const cmcRefs = useRef([]);
  const cpcRefs = useRef([]);
  const orderRefs = useRef([]);
  const [openSelectIndex, setOpenSelectIndex] = useState({
    part: null,
    process: null,
    cmc: null,
    cpc: null,
  });

  const handleOrderKeyDown = (e, index) => {
    if (e.key === "Enter") {
      setOpenSelectIndex({ part: index, process: null });
      setTimeout(() => {
        partRefs.current[index]?.focus();
      }, 100);
    }
  };

  const handlePartKeyDown = (e, index) => {
    if (e.key === "Enter") {
      setOpenSelectIndex({ part: null, process: index });
      setTimeout(() => {
        processRefs.current[index]?.focus();
      }, 100);
    }
  };

  const handleProcessKeyDown = (e, index) => {
    if (e.key === "Enter") {
      updateProcessNameskey(index);
      handleAutoIncrementOrder(index + 1);
      // ไปโฟกัสที่ช่อง Order No แถวเดียวกัน
      setTimeout(() => {
        orderRefs.current[index + 1]?.focus();
      }, 100);
    }
  };

  const handleAutoIncrementOrder = async (index) => {
    const baseOrderNo = CostData?.[`Act_Order_No${index}`];
    if (!baseOrderNo) return;

    const match = baseOrderNo.match(/^([A-Za-z]+)(\d+)$/);
    if (!match) {
      console.warn("Invalid base order number:", baseOrderNo);
      return;
    }

    const prefix = match[1];
    let orderNum = parseInt(match[2], 10);
    orderNum += 1;
    const nextOrder = prefix + String(orderNum).padStart(match[2].length, "0");

    setCostData((prev) => ({
      ...prev,
      [`Act_Order_No${index + 1}`]: nextOrder,
    }));



    searchPartData({ [`Act_Order_No${index + 1}`]: nextOrder });
  };

  const handleF9Click = async () => {
    try {
      const confirms = await Swal.fire({
        title: "Confirms",
        text: "Do you save data ?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
      });
      if (!confirms.isConfirmed) return;

      for (let i = 0; i < 10; i++) {
        const row = i + 1;
        const check = CostData?.[`Act_Order_No${row}`];
        if (!check) continue;

        const result = await UpdateCost2(
          CostData,
          row,
          StatusData,
          Plan_MultiData,
          Order_MultiData,
          ""
        );

        switch (result.status) {
          case "warning":
            await Swal.fire({
              title: "Warning",
              text: result.message,
              icon: "warning",
              confirmButtonText: "OK",
            });
            setCostStatus((prev) => ({
              ...prev,
              [row]: `Warning: ${result.message}`,
            }));
            return;

          case "question":
            const confirm = await Swal.fire({
              title: "Question",
              text: result.message,
              icon: "question",
              showCancelButton: true,
              confirmButtonText: "Yes",
              cancelButtonText: "Cancel",
            });
            setCostStatus((prev) => ({
              ...prev,
              [row]: `Warning: ${result.message}`,
            }));
            if (confirm.isConfirmed) {
              await UpdateCostConfirms2(
                CostData,
                row,
                StatusData,
                Plan_MultiData,
                Order_MultiData,
                ""
              );
            }
            return;

          case "error":
            await Swal.fire({
              title: result.errorCode,
              text: `${result.message}\n${result.details}`,
              icon: "error",
              confirmButtonText: "OK",
            });
            setCostStatus((prev) => ({
              ...prev,
              [row]: `Warning: ${result.message}`,
            }));
            return;

          default:
            break;
        }

        setCostStatus((prev) => ({
          ...prev,
          [row]: `Success`,
        }));
      }

      await Swal.fire({
        title: "Success!",
        text: "Cost updated successfully.",
        icon: "success",
        confirmButtonText: "OK",
      });
    } catch (error) {
      console.error("Error in handleF9Click:", error);
      Swal.fire({
        title: "An error occurred.",
        text: "Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleF11Click = async () => {
    const result = await Swal.fire({
      title: "Search other Order_No ?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancle",
    });
    if (result.isConfirmed) {
      setCostData((prevData) => {
        const updatedData = { ...prevData };

        for (let index = 0; index < 10; index++) {
          updatedData[`Act_Order_No${index + 1}`] = "";
          updatedData[`Act_Parts_No${index + 1}`] = "";
          updatedData[`Act_Process_No${index + 1}`] = "";
          updatedData[`Act_Weight${index + 1}`] = "";
          updatedData[`Act_CMC${index + 1}`] = "";
          updatedData[`Act_CMT${index + 1}`] = "";
          updatedData[`Act_CPC${index + 1}`] = "";
          updatedData[`Act_CPT${index + 1}`] = "";
          updatedData[`Act_CPD${index + 1}`] = "";
          updatedData[`Act_CPN${index + 1}`] = "";
          updatedData[`Act_Cs_Remark${index + 1}`] = "";
          updatedData[`Act_Cs_Progress_CD${index + 1}`] = "";
          updatedData[`Act_Cs_Progress_Abb${index + 1}`] = "";
          updatedData[`Act_Cs_Complete_Date${index + 1}`] = "";
          updatedData[`Act_Cs_Complete_Qty${index + 1}`] = "";
          updatedData[`Act_Outside${index + 1}`] = "";
          updatedData[`Act_Cs_Final_Complete${index + 1}`] = "";
        }

        return updatedData;
      });

      setProcessNames("");
      setCostStatus("");
    }
  };

  const handleF12Click = async () => {
    const result = await Swal.fire({
      title: "Exit cost multi?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      setCostData({});
      setProcessNames("");
      window.close(); // ปิดหน้าต่างเบราว์เซอร์หรือโปรแกรม
    }
  };

  const handleCostInputChange = async (eventOrId, value = null) => {
    let id, newValue;

    if (typeof eventOrId === "object" && eventOrId.target) {
      const { target } = eventOrId;
      id = target.id;
      newValue =
        target.type === "checkbox"
          ? target.checked
          : target.value === ""
          ? null
          : target.value;
    } else {
      id = eventOrId;
      newValue = value;
    }

    if (id.startsWith("Act_CPD")) {
      newValue = newValue
        ? new Date(newValue).toLocaleDateString("en-CA")
        : null;
    }

    if (id.startsWith("Act_Cs_Complete_Date")) {
      newValue = newValue
        ? new Date(newValue).toLocaleDateString("en-CA")
        : null;
    }

    if (id.startsWith("Input_CPD")) {
      newValue = newValue
        ? new Date(newValue).toLocaleDateString("en-CA")
        : null;
    }

    if (
      id.startsWith("Act_Order_No") &&
      (newValue === null || newValue === "")
    ) {
      const match = id.match(/^Act_Order_No(\d+)$/);
      if (match) {
        const index = match[1];
        setCostData((prevData) => ({
          ...prevData,
          [`Act_Order_No${index}`]: "",
          [`Act_Parts_No${index}`]: "",
          [`Act_Process_No${index}`]: "",
          [`Act_Weight${index}`]: "",
          [`Act_CMC${index}`]: "",
          [`Act_CMT${index}`]: "",
          [`Act_CPC${index}`]: "",
          [`Act_CPT${index}`]: "",
          [`Act_CPD${index}`]: "",
          [`Act_CPN${index}`]: "",
          [`Act_Cs_Remark${index}`]: "",
          [`Act_Cs_Progress_CD${index}`]: "",
          [`Act_Cs_Progress_Abb${index}`]: "",
          [`Act_Cs_Complete_Date${index}`]: "",
          [`Act_Cs_Complete_Qty${index}`]: "",
          [`Act_Outside${index}`]: "",
          [`Act_Cs_Final_Complete${index}`]: "",
          // เพิ่มฟิลด์อื่น ๆ ที่อยาก reset ได้ที่นี่
        }));
        setProcessNames((prevState) => ({
          ...prevState,
          [`ProcessNamesForRow${index}`]: "",
        }));
      }
      return; // ออกจากฟังก์ชันทันทีเพื่อไม่ต้องเรียก search
    }

    setCostData((prevCostData) => {
      const updatedData = {
        ...prevCostData,
        [id]: newValue,
      };

      if (id.startsWith("Act_Order_No")) {
        searchOrderData(newValue);
        searchPartData({ [id]: newValue });
      }

      return updatedData;
    });
  };

  const [processNames, setProcessNames] = useState({});

  const handleProcessInputChange = (e, index) => {
    const updatedValue = e.target.value;

    setProcessNames((prevState) => ({
      ...prevState,
      [`ProcessNamesForRow${index + 1}`]: updatedValue,
    }));
  };

  const updateProcessNames = (index) => {
    const processKey =
      Plan_MultiData?.[`PPC${CostData?.[`Act_Process_No${index + 1}`]}`];

    const updatedProcessNames = (ProcessCData || [])
      .filter(
        (Process) => Process.Process_CD && Process.Process_CD === processKey
      )
      .map((Process) => Process.Process_Abb);

    setProcessNames((prevData) => ({
      ...prevData,
      [`ProcessNamesForRow${index + 1}`]:
        updatedProcessNames.length > 0 ? updatedProcessNames : ["None"],
    }));
  };

  const updateProcessNameskey = async (index) => {
    let FG = 0;
    let KN = 0;

    const result = await SearchResultData(
      CostData[`Act_Order_No${index + 1}`],
      CostData[`Act_Parts_No${index + 1}`]
    );

    if (result) {
      while (FG < 1) {
        KN += 1;

        if (KN !== 36) {
          if (!result["RPD" + KN] || result["RPD" + KN] === "") {
            FG = 1;
          }
        } else {
          FG = 1;
        }
      }
      console.log("Next Process No:", KN);
    }

    const currentProcessNo = String(KN || "");

    setCostData((prevData) => ({
      ...prevData,
      [`Act_Process_No${index + 1}`]: currentProcessNo,
    }));
  };

  const Resource =
    Array.isArray(ResourceData) && ResourceData.length > 0
      ? ResourceData.map((item) => ({
          value: item.Resource_CD,
          label: `${item.Resource_CD}  |  ${item.Resource_Symbol}`,
        }))
      : [{ value: "", label: "No information" }];

  const Worker =
    Array.isArray(WorkerData) && WorkerData.length > 0
      ? WorkerData.map((item) => ({
          value: item.Worker_CD,
          label: `${item.Worker_CD}  |  ${item.Worker_Abb}`,
        }))
      : [{ value: "", label: "No information" }];

  const CsProgress =
    Array.isArray(CsProgressData) && CsProgressData.length > 0
      ? CsProgressData.map((item) => ({
          value: item.Cs_Progress_CD,
          label: `${item.Cs_Progress_CD}`,
        }))
      : [{ value: "", label: "No information" }];

  const resourceKey = CostData?.[`Input_CMC`];
  const Input_Resource_Name = (ResourceData || [])
    .filter((Resource) => Resource.Resource_CD === resourceKey)
    .map((Resource) => Resource.Resource_Symbol);

  const WorkerKey = CostData?.[`Input_CPC`];
  const Input_Worker_Name = (WorkerData || [])
    .filter((Worker) => Worker.Worker_CD === WorkerKey)
    .map((Worker) => Worker.Worker_Abb);

  const ProgressKey = CostData?.[`Input_Cs_Progress_CD`];
  const Input_Cs_Progress_Abb1 = (CsProgressData || [])
    .filter((Worker) => Worker.Cs_Progress_CD === ProgressKey)
    .map((Worker) => Worker.Cs_Progress_Abb);

  return (
    <div className="flex bg-[#C0C0C0] h-[100vh]">
      <div className="flex bg-[#C0C0C0] flex-col w-screen mr-2 ml-2">
        <div className="flex-1  flex-col overflow-x-auto flex-grow p-2 ">
          <div className="grid grid-cols-1">
            <div className="relative flex items-center py-3 bg-[#00FFFF] px-4 rounded-md mb-4">
              {/* Calc Complete อยู่ตรงกลางของ div */}
              <div className="absolute left-1/2 transform -translate-x-1/2">
                <label className="text-xl font-bold">Cost Multi Input</label>
              </div>

              {/* Date อยู่ทางขวา */}
              <div className="ml-auto flex items-center">
                <label className="font-medium text-xs mr-2">Date</label>
                <input
                  type="date"
                  className="bg-white border border-gray-500 rounded-md px-2 py-0.5 w-[200px]"
                  name="txtProcessing_Date"
                  value={currentDate}
                  readOnly
                />
              </div>
            </div>
            {/* 【Input_Info】 */}
            <div className="grid grid-cols-5 gap-4">
              <div className="p-4 text-center">
                <p>【Input_Info】</p>
              </div>
              <div className="flex items-center p-4 gap-2">
                <input
                  id="Continu_Input"
                  checked={
                    CostData?.Continu_Input !== undefined
                      ? CostData?.Continu_Input
                      : true
                  }
                  onChange={handleCostInputChange}
                  type="checkbox"
                  className="w-5 h-5"
                />
                <label className="mr-2 text-md font-medium">
                  Continuous_Input
                </label>
              </div>
              <div className="flex items-center p-4 gap-2">
                <input
                  id="Procee_List_View"
                  checked={
                    CostData?.Procee_List_View !== undefined
                      ? CostData?.Procee_List_View
                      : true
                  }
                  onChange={handleCostInputChange}
                  type="checkbox"
                  className="w-5 h-5"
                />
                <label className="mr-2 text-md font-medium">
                  Procee_List_View
                </label>
              </div>
              <div className="flex items-center p-4 gap-2">
                <input
                  id="Auto_Year_Change"
                  checked={
                    CostData?.Auto_Year_Change !== undefined
                      ? CostData?.Auto_Year_Change
                      : true
                  }
                  onChange={handleCostInputChange}
                  type="checkbox"
                  className="w-5 h-5"
                />
                <label className="mr-2 text-md font-medium">
                  Auto_Year_Change
                </label>
              </div>
              <div className="flex items-center p-4 gap-2">
                <input
                  id="Tab_Stop"
                  checked={
                    CostData?.Tab_Stop !== undefined
                      ? CostData?.Tab_Stop
                      : CostData?.Tab_Stop === false
                  }
                  onChange={handleCostInputChange}
                  type="checkbox"
                  className="w-5 h-5"
                />
                <label className="mr-2 text-md font-medium">Tab_Stop</label>
              </div>
            </div>

            {/* 【Input_Info】ส่วนล่าง */}
            <div className="flex gap-4 justify-center items-center mb-4">
              <div className="flex items-center">
                <label className="mr-2 text-md font-medium gap-2 ">
                  Machine
                </label>
                <Select
                  id="Input_CMC"
                  value={{
                    label: CostData?.Input_CMC || "",
                    value: CostData?.Input_CMC || "",
                  }}
                  options={Resource}
                  className="text-xs w-28"
                  onChange={(selectedOption) => {
                    setCostData((prev) => ({
                      ...prev,
                      [`Input_CMC`]: selectedOption?.value || "",
                    }));
                  }}
                  styles={{
                    control: (base) => ({
                      ...base,
                      backgroundColor: "#CC99FF",
                    }),
                    menu: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                    option: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                  }}
                />
                <input
                  id="Input_Resource_Name"
                  value={Input_Resource_Name || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28 h-9"
                />
              </div>

              <div className="flex items-center">
                <label className="mr-2 text-md font-medium gap-2 ">
                  Person
                </label>
                <Select
                  id="Input_CPC"
                  value={{
                    label: CostData?.Input_CPC || "",
                    value: CostData?.Input_CPC || "",
                  }}
                  options={Worker}
                  className="text-xs w-28"
                  onChange={(selectedOption) => {
                    setCostData((prev) => ({
                      ...prev,
                      [`Input_CPC`]: selectedOption?.value || "",
                    }));
                  }}
                  styles={{
                    control: (base) => ({
                      ...base,
                      backgroundColor: "#CC99FF",
                    }),
                    menu: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                    option: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                  }}
                />
                <input
                  id="Input_Worker_Name"
                  value={Input_Worker_Name || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28 h-9"
                />
              </div>

              <div className="flex items-center">
                <label className="mr-2 text-md font-medium gap-2 ">Time</label>
                <input
                  id="Input_CPT"
                  value={CostData?.Input_CPT || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="border-2  bg-[#FFFF99] border-gray-500 rounded-md px-2 py-1 text-xs w-16 h-9"
                />
              </div>

              <div className="flex items-center">
                <label className="mr-2 text-md font-medium gap-2 ">Date</label>
                <DatePicker
                  id="Input_CPD"
                  selected={
                    CostData && CostData.Input_CPD
                      ? new Date(CostData.Input_CPD)
                      : new Date()
                  }
                  onChange={(date) => {
                    // เมื่อเลือกวันที่ใหม่หรือเคลียร์ค่า
                    handleCostInputChange(
                      `Input_CPD`,
                      date ? date.toISOString().split("T")[0] : null // ถ้าเลือกวันที่จะเก็บค่า, ถ้าเคลียร์จะเก็บเป็น null
                    );
                  }}
                  dateFormat="dd/MM/yyyy"
                  type="date"
                  className="border-2 bg-[#CC99FF] border-gray-500 rounded-md px-2 py-1 text-xs w-32 h-9"
                />
              </div>

              <div className="flex items-center">
                <label className="mr-2 text-md font-medium gap-2 ">
                  Cs_Progress
                </label>
                <Select
                  id="Input_Cs_Progress_CD"
                  value={{
                    label: CostData?.Input_Cs_Progress_CD || "",
                    value: CostData?.Input_Cs_Progress_CD || "",
                  }}
                  options={CsProgress}
                  className="text-xs w-16"
                  onChange={(selectedOption) => {
                    setCostData((prev) => ({
                      ...prev,
                      [`Input_Cs_Progress_CD`]: selectedOption?.value || "",
                    }));
                  }}
                  styles={{
                    control: (base) => ({
                      ...base,
                      backgroundColor: "#CC99FF",
                    }),
                    menu: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                    option: (base) => ({
                      ...base,
                      fontSize: "12px",
                    }),
                  }}
                />
                <input
                  id="Input_Cs_Progress_Abb1"
                  value={Input_Cs_Progress_Abb1 || ""}
                  onChange={handleCostInputChange}
                  type="text"
                  className="border-2 border-gray-500 rounded-md px-2 py-1 text-xs w-28 h-9"
                />
              </div>
            </div>
          </div>
          {/* 【】ส่วน Body */}
          <div>
            <table className="w-full table-fixed border border-black border-collapse bg-transparent">
              <thead>
                <tr className="border border-black text-left text-sm bg-transparent">
                  <th className="p-2  w-24">Order_No/Parts_No</th>
                  <th className="p-2 border-y border-r border-black w-16"></th>
                  <th className="p-2 w-16"></th>
                  <th className="p-2 w-24">Process</th>
                  <th className="p-2 border-y border-l border-black w-24"></th>
                  <th className="p-2 border-t border-b border-black w-24">
                    Machine/Time
                  </th>
                  <th className="p-2 border-y border-r border-black w-12"></th>
                  <th className="p-2 border-y border-l border-black w-24"></th>
                  <th className="p-2 border-t border-b border-black w-24">
                    Person(CPC)/Time
                  </th>
                  <th className="p-2 border-y border-r border-black w-12"></th>
                  <th className="p-2 border-y border-l border-black w-24">
                    Process_Date/Qty
                  </th>
                  <th className="p-2 border-y border-r border-black w-12"></th>
                  <th className="p-2 border border-black w-36">Cs_Remark</th>
                  <th className="p-2 border border-black w-24">Result</th>
                </tr>
              </thead>
              <tbody>
                {[...Array(10)].map((_, index) => {
                  useEffect(() => {
                    const processNo = CostData?.[`Act_Process_No${index + 1}`];
                    if (processNo) {
                      updateProcessNames(index);
                    }
                  }, [CostData?.[`Act_Process_No${index + 1}`]]);

                  const ProcessNamesForRow =
                    processNames[`ProcessNamesForRow${index + 1}`] || [];

                  const resourceKey = CostData?.[`Act_CMC${index + 1}`];
                  const ResourceForRow = (ResourceData || [])
                    .filter((Resource) => Resource.Resource_CD === resourceKey)
                    .map((Resource) => Resource.Resource_Symbol);

                  const WorkerKey = CostData?.[`Act_CPC${index + 1}`];
                  const WorkerForRow = (WorkerData || [])
                    .filter((Worker) => Worker.Worker_CD === WorkerKey)
                    .map((Worker) => Worker.Worker_Abb);

                  const ProgressKey =
                    CostData?.[`Act_Cs_Progress_CD${index + 1}`];
                  const ProgressForRow = (CsProgressData || [])
                    .filter((Worker) => Worker.Cs_Progress_CD === ProgressKey)
                    .map((Worker) => Worker.Cs_Progress_Abb);

                  useEffect(() => {
                    if (CostData?.[`Act_Parts_No${index + 1}`]) {
                      const orderIndex = index + 1;
                      const actOrderNo =
                        CostData[`Act_Order_No${orderIndex}`] || "";
                      const actPartsNo =
                        CostData[`Act_Parts_No${orderIndex}`] || "";

                      cost_process(actOrderNo, actPartsNo, orderIndex);
                      Search_Parts_No_AfterUpdate(
                        actOrderNo,
                        actPartsNo,
                        orderIndex
                      );
                    }
                  }, [index, CostData?.[`Act_Parts_No${index + 1}`]]);

                  const planDatas = Plan_MultiData?.[`plans${index + 1}`];
                  useEffect(() => {
                    if (planDatas && Object.keys(planDatas).length > 0) {
                      const Pt_Qty = planDatas?.Pt_Qty ?? 0;
                      const Pt_Spare_Qty = planDatas?.Pt_Spare_Qty ?? 0;
                      const Pt_NG_Qty = planDatas?.Pt_NG_Qty ?? 0;
                      const calculatedValue = Pt_Qty + Pt_Spare_Qty - Pt_NG_Qty;
                      setCostData((prev) => {
                        const orderIndex = index + 1;
                        return {
                          ...prev,
                          [`Act_CPN${orderIndex}`]: calculatedValue || 0,
                        };
                      });
                    }
                  }, [planDatas, index]);

                  useEffect(() => {
                    if (CostData?.[`Act_Cs_Progress_CD${index + 1}`]) {
                      Cs_Progress_CD_AfterUpdate2(CostData, index + 1);
                    }
                  }, [
                    CostData?.[`Act_Cs_Progress_CD${index + 1}`],
                    CostData?.[`Act_Process_No${index + 1}`],
                  ]);

                  return (
                    <React.Fragment key={index}>
                      <tr className="border border-black bg-transparent">
                        <td className="p-2  bg-[#CCFFFF] ">
                          <input
                            id={`Act_Order_No${index + 1}`}
                            name={`Act_Order_No${index + 1}`}
                            ref={(el) => (orderRefs.current[index] = el)} // 👈 สำคัญ!
                            value={CostData?.[`Act_Order_No${index + 1}`] || ""}
                            onChange={handleCostInputChange}
                            onKeyDown={(e) => handleOrderKeyDown(e, index)}
                            onDoubleClick={() =>
                              handleAutoIncrementOrder(index)
                            }
                            className="w-full bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td className="p-2 border-y border-r border-black bg-[#CCFFFF] ">
                          <Select
                            id={`Act_Parts_No${index + 1}`}
                            ref={(el) => (partRefs.current[index] = el)}
                            menuIsOpen={
                              openSelectIndex.part === index ? true : undefined
                            }
                            onMenuClose={() =>
                              setOpenSelectIndex((prev) => ({
                                ...prev,
                                part: null,
                              }))
                            }
                            onKeyDown={(e) => handlePartKeyDown(e, index)}
                            value={{
                              label:
                                CostData?.[`Act_Parts_No${index + 1}`] || "",
                              value:
                                CostData?.[`Act_Parts_No${index + 1}`] || "",
                            }}
                            className="text-xs w-full"
                            options={
                              Act_PartData?.[`Parts_No${index + 1}`]?.length > 0
                                ? Act_PartData[`Parts_No${index + 1}`].map(
                                    (value) => ({
                                      label: value,
                                      value: value,
                                    })
                                  )
                                : [{ label: "No data available", value: "" }]
                            }
                            onChange={(selectedOption) => {
                              const selectedValue = selectedOption?.value;
                              if (!selectedValue) return;

                              const id = `Act_Parts_No${index + 1}`;

                              setCostData((prev) => {
                                const orderIndex = index + 1;
                                return {
                                  ...prev,
                                  [id]: selectedValue,
                                  [`Act_CMC${orderIndex}`]:
                                    prev?.Input_CMC || "",
                                  [`Act_CMT${orderIndex}`]: "0",
                                  [`Act_CPC${orderIndex}`]:
                                    prev?.Input_CPC || "",
                                  [`Act_CPT${orderIndex}`]:
                                    prev?.Input_CPT || "",
                                  [`Act_CPD${orderIndex}`]:
                                    prev?.Input_CPD ||
                                    new Date().toISOString().split("T")[0],
                                  [`Act_Cs_Progress_CD${orderIndex}`]:
                                    prev?.Input_Cs_Progress_CD || "",
                                };
                              });
                            }}
                            styles={{
                              control: (base) => ({
                                ...base,
                                backgroundColor: "transparent",
                                minHeight: "24px",
                                padding: "0px 4px",
                                fontSize: "16px",
                                border: "none",
                                boxShadow: "none",
                                "&:hover": {
                                  borderColor: "transparent",
                                },
                              }),
                              menu: (base) => ({
                                ...base,
                                fontSize: "14px",
                                width: "200px",
                              }),
                              option: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                            }}
                          />
                        </td>
                        <td className="p-2 border-y border-l border-black bg-[#FFFF99]">
                          <div className="flex items-center">
                            <Select
                              id={`Act_Process_No${index + 1}`}
                              ref={(el) => (processRefs.current[index] = el)}
                              menuIsOpen={
                                openSelectIndex.process === index
                                  ? true
                                  : undefined
                              }
                              onMenuClose={() =>
                                setOpenSelectIndex((prev) => ({
                                  ...prev,
                                  process: null,
                                }))
                              }
                              onKeyDown={(e) => handleProcessKeyDown(e, index)}
                              value={{
                                label:
                                  CostData?.[`Act_Process_No${index + 1}`] ||
                                  "",
                                value:
                                  CostData?.[`Act_Process_No${index + 1}`] ||
                                  "",
                              }}
                              options={
                                Act_ProcessData?.[`Cost${index + 1}`]?.length >
                                0
                                  ? Act_ProcessData[`Cost${index + 1}`].map(
                                      (value) => ({
                                        label: (
                                          <div>
                                            <strong>No:</strong> {value.No}
                                            <br />
                                            <strong>Pr_Abb:</strong>{" "}
                                            {value.Pr_Abb || "-"}
                                            <br />
                                          </div>
                                        ),
                                        value: value.No,
                                      })
                                    )
                                  : [{ label: "No data available", value: "" }]
                              }
                              onChange={(selectedOption) => {
                                const selectedNo = selectedOption?.value;
                                const selectedData = Act_ProcessData?.[
                                  `Cost${index + 1}`
                                ]?.find((p) => p.No === selectedNo);

                                setCostData((prev) => ({
                                  ...prev,
                                  [`Act_Process_No${index + 1}`]:
                                    selectedNo || "",
                                }));

                                setProcessNames((prev) => ({
                                  ...prev,
                                  [`ProcessNamesForRow${index + 1}`]:
                                    selectedData?.Pr_Abb
                                      ? [selectedData.Pr_Abb]
                                      : ["None"],
                                }));
                              }}
                              className="text-xs w-full"
                              styles={{
                                control: (base) => ({
                                  ...base,
                                  backgroundColor: "transparent",
                                  minHeight: "24px",
                                  padding: "0px 4px",
                                  fontSize: "16px",
                                  border: "none",
                                  boxShadow: "none",
                                  "&:hover": {
                                    borderColor: "transparent",
                                  },
                                }),
                                menu: (base) => ({
                                  ...base,
                                  fontSize: "14px",
                                  width: "200px",
                                }),
                                option: (base) => ({
                                  ...base,
                                  fontSize: "14px",
                                }),
                              }}
                            />
                          </div>
                        </td>
                        <td className="p-2 border-y border-r border-black bg-[#FFFFFF]">
                          <input
                            disabled
                            id={`ProcessNamesForRow${index + 1}`}
                            value={ProcessNamesForRow.join(", ")}
                            onChange={handleProcessInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td className="p-2 border-y border-l border-black bg-[#CC99FF] ">
                          <Select
                            id={`Act_CMC${index + 1}`}
                            ref={(el) => (cmcRefs.current[index] = el)}
                            menuIsOpen={
                              openSelectIndex.cmc === index ? true : undefined
                            }
                            onMenuClose={() =>
                              setOpenSelectIndex((prev) => ({
                                ...prev,
                                cmc: null,
                              }))
                            }
                            value={{
                              label: CostData?.[`Act_CMC${index + 1}`] || "",
                              value: CostData?.[`Act_CMC${index + 1}`] || "",
                            }}
                            options={Resource}
                            className="text-xs w-full"
                            onChange={(selectedOption) => {
                              setCostData((prev) => ({
                                ...prev,
                                [`Act_CMC${index + 1}`]:
                                  selectedOption?.value || "",
                              }));
                            }}
                            styles={{
                              control: (base) => ({
                                ...base,
                                backgroundColor: "transparent", // ตั้งค่าพื้นหลังโปร่ง
                                minHeight: "24px",
                                padding: "0px 4px",
                                fontSize: "16px",
                                border: "none", // ลบเส้นขอบ
                                boxShadow: "none",
                                "&:hover": {
                                  borderColor: "transparent", // ลบเส้นขอบตอน hover
                                },
                              }),
                              menu: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                              option: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                            }}
                          />
                        </td>
                        <td className="p-2 border-t border-b border-black bg-[#FFFFFF]">
                          <input
                            disabled
                            id={`Act_Resource_Name${index + 1}`}
                            value={ResourceForRow.join(", ")}
                            onChange={handleCostInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td className="p-2 border-y border-r border-black">
                          {" "}
                          <input
                            id={`Act_CMT${index + 1}`}
                            value={CostData?.[`Act_CMT${index + 1}`] || ""}
                            onChange={handleCostInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />{" "}
                        </td>
                        <td className="p-2 border-y border-l border-black bg-[#CC99FF] ">
                          <Select
                            id={`Act_CPC${index + 1}`}
                            ref={(el) => (cpcRefs.current[index] = el)}
                            menuIsOpen={
                              openSelectIndex.cpc === index ? true : undefined
                            }
                            onMenuClose={() =>
                              setOpenSelectIndex((prev) => ({
                                ...prev,
                                cpc: null,
                              }))
                            }
                            value={{
                              label: CostData?.[`Act_CPC${index + 1}`] || "",
                              value: CostData?.[`Act_CPC${index + 1}`] || "",
                            }}
                            options={Worker}
                            className="text-xs w-full"
                            onChange={(selectedOption) => {
                              setCostData((prev) => ({
                                ...prev,
                                [`Act_CPC${index + 1}`]:
                                  selectedOption?.value || "",
                              }));
                            }}
                            styles={{
                              control: (base) => ({
                                ...base,
                                backgroundColor: "transparent", // ตั้งค่าพื้นหลังโปร่ง
                                minHeight: "24px",
                                padding: "0px 4px",
                                fontSize: "16px",
                                border: "none", // ลบเส้นขอบ
                                boxShadow: "none",
                                "&:hover": {
                                  borderColor: "transparent", // ลบเส้นขอบตอน hover
                                },
                              }),
                              menu: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                              option: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                            }}
                          />
                        </td>
                        <td className="p-2 border-t border-b border-black bg-[#FFFFFF]">
                          <input
                            disabled
                            id={`Act_Worker_Name${index + 1}`}
                            value={WorkerForRow.join(", ")}
                            onChange={handleCostInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td className="p-2 border-y border-r border-black bg-[#FFFF99]">
                          {" "}
                          <input
                            id={`Act_CPT${index + 1}`}
                            value={CostData?.[`Act_CPT${index + 1}`] || ""}
                            onChange={handleCostInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />{" "}
                        </td>
                        <td className="p-2 border-y border-l border-black bg-[#CC99FF] relative">
                          <DatePicker
                            id={`Act_CPD${index + 1}`}
                            selected={
                              CostData?.[`Act_CPD${index + 1}`]
                                ? new Date(CostData[`Act_CPD${index + 1}`])
                                    .toISOString()
                                    .split("T")[0]
                                : null
                            }
                            onChange={(date) =>
                              handleCostInputChange(
                                `Act_CPD${index + 1}`,
                                date ? date.toISOString().split("T")[0] : null
                              )
                            }
                            isClearable
                            type="date"
                            dateFormat="dd/MM/yyyy"
                            className="w-full bg-transparent border-none text-md p-1 focus:outline-none"
                          />

                          {/* Icon for when no date is selected */}
                          {!CostData?.[`Act_CPD${index + 1}`] && (
                            <AiTwotoneCalendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none" />
                          )}
                        </td>
                        <td className="p-2  border-y border-r border-black bg-[#FFFF99]">
                          <input
                            id={`Act_CPN${index + 1}`}
                            value={CostData?.[`Act_CPN${index + 1}`] || ""}
                            onChange={handleCostInputChange}
                            className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td
                          className="p-2 relative border-none bg-[#FFFF99]"
                          style={{ height: "38px" }}
                        >
                          <textarea
                            id={`Act_Cs_Remark${index + 1}`}
                            value={
                              CostData?.[`Act_Cs_Remark${index + 1}`] || ""
                            }
                            onChange={handleCostInputChange}
                            className="absolute  bg-[#FFFF99]  border-none text-md p-1 focus:outline-none text-sm w-full resize-y"
                            style={{
                              top: 0,
                              left: 0,
                              width: "100%",
                              minHeight: "105px",
                              maxHeight: "105px",
                            }}
                            rows={2}
                          />
                        </td>
                        <td
                          className="p-2 relative border-none bg-[#FFFFFF]"
                          style={{ height: "38px" }}
                        >
                          <div
                            id={`Act_Seq_No${index + 1}`}
                            className={`w-full text-md p-1 
    ${costStatus[index + 1]?.startsWith("Error") ? "text-red-500" : ""}
    ${costStatus[index + 1] === "Success" ? "text-green-500" : ""}
    ${!costStatus[index + 1] ? "text-gray-700" : ""}
  `}
                          >
                            {CostData?.[`Act_Seq_No${index + 1}`] || "-"}
                          </div>

                          {costStatus[index + 1] && (
                            <p className="text-xs text-gray-600">
                              {costStatus[index + 1]}
                            </p>
                          )}
                        </td>
                      </tr>
                      <tr className="border border-black bg-transparent">
                        <td className="p-2 text-right">
                          <label className="  text-md font-medium">
                            Cs_Progress
                          </label>
                        </td>
                        <td className="p-2 border-y border-r border-black bg-[#CC99FF] ">
                          <Select
                            id={`Act_Cs_Progress_CD${index + 1}`}
                            value={{
                              label:
                                CostData?.[`Act_Cs_Progress_CD${index + 1}`] ||
                                "",
                              value:
                                CostData?.[`Act_Cs_Progress_CD${index + 1}`] ||
                                "",
                            }}
                            options={CsProgress}
                            className="text-xs w-full"
                            onChange={(selectedOption) => {
                              setCostData((prev) => ({
                                ...prev,
                                [`Act_Cs_Progress_CD${index + 1}`]:
                                  selectedOption?.value || "",
                              }));
                            }}
                            styles={{
                              control: (base) => ({
                                ...base,
                                backgroundColor: "transparent", // ตั้งค่าพื้นหลังโปร่ง
                                minHeight: "24px",
                                padding: "0px 4px",
                                fontSize: "16px",
                                border: "none", // ลบเส้นขอบ
                                boxShadow: "none",
                                "&:hover": {
                                  borderColor: "transparent", // ลบเส้นขอบตอน hover
                                },
                              }),
                              menu: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                              option: (base) => ({
                                ...base,
                                fontSize: "14px",
                              }),
                            }}
                          />
                        </td>
                        <td
                          className={`p-2 border-y border-l border-black
                           ${
                             CostData?.[`Act_Cs_Progress_CD${index + 1}`] ===
                             "5"
                               ? "bg-red-500"
                               : CostData?.[
                                   `Act_Cs_Progress_CD${index + 1}`
                                 ] === "4"
                               ? "bg-[#FFFF00]"
                               : CostData?.[
                                   `Act_Cs_Progress_CD${index + 1}`
                                 ] === "3"
                               ? "bg-orange-500"
                               : CostData?.[
                                   `Act_Cs_Progress_CD${index + 1}`
                                 ] === "1"
                               ? "bg-green-500"
                               : "bg-[#FFFF00]"
                           }
                          `}
                        >
                          <input
                            disabled
                            id={`Act_Cs_Progress_Abb${index + 1}`}
                            value={ProgressForRow.join(", ")}
                            onChange={handleCostInputChange}
                            className="w-56  bg-transparent border-none text-md p-1 focus:outline-none"
                          />
                        </td>
                        <td
                          className={`p-2 border-y border-r border-black text-right bg-[#FFFF00]
                         ${
                           CostData?.[`Act_Cs_Progress_CD${index + 1}`] === "5"
                             ? "bg-red-500"
                             : CostData?.[`Act_Cs_Progress_CD${index + 1}`] ===
                               "4"
                             ? "bg-[#FFFF00]"
                             : CostData?.[`Act_Cs_Progress_CD${index + 1}`] ===
                               "3"
                             ? "bg-orange-500"
                             : CostData?.[`Act_Cs_Progress_CD${index + 1}`] ===
                               "1"
                             ? "bg-green-500"
                             : "bg-[#FFFF00]"
                         }
                          `}
                        ></td>

                        <td className="p-2 text-right">
                          <label className="  text-md font-medium">
                            Comp_Date
                          </label>
                        </td>

                        <td className={`p-2 border-y border-r border-black`}>
                          <div className={`relative w-full `}>
                            <DatePicker
                              id={`Act_Cs_Complete_Date${index + 1}`}
                              selected={
                                CostData?.[`Act_Cs_Complete_Date${index + 1}`]
                                  ? new Date(
                                      CostData[
                                        `Act_Cs_Complete_Date${index + 1}`
                                      ]
                                    ) // แปลงค่าเป็นวันที่หากมีค่า
                                  : null
                              }
                              onChange={(date) =>
                                handleCostInputChange(
                                  `Act_Cs_Complete_Date${index + 1}`,
                                  date ? date.toISOString().split("T")[0] : null // แปลงวันที่เป็น ISO-8601 (ไม่รวมเวลา)
                                )
                              }
                              isClearable
                              dateFormat="dd/MM/yyyy"
                              className={`w-full bg-transparent text-md p-2 focus:outline-none`}
                            />

                            {/* ถ้าไม่มีค่าใน `Act_Cs_Complete_Date${index + 1}` จะแสดงไอคอนปฏิทิน */}
                            {!CostData?.[
                              `Act_Cs_Complete_Date${index + 1}`
                            ] && (
                              <AiTwotoneCalendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none" />
                            )}
                          </div>
                        </td>
                        <td className="p-2 text-right">
                          <label className="  text-md font-medium">
                            Comp_Qty
                          </label>
                        </td>
                        <td className="p-2 border-m border-r border-black">
                          {" "}
                          <input
                            disabled={
                              CostData?.[`Cs_Complete_Qtys${index + 1}`]
                            }
                            id={`Act_Cs_Complete_Qty${index + 1}`}
                            value={
                              CostData?.[`Act_Cs_Complete_Qty${index + 1}`]
                            }
                            onChange={handleCostInputChange}
                            type="text"
                            className="w-full text-center  bg-transparent border-none text-md p-1 focus:outline-none"
                          />{" "}
                        </td>

                        <td className="p-2 border-m border-b border-black">
                          <div className="flex items-center">
                            <input
                              disabled={CostData?.[`Outsides${index + 1}`]}
                              id={`Act_Outside${index + 1}`}
                              checked={
                                CostData?.[`Act_Outside${index + 1}`] !==
                                undefined
                                  ? CostData?.[`Act_Outside${index + 1}`]
                                  : false
                              }
                              onChange={handleCostInputChange}
                              type="checkbox"
                              className="w-5 h-5 ml-2"
                            />
                            <label className=" pl-2 text-md font-medium">
                              Outside
                            </label>
                          </div>
                        </td>
                        <td className="p-2 border-m border-b border-black"></td>
                        <td className="p-2 border-m border-b border-black">
                          <div className="flex items-center">
                            <input
                              disabled={
                                CostData?.[`Cs_Final_Completes${index + 1}`]
                              }
                              id={`Act_Cs_Final_Complete${index + 1}`}
                              checked={
                                CostData?.[
                                  `Act_Cs_Final_Complete${index + 1}`
                                ] !== undefined
                                  ? CostData?.[
                                      `Act_Cs_Final_Complete${index + 1}`
                                    ]
                                  : false
                              }
                              onChange={handleCostInputChange}
                              type="checkbox"
                              className="w-5 h-5 ml-2"
                            />
                            <label className="pl-2 text-md font-medium">
                              Final_Comp
                            </label>
                          </div>
                        </td>
                        <td className="p-2 border-m border-b border-black"></td>
                        <td className="p-2 border-m border-b border-black"></td>
                        <td className="p-2 border-m border-b border-black bg-[#FFFFFF]">
                          <div className="flex items-center">
                            <input
                              disabled
                              id={`Act_Cost_No${index + 1}`}
                              value={CostData?.[`Act_Cost_No${index + 1}`]}
                              onChange={handleCostInputChange}
                              type="text"
                              className="w-full  bg-transparent border-none text-md p-1 focus:outline-none"
                            />
                            <input
                              disabled
                              id={`Act_Result${index + 1}`}
                              value={CostData?.[`Act_Result${index + 1}`]}
                              onChange={handleCostInputChange}
                              type="text"
                              className="w-full bg-transparent border-none text-md p-1 focus:outline-none"
                            />
                          </div>
                        </td>
                      </tr>
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <div className="bg-white p-3 mt-5">
          <div className="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 gap-4">
            <div className="grid grid-cols-4 gap-2">
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F1}
                id="F1"
              >
                (F1)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F2}
              >
                (F2)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F3}
              >
                (F3)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F4}
              >
                (F4)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F5}
              >
                (F5)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F6}
              >
                (F6)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F7}
              >
                (F7)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F8}
              >
                (F8)
              </button>
            </div>
            <div className="grid grid-cols-4 gap-2">
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F9}
                onClick={handleF9Click}
              >
                Action
                <br />
                実行(F9)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white disabled:bg-gray-300 disabled:cursor-not-allowed disabled:text-gray-500"
                disabled={buttonState.F10}
              >
                (F10)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-sm text-white"
                id="F11"
                onClick={handleF11Click}
              >
                NextInput <br />
                次へ (F11)
              </button>
              <button
                className="bg-blue-500 p-3 rounded-lg hover:bg-blue-700 font-medium text-white"
                disabled={buttonState.F12}
                onClick={handleF12Click}
                id="F12"
              >
                Exit <br />
                終了 (F12)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
