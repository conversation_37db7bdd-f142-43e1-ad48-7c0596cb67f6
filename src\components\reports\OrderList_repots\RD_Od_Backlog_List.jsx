import React, { useEffect, useRef, useState } from "react";
import html2pdf from "html2pdf.js";
import "../../fonts/CODE39.ttf";

export default function RD_Od_Backlog_List() {
  const apiUrl_5173 = import.meta.env.VITE_FRONTEND_URL;

  const [orderListData, setOrderListData] = useState([]);
  const reportRef = useRef();
  const styles = {
    fontFamily: "CODE39",
  };

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.origin !== `${apiUrl_5173}`) {
        return;
      }
      const { status, data } = event.data;
      if (status === "success") {
        setOrderListData(data);
      } else {
        console.error("Failed to receive data.");
      }
    };

    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  const getCurrentDateTime = () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, "0");
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const year = now.getFullYear();
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const handleViewPDF = async () => {
    const element = reportRef.current;

    const options = {
      filename: `RD_Od_Backlog.pdf`,
      html2canvas: { scale: 2 },
      jsPDF: { unit: "mm", format: "a4", orientation: "landscape" },
    };

    html2pdf()
      .from(element)
      .set(options)
      .outputPdf("blob") // ส่งออกเป็น Blob
      .then((blob) => {
        const url = URL.createObjectURL(blob); // สร้าง Blob URL
        window.open(url, "_blank"); // เปิดในแท็บใหม่
      });
  };

  // ข้อมูลจำลองในรูปแบบ Array
  const rows = [
    {
      progress: "2",
      confirm_delivery: "25/12/2018",
      product_delivery: "24/12/2018",
      request_delivery: "24/12/2018",
      order_no: "SAM1810003",
      customer: "FDC02 FDC02[E]",
      nav_goods_name: "PECH PUNCH",
      material_1_2: "D40",
      item_cd: "37",
      req: "1",
      cat: "0",
      qty: "2",
      unit_price: "0",
      comp_qty: "0",
      rest_qty: "2",
      rest_sum: "0",
      instructions: "2",
    },
  ];

  const summaryRows1 = [
    {
      label: "Calc_Process_Date_Sum:",
      value: 4,
      qty: 0,
      sumTotal: 0,
      total: (148227.422).toFixed(3),
      remark: "",
    },
  ];

  return (
    <div className="flex bg-white h-[100vh] ">
      <div className="flex flex-col w-screen mr-2 ml-2">
        <div className="flex justify-end p-4">
          <button
            onClick={handleViewPDF}
            className="bg-blue-500 text-white px-4 py-2 rounded shadow hover:bg-blue-600"
          >
            View PDF
          </button>
        </div>
        <div
          ref={reportRef}
          className="w-auto"
          style={{ ...styles, maxWidth: "100%" }}
        >
          <div className="bg-white py-2">
            <div className="w-full overflow-x-auto">
              <div className="flex items-center justify-between p-1 w-full">
                <div className="flex items-start">
                  <span className="font-bold text-blue-900 w-28 text-xs pl-5 pt-1">
                    Product_Grp:
                  </span>

                  <div
                    className="px-[10px] py-[2px] border border-black text-xs w-[100px] h-6 text-center"
                    style={{ transform: "translateY(7px)" }}
                  >
                    <span style={{ transform: "translateY(-8px)" }}>
                      FTC-P1
                    </span>
                  </div>
                  <div
                    className="px-[10px] py-[2px] border border-black text-xs w-[100px] h-6 text-center"
                    style={{ transform: "translateY(7px)" }}
                  >
                    <span style={{ transform: "translateY(-8px)" }}>
                      FTC-PDS
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-auto flex-shrink-0">
                  <span className="font-bold text-blue-900 text-lg">
                    Order Backlog List
                  </span>
                </div>

                <div className="flex items-center space-x-2 ml-auto mr-2 flex-shrink-0">
                  <span className="font-bold text-blue-900 text-xs">
                    Create_Date:
                  </span>
                  <span className="text-xs">{getCurrentDateTime()}</span>
                  <span className="font-bold text-blue-900 text-xs">Page:</span>
                  <span className="text-xs">1/1</span>
                </div>
              </div>

              <div className="flex items-start ml-1 mt-1">
                <span
                  className="font-bold text-blue-900 w-28 text-xs pl-8 pt-1"
                  style={{ transform: "translateY(-7px)" }}
                >
                  Ctl_Person:
                </span>
                <div className="px-[10px] py-[2px] border border-black text-xs w-[100px] h-6 text-center">
                  <span></span>
                </div>
                <div className="px-[10px] py-[2px] border border-black text-xs w-[100px] h-6 text-center">
                  <span></span>
                </div>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto max-h-[100vh] w-full">
            <table className="table-auto min-w-full bg-white border-2 border-blue-800 text-xs">
              <thead className=" bg-white">
                <tr>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Progress
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Confirm Delivery
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Product Delivery
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Request Delivery
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Order_No
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Customer
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      NAV_Goods_Name
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Material 1/2
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Item <br />
                      CD
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Req <br />
                      CAT
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>Qty</span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Unit <br />
                      Price
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Comp <br />
                      Qty
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Rest <br />
                      Qty
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Rest <br />
                      Sum
                    </span>
                  </th>
                  <th
                    className="px-1 py-2 border border-blue-800 border-dashed text-xs w-auto"
                    rowSpan="2"
                  >
                    <span style={{ transform: "translateY(-7px)" }}>
                      Instructions/Remark
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {orderListData.map((row, index) => (
                  <tr key={index} className="text-xs text-center">
                    <td className="p-1 border border-blue-800 border-dashed"></td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Confirm_Delivery}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Product_Delivery}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Request_Delivery}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Order_No}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Customer_CD}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed"></td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      <div className="flex flex-col">
                        <span>{row.Material1}</span>
                        <span>{row.Material1}</span>
                      </div>
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Item1_CD}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      <div className="flex justify-between w-full">
                        <span className="text-left">
                          {row.Request1_CD} {row.Request2_CD} {row.Request3_CD}
                        </span>
                        <span className="text-right">{row.cat}</span>
                      </div>
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Quantity}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Price_CD === 1 ? "*" : null} {row.Unit_Price}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Comp_Qty}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Rest_Qty}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Rest_Sum}
                    </td>
                    <td className="p-1 border border-blue-800 border-dashed">
                      {row.Pd_Instructions || row.Pd_Remark
                        ? `${row.Pd_Instructions || ""}/${row.Pd_Remark || ""}`
                        : ""}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tbody className="bg-[#cffff9] font-bold text-[12px]">
                {summaryRows1.map((item, index) => (
                  <tr key={index}>
                    <td colSpan="10" className="py-1 text-right text-[#000080]">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.label}
                      </span>
                    </td>
                    <td className="pr-0.5 text-right">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.value}
                      </span>
                    </td>
                    <td colSpan="2" className="pr-0.5 text-right">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.qty}
                      </span>
                    </td>
                    <td className="pr-0.5 text-right">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.sumTotal}
                      </span>
                    </td>
                    <td className="pr-0.5 text-center">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.total}
                      </span>
                    </td>
                    <td className="pr-0.5 text-right">
                      <span style={{ transform: "translateY(-7px)" }}>
                        {item.remark}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
